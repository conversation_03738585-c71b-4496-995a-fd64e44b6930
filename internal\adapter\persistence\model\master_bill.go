package model

import (
	"time"
	"zebra-hub-system/internal/domain/entity"
)

// MasterBillPO 主提单持久化对象
type MasterBillPO struct {
	ID               int64      `gorm:"column:id;primaryKey;autoIncrement"`
	MasterBillNumber string     `gorm:"column:master_bill_number;comment:提单号/航班号"`
	DepartureDate    *time.Time `gorm:"column:departure_date;comment:起飞日期"`
	ArrivalDate      *time.Time `gorm:"column:arrival_date;comment:到达日期"`
	Origin           string     `gorm:"column:origin;comment:始发地"`
	Destination      string     `gorm:"column:destination;comment:目的地"`
	CarrierCode      string     `gorm:"column:carrier_code;comment:承运商代码"`
	Status           int        `gorm:"column:status;comment:提单状态"`
	TotalWeight      float64    `gorm:"column:total_weight;comment:总重量"`
	TotalVolume      float64    `gorm:"column:total_volume;comment:总体积"`
	WaybillCount     int        `gorm:"column:waybill_count;comment:运单数量"`
	Remark           string     `gorm:"column:remark;comment:备注"`
	CreateTime       time.Time  `gorm:"column:create_time;comment:创建时间"`
	UpdateTime       time.Time  `gorm:"column:update_time;comment:更新时间"`
	CreatorID        int64      `gorm:"column:creator_id;comment:创建者ID"`
	IsDeleted        int        `gorm:"column:is_deleted;comment:是否删除"`
}

// TableName 表名
func (MasterBillPO) TableName() string {
	return "tb_master_bill"
}

// ToEntity 转换为实体
func (p *MasterBillPO) ToEntity() *entity.MasterBill {
	if p == nil {
		return nil
	}
	return &entity.MasterBill{
		ID:               p.ID,
		MasterBillNumber: p.MasterBillNumber,
		DepartureDate:    p.DepartureDate,
		ArrivalDate:      p.ArrivalDate,
		Origin:           p.Origin,
		Destination:      p.Destination,
		CarrierCode:      p.CarrierCode,
		Status:           p.Status,
		TotalWeight:      p.TotalWeight,
		TotalVolume:      p.TotalVolume,
		WaybillCount:     p.WaybillCount,
		Remark:           p.Remark,
		CreateTime:       p.CreateTime,
		UpdateTime:       p.UpdateTime,
		CreatorID:        p.CreatorID,
		IsDeleted:        p.IsDeleted,
	}
} 