# 斑马物巢系统 API 对接文档

## 概述

本文档提供斑马物巢系统 API 对接指南，包含认证、订单创建、面单生成等核心功能接口。

## 服务器信息

- **基础 URL**: `http://121.36.57.183:8080/` (生产环境)
- **API 版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用说明

### 响应格式

所有 API 接口统一返回以下格式：

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:30:00Z",
  "data": {
    // 具体业务数据
  }
}
```

### 错误码说明

| 错误码 | 说明                 | HTTP 状态码 |
| ------ | -------------------- | ----------- |
| 100000 | 操作成功             | 200         |
| 100001 | 未知错误             | 500         |
| 100002 | 无效参数             | 400         |
| 100004 | 未授权               | 401         |
| 101001 | 用户不存在           | 404         |
| 101002 | 用户名或密码错误     | 401         |
| 101005 | Token 无效或已过期   | 401         |
| 201001 | 运单不存在           | 404         |
| 201005 | 收件人地址信息无效   | 400         |
| 202001 | 运单号渠道不存在     | 400         |
| 202005 | 所选渠道可用单号不足 | 409         |

---

## 接口 1：用户登录

### 基本信息

- **接口路径**: `POST /api/v1/login`
- **功能描述**: 用户认证，获取访问令牌
- **认证要求**: 无（公开接口）

### 请求参数

#### 请求体 (JSON)

```json
{
  "username": "admin",
  "password": "123456"
}
```

#### 参数说明

| 字段名   | 类型   | 必填 | 说明           |
| -------- | ------ | ---- | -------------- |
| username | string | 是   | 用户名或手机号 |
| password | string | 是   | 用户密码       |

### 响应参数

#### 成功响应 (200)

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "status": 1,
      "roleId": 1
    }
  }
}
```

#### 失败响应

```json
{
  "success": false,
  "errorCode": 101002,
  "errorMessage": "用户名或密码错误",
  "data": null
}
```

### 调用示例

#### cURL

```bash
curl -X POST "https://api.zebra-hub.com/api/v1/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "123456"
  }'
```

---

## 接口 2：创建订单

### 基本信息

- **接口路径**: `POST /api/v1/external/orders`
- **功能描述**: 创建订单接口，支持订单号重复时的更新操作
- **认证要求**: Bearer Token（通过登录接口获取）

### 请求参数

#### 请求头

```
Authorization: Bearer {token}
Content-Type: application/json
```

#### 请求体 (JSON)

```json
{
  "merchantOrderNumber": "MERCHANT_ORDER_20241217001",
  "shipmentTypeId": 1,
  "receiverName": "田中太郎",
  "receiverPhone": "090-1234-5678",
  "receiverZipCode": "100-0001",
  "receiverAddress": "東京都千代田区千代田1-1",
  "items": [
    {
      "name": "手机壳",
      "quantity": 2
    },
    {
      "name": "数据线",
      "quantity": 1
    }
  ]
}
```

#### 参数说明

| 字段名              | 类型   | 必填 | 说明                                                     |
| ------------------- | ------ | ---- | -------------------------------------------------------- |
| merchantOrderNumber | string | 是   | 商家订单号（支持重复提交更新）                           |
| shipmentTypeId      | int64  | 是   | 货物类型 ID<br/>1-普通货物<br/>2-带电货物<br/>3-投函货物 |
| receiverName        | string | 是   | 收件人姓名                                               |
| receiverPhone       | string | 是   | 收件人电话（日本手机号格式）                             |
| receiverZipCode     | string | 是   | 收件人邮编（日本邮编格式，7 位数字或 XXX-XXXX）          |
| receiverAddress     | string | 是   | 收件人地址                                               |
| items               | array  | 是   | 物品列表                                                 |
| items[].name        | string | 是   | 物品名称                                                 |
| items[].quantity    | int    | 是   | 物品数量（最小值：1）                                    |

### 响应参数

#### 成功响应 (200)

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "trackingNumber": "JP123456789",
    "merchantOrderNumber": "MERCHANT_ORDER_20241217001",
    "systemOrderNo": "BM1217123456"
  }
}
```

#### 响应字段说明

| 字段名              | 类型   | 说明                   |
| ------------------- | ------ | ---------------------- |
| trackingNumber      | string | 系统自动分配的运单号   |
| merchantOrderNumber | string | 商家订单号（原样返回） |
| systemOrderNo       | string | 系统生成的订单号       |

#### 失败响应示例

```json
{
  "success": false,
  "errorCode": 201005,
  "errorMessage": "电话号码格式无效",
  "data": null
}
```

### 业务逻辑说明

- 如果商家订单号已存在且运单状态为**预报(1)**，则执行更新操作
- 如果运单状态为其他状态，则返回错误，不允许更新
- 更新时保持原有运单号不变

### 调用示例

#### cURL

```bash
curl -X POST "https://api.zebra-hub.com/api/v1/external/orders" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "merchantOrderNumber": "ORDER_20241217_001",
    "shipmentTypeId": 1,
    "receiverName": "田中花子",
    "receiverPhone": "080-9876-5432",
    "receiverZipCode": "900-0001",
    "receiverAddress": "沖縄県那覇市港町1-1",
    "items": [
      {
        "name": "电子产品",
        "quantity": 1
      }
    ]
  }'
```

---

## 接口 3：生成 PDF 面单

### 基本信息

- **接口路径**: `POST /api/v1/manifests/labels`
- **功能描述**: 根据运单号批量生成 PDF 面单文件
- **认证要求**: Bearer Token（通过登录接口获取）

### 请求参数

#### 请求头

```
Authorization: Bearer {token}
```

#### 请求体 (JSON)

```json
{
  "trackingNumbers": ["JP123456789", "JP987654321", "JP555666777"]
}
```

#### 参数说明

| 字段名          | 类型  | 必填 | 说明                    |
| --------------- | ----- | ---- | ----------------------- |
| trackingNumbers | array | 是   | 运单号列表（最少 1 个） |

### 响应参数

#### 成功响应 (200)

- **Content-Type**: `application/pdf`
- **Content-Disposition**: `attachment; filename="labels.pdf"`
- **响应体**: PDF 文件的二进制数据

#### 失败响应 (400)

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "未找到任何有效的运单信息",
  "data": null
}
```

### 面单内容说明

生成的 PDF 面单包含以下信息：

- **条形码**: 运单号的 Code128 条形码
- **运单号**: 条形码下方的文本
- **收件人信息**:
  - 姓名 (名前)
  - 地址 (住所)
  - 电话号码 (電話番号)
  - 邮编 (郵便番号)
- **物品信息**:
  - 品名 (品名)
  - 数量 (数量)
- **页脚信息**:
  - 印刷时间 (印刷時間)
  - 用户名

### 调用示例

#### cURL

```bash
curl -X POST "https://api.zebra-hub.com/api/v1/manifests/labels" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "trackingNumbers": [
      "JP123456789",
      "JP987654321"
    ]
  }' \
  --output labels.pdf
```

