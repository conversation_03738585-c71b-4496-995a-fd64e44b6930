#!/bin/bash

# 账单生成消费者启动脚本

# 设置工作目录
cd "$(dirname "$0")/.."

# 环境变量设置
export GO_ENV=${GO_ENV:-production}

# 日志目录
LOG_DIR="./logs"
mkdir -p $LOG_DIR

# 进程文件
PID_FILE="$LOG_DIR/billing-consumer.pid"
LOG_FILE="$LOG_DIR/billing-consumer.log"

# 函数：检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    else
        return 1
    fi
}

# 函数：启动服务
start() {
    if is_running; then
        echo "账单生成消费者已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi

    echo "正在启动账单生成消费者..."
    
    # 构建程序
    go build -o ./bin/billing-consumer ./cmd/billing-consumer/
    
    if [ $? -ne 0 ]; then
        echo "构建失败！"
        return 1
    fi

    # 启动程序
    nohup ./bin/billing-consumer > "$LOG_FILE" 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > "$PID_FILE"
    
    # 等待一秒检查是否启动成功
    sleep 1
    if is_running; then
        echo "账单生成消费者启动成功 (PID: $PID)"
        echo "日志文件: $LOG_FILE"
        return 0
    else
        echo "账单生成消费者启动失败！"
        return 1
    fi
}

# 函数：停止服务
stop() {
    if ! is_running; then
        echo "账单生成消费者未运行"
        return 1
    fi

    PID=$(cat "$PID_FILE")
    echo "正在停止账单生成消费者 (PID: $PID)..."
    
    # 发送SIGTERM信号
    kill $PID
    
    # 等待进程结束
    for i in {1..30}; do
        if ! ps -p $PID > /dev/null 2>&1; then
            rm -f "$PID_FILE"
            echo "账单生成消费者已停止"
            return 0
        fi
        sleep 1
    done
    
    # 如果还没停止，强制杀死
    echo "强制停止账单生成消费者..."
    kill -9 $PID 2>/dev/null
    rm -f "$PID_FILE"
    echo "账单生成消费者已强制停止"
}

# 函数：重启服务
restart() {
    stop
    sleep 2
    start
}

# 函数：显示状态
status() {
    if is_running; then
        PID=$(cat "$PID_FILE")
        echo "账单生成消费者正在运行 (PID: $PID)"
        
        # 显示进程信息
        ps -p $PID -o pid,ppid,cmd --no-headers
        
        # 显示最近的日志
        echo ""
        echo "最近的日志 (最后10行):"
        echo "========================"
        tail -n 10 "$LOG_FILE" 2>/dev/null || echo "无法读取日志文件"
    else
        echo "账单生成消费者未运行"
    fi
}

# 函数：查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        if [ "$1" = "-f" ]; then
            echo "实时查看日志 (Ctrl+C 退出):"
            echo "==========================="
            tail -f "$LOG_FILE"
        else
            echo "显示日志文件内容:"
            echo "=================="
            cat "$LOG_FILE"
        fi
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 主逻辑
case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    logs)
        logs "$2"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs [-f]}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动账单生成消费者"
        echo "  stop    - 停止账单生成消费者"
        echo "  restart - 重启账单生成消费者"
        echo "  status  - 查看运行状态"
        echo "  logs    - 查看日志"
        echo "  logs -f - 实时查看日志"
        exit 1
        ;;
esac

exit $? 