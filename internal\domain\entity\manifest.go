package entity

import (
	"time"
	"zebra-hub-system/internal/util"
)

// Manifest 运单实体
type Manifest struct {
	ID                        int64                     `json:"id"`
	ExpressNumber             string                    `json:"expressNumber"`
	SawagaNumber              string                    `json:"sawagaNumber"`
	OrderNumber               string                    `json:"orderNumber"`
	TransferredTrackingNumber string                    `json:"transferredTrackingNumber"`
	OrderNo                   string                    `json:"orderNo"`
	Weight                    float64                   `json:"weight"`
	Length                    float64                   `json:"length"`
	Width                     float64                   `json:"width"`
	Height                    float64                   `json:"height"`
	DimensionalWeight         float64                   `json:"dimensionalWeight"`
	Cost                      float64                   `json:"cost"`
	OverLengthSurcharge       float64                   `json:"overLengthSurcharge"`
	RemoteAreaSurcharge       float64                   `json:"remoteAreaSurcharge"`
	OtherCostName             string                    `json:"otherCostName"`
	OtherCost                 float64                   `json:"otherCost"`
	FeeTotal                  float64                   `json:"feeTotal"`
	Value                     float64                   `json:"value"`
	Description               string                    `json:"description"`
	ReceiverPhone             string                    `json:"receiverPhone"`
	ReceiverZipCode           string                    `json:"receiverZipCode"`
	ReceiverAddress           string                    `json:"receiverAddress"`
	ReceiverName              string                    `json:"receiverName"`
	ReceiverEnName            string                    `json:"receiverEnName"`
	ReceiverEnAddress         string                    `json:"receiverEnAddress"`
	PrefectureName            string                    `json:"prefectureName"`
	MunicipalName             string                    `json:"municipalName"`
	LocalitiesName            string                    `json:"localitiesName"`
	PrefectureEnName          string                    `json:"prefectureEnName"`
	MunicipalEnName           string                    `json:"municipalEnName"`
	LocalitiesEnName          string                    `json:"localitiesEnName"`
	PackageNo                 string                    `json:"packageNo"`
	SawagaSiteCode            string                    `json:"sawagaSiteCode"`
	Remark                    string                    `json:"remark"`
	UserID                    int64                     `json:"userId"`
	Status                    int                       `json:"status"`
	TrackingStatus            int                       `json:"trackingStatus"`
	IsOnline                  bool                      `json:"isOnline"`
	LogisticsJourney          string                    `json:"logisticsJourney"`
	TrackingUpdateTime        util.PointerFormattedTime `json:"trackingUpdateTime"` // 物流更新时间
	SourceType                int                       `json:"sourceType"`
	CarrierCode               string                    `json:"carrierCode"`
	DestinationCode           string                    `json:"destinationCode"`
	ShippingFeeTemplateType   int                       `json:"shippingFeeTemplateType"` // 运费模板类型
	CreatorID                 int64                     `json:"creatorId"`
	PickUpBy                  int64                     `json:"pickUpBy"`
	IsDelete                  bool                      `json:"isDelete"`
	MasterBillID              int64                     `json:"masterBillId"`
	MasterBillNumber          string                    `json:"masterBillNumber"`
	PreRegistrationBatchID    string                    `json:"preRegistrationBatchId"` // 预报批次ID
	PickUpTime                util.PointerFormattedTime `json:"pickUpTime"`             // 揽件时间
	ShipmentTime              util.PointerFormattedTime `json:"shipmentTime"`           // 发货时间
	DeliveredTime             util.PointerFormattedTime `json:"deliveredTime"`          // 收件时间
	CreateTime                time.Time                 `json:"createTime"`
	UpdateTime                time.Time                 `json:"updateTime"`
	PackingStatus             int                       `json:"packingStatus"`
	ParcelSortingRecordID     int64                     `json:"parcelSortingRecordId"`
	ValidationStatus          int                       `json:"validationStatus"`
	ValidationError           string                    `json:"validationError"`

	Items []ManifestItem `json:"items,omitempty"`
}

// TableName 指定表名
func (Manifest) TableName() string {
	return "tb_manifest"
}
