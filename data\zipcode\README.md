# 日本邮编数据

## 文件说明

- `japan_zipcode.csv`: 日本邮编数据文件，Shift-JIS 编码

## 文件格式

CSV 文件格式为：邮编,都道府县(日),市区町村(日),町域(日),都道府県(英),市区町村(英),町域(英)

- 第 1 列：邮政编码，格式为纯 7 位数字（XXXXXXX），例如"1000001"
- 第 2 列：都道府县名（日文）
- 第 3 列：市区町村名（日文）
- 第 4 列：町域名（日文）
- 第 5 列：都道府县名（英文）
- 第 6 列：市区町村名（英文）
- 第 7 列：町域名（英文）

注意：文件使用 Shift-JIS 编码，字段可能包含双引号包围

## 数据处理说明

- **存储格式**：数据库中只存储邮编的数字部分，例如"1000001"
- **校验逻辑**：
  1. 如果输入格式为纯 7 位数字（XXXXXXX），直接校验
  2. 如果输入格式为 XXX-XXXX，提取数字部分后校验
  3. 其他情况，提取所有数字，验证是否为 7 位
  4. 与预加载的邮编数据比对
- **展示格式**：接口返回时会将数字重新格式化为"XXX-XXXX"的展示格式

## 数据获取

可以从日本邮政官方网站获取最新的邮编数据：

- [日本邮政官方下载页面](https://www.post.japanpost.jp/zipcode/download.html)

## 使用方法

1. 将下载的 CSV 文件放在本目录下
2. 确保文件名与配置文件中设置的名称一致 (`japan_zipcode.csv`)
3. 保持文件的 Shift-JIS 编码不变
4. 确保邮编列为纯 7 位数字格式（如有连字符，请先处理）

## 配置

配置文件 `configs/config.yaml` 中可以修改文件路径：

```yaml
zipcode:
  japan:
    filepath: "data/zipcode/japan_zipcode.csv"
```
