# 财务模块 - 根据账单记录 ID 导出 Excel API 文档

## 概述

本接口用于根据账单记录 ID 查询对应的账单明细和财务调整快照，生成包含完整账单信息的 Excel 文件供下载。

## 接口信息

- **接口路径**: `POST /api/v1/finance/billing-record/export`
- **请求方式**: POST
- **Content-Type**: application/json
- **响应类型**: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet (Excel 文件)
- **认证要求**: 需要 JWT 认证

## 请求参数

### 请求体 (JSON)

```json
{
  "billingRecordId": 123
}
```

| 参数名          | 类型  | 必填 | 说明                    | 示例 |
| --------------- | ----- | ---- | ----------------------- | ---- |
| billingRecordId | int64 | 是   | 账单记录 ID，必须大于 0 | 123  |

## 响应说明

### 成功响应 (HTTP 200)

- **Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Content-Disposition**: `attachment; filename=客户昵称_账单_账单编号_账单日期.xlsx`
- **响应体**: Excel 文件的二进制数据

### 错误响应

#### 参数错误 (HTTP 400)

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "参数错误: billingRecordId必须大于0",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": null
}
```

#### 账单记录不存在 (HTTP 404)

```json
{
  "success": false,
  "errorCode": 100006,
  "errorMessage": "生成账单Excel失败: 账单记录不存在",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": null
}
```

#### 服务器内部错误 (HTTP 500)

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "生成账单Excel失败: 系统内部错误",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": null
}
```

## Excel 文件结构

生成的 Excel 文件包含以下内容：

### 1. 文件信息

- **工作表名称**: "账单明细"
- **文件名格式**: `{客户昵称}_账单_{账单编号}_{账单日期}.xlsx`

### 2. 表头信息

- **公司标题**: 斑马物巢物流有限公司 - {客户昵称}账单明细
- **账单信息**: 账单编号、账期、总金额、状态等
- **模板信息**: 使用的运费模板详情（普通货物、带电货物、投函货物）

### 3. 运单明细部分

包含以下列：

- 序号、快递单号、系统单号、转单号
- 发货时间、预报时间、收件人、物品名称、货物类型
- 重量(kg)、长(cm)、宽(cm)、高(cm)、三边和(cm)、体积重(kg)、计费重量(kg)
- 首重费用(元)、续重费用(元)、超长费(元)、偏远费(元)、其他费用(元)、总费用(元)

### 4. 财务调整明细部分

包含以下列：

- 序号、调整类型、调整描述、调整金额(元)、货币单位、生效日期
- 关联快递单号、关联系统单号、关联收件人、创建时间

### 5. 汇总信息

- 运单明细小计
- 财务调整小计
- 账单总计
- 报表生成时间

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8080/api/v1/finance/billing-record/export" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "billingRecordId": 123
  }' \
  --output "账单明细.xlsx"
```

### JavaScript 示例

```javascript
const exportBillingRecord = async (billingRecordId) => {
  try {
    const response = await fetch("/api/v1/finance/billing-record/export", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        billingRecordId: billingRecordId,
      }),
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download =
        response.headers.get("Content-Disposition").split("filename=")[1] ||
        "账单明细.xlsx";
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } else {
      const error = await response.json();
      console.error("导出失败:", error.errorMessage);
    }
  } catch (error) {
    console.error("请求失败:", error);
  }
};

// 使用示例
exportBillingRecord(123);
```

## 业务逻辑说明

### 1. 数据获取流程

1. 根据账单记录 ID 获取账单记录详情
2. 分页获取所有账单明细数据（billing_record_items）
3. 分页获取所有财务调整快照数据（billing_financial_adjustment_snapshots）
4. 从账单记录中提取运费模板信息

### 2. 数据转换

- 将账单明细转换为运费账单项格式
- 将财务调整快照转换为调整项格式
- 处理时间字段的格式化
- 安全处理指针字段的空值

### 3. Excel 生成特性

- 保持与原有运费账单报表相同的样式
- 在运单明细后追加财务调整明细
- 包含完整的样式设置（边框、颜色、字体等）
- 支持中文文件名的 URL 编码

## 注意事项

1. **权限控制**: 需要有效的 JWT token
2. **数据量限制**: 系统会分页获取所有数据，无数量限制
3. **文件编码**: 文件名支持中文，会自动进行 URL 编码
4. **错误处理**: 完整的错误码映射和 HTTP 状态码处理
5. **内存管理**: Excel 文件在内存中生成，大数据量时需注意内存使用

## 相关接口

- [分页查询账单记录](./财务模块_分页查询账单记录_API文档.md)
- [获取账单记录详情](./财务模块_获取账单记录详情_API文档.md)
- [查询账单明细列表](./财务模块_根据账单记录ID查询账单明细列表_API文档.md)
- [查询调整明细列表](./财务模块_根据账单记录ID分页查询调整明细_API文档.md)

## 更新日志

- **2024-01-15**: 初始版本，支持根据账单记录 ID 导出完整的 Excel 账单报表
