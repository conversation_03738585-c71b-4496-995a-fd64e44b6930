# 财务模块新增接口总结

## 🎯 **新增接口功能**

本次为财务模块新增了一个重要的查询接口：**查询可生成账单用户列表**。

### **接口概述**

- **接口路径**: `GET /api/v1/finance/billing/users`
- **功能**: 根据时间范围查询在指定时间段内有可生成账单运单或财务调整记录的用户列表
- **用途**: 为账单生成、财务结算、业务统计等场景提供数据支持

## 🔍 **核心功能特性**

### **1. 统一时间维度查询**

- 固定使用**发货时间**（SHIPMENT_TIME）查询，确保数据一致性
- 自动查询满足条件的运单记录（status>=3, is_delete=0）
- 自动查询有效的财务调整记录（effective_date 范围内, is_void=0）

### **2. 智能数据合并**

- 分别统计用户的运单数量和财务调整数量
- 智能合并同一用户的不同类型数据
- 按总数量（运单+调整记录）倒序排列

### **3. 丰富的响应信息**

- 用户基本信息（ID、用户名、昵称）
- 详细统计数据（运单数量、调整数量、总数量）
- 标识字段（是否有运单、是否有调整记录）

## 🏗️ **技术架构实现**

严格按照 DDD（领域驱动设计）架构实现：

### **领域层 (Domain Layer)**

- `entity/billing_user_stats.go` - 用户统计数据实体
- `repository/billing_repository.go` - 新增统计查询接口

### **应用层 (Application Layer)**

- `service/billing_service.go` - 新增业务逻辑实现
  - 请求参数验证
  - 数据查询协调
  - 结果合并与排序

### **基础设施层 (Infrastructure Layer)**

- `persistence/billing_repository_impl.go` - 数据库查询实现
  - 运单用户统计查询
  - 财务调整用户统计查询

### **接口层 (Interface Layer)**

- `handler/finance_handler.go` - HTTP 接口处理
- `router/router.go` - 路由配置

## 📊 **数据库查询优化**

### **运单统计查询**

```sql
SELECT
    m.user_id,
    u.username,
    u.nickname,
    COUNT(*) as manifest_count
FROM tb_manifest m
JOIN tb_user u ON m.user_id = u.id
WHERE m.status >= 3
    AND m.is_delete = 0
    AND m.{timeField} BETWEEN ? AND ?
GROUP BY m.user_id, u.username, u.nickname
ORDER BY manifest_count DESC
```

### **财务调整统计查询**

```sql
SELECT
    adj.customer_account_id,
    u.username,
    u.nickname,
    COUNT(*) as adjustment_count
FROM manifest_financial_adjustments adj
JOIN tb_user u ON adj.customer_account_id = u.id
WHERE adj.effective_date BETWEEN ? AND ?
    AND adj.is_void = 0
GROUP BY adj.customer_account_id, u.username, u.nickname
ORDER BY adjustment_count DESC
```

## 🔄 **业务流程**

1. **参数验证** - 验证时间类型、时间格式、时间范围
2. **并行查询** - 同时查询运单统计和调整记录统计
3. **数据合并** - 按用户 ID 合并两种统计数据
4. **结果排序** - 按总数量倒序排列
5. **响应返回** - 返回标准化的 API 响应

## 🎨 **接口特色**

### **灵活的时间维度**

- 预报时间：适用于预报统计和计划管理
- 发货时间：适用于实际业务统计和结算

### **完整的统计信息**

- 运单数量：可生成账单的运单数
- 调整数量：财务调整记录数
- 标识字段：快速识别用户业务类型

### **高性能设计**

- 使用聚合查询减少数据传输
- JOIN 查询一次性获取用户信息
- 内存中合并避免复杂的 SQL

## 📝 **使用场景**

### **财务结算**

```bash
# 查询11月份有业务发生的用户（固定使用发货时间）
GET /api/v1/finance/billing/users?startTime=2024-11-01 00:00:00&endTime=2024-11-30 23:59:59
```

### **账单生成准备**

```bash
# 查询需要生成账单的用户（固定使用发货时间）
GET /api/v1/finance/billing/users?startTime=2024-12-01 00:00:00&endTime=2024-12-31 23:59:59
```

### **业务统计分析**

- 用户活跃度分析
- 业务量分布统计
- 异常用户识别

## ✅ **质量保证**

### **代码规范**

- 遵循 Go 语言编程规范
- 使用中文注释
- 按业务模块合理拆分

### **错误处理**

- 完整的参数验证
- 友好的错误提示
- 标准化的错误码

### **文档完善**

- 详细的 API 文档
- 完整的使用示例
- 业务场景说明

## 🚀 **部署状态**

- ✅ 代码实现完成
- ✅ 编译测试通过
- ✅ 路由配置完成
- ✅ API 文档编写完成
- ✅ 服务启动成功

## 📚 **相关文档**

- [查询可生成账单用户列表 API 文档](./财务模块_查询可生成账单用户列表_API文档.md)
- [分页查询账单记录 API 文档](./财务模块_分页查询账单记录_API文档.md)

---

**接口已成功开发完成，可以投入使用！** 🎉
