# 查询可生成账单用户列表 API 文档

## 接口概述

该接口用于根据时间范围（固定使用发货时间）查询在指定时间段内有可生成账单的运单（tb_manifest）或财务调整记录的用户列表，包含详细的统计信息。

## 接口信息

- **请求方法**: GET
- **请求路径**: `/api/v1/finance/billing/users`
- **Content-Type**: `application/json`
- **需要认证**: 是

## 请求参数

### 查询参数 (Query Parameters)

| 参数名    | 类型   | 必填 | 说明                                |
| --------- | ------ | ---- | ----------------------------------- |
| startTime | string | 是   | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime   | string | 是   | 结束时间，格式：yyyy-MM-dd HH:mm:ss |

## 业务逻辑说明

### 查询条件

1. **运单查询条件**：

   - 状态：`status >= 3`（可生成账单的运单）
   - 删除标识：`is_delete = 0`（未删除）
   - 时间字段：固定使用 `shipment_time`（发货时间）
   - 时间范围：`startTime` 到 `endTime`

2. **财务调整查询条件**：
   - 生效日期：`effective_date` 在指定时间范围内
   - 作废标识：`is_void = 0`（未作废）

### 数据合并规则

- 系统会分别查询有运单记录和有财务调整记录的用户
- 如果用户既有运单又有调整记录，会合并统计数据
- 结果按总数量（运单数量 + 调整记录数量）倒序排列

## 响应结果

### 成功响应

**HTTP 状态码**: 200

**响应体结构**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:35:00Z",
  "data": {
    "total": 25,
    "list": [
      {
        "userId": 1001,
        "nickname": "张三",
        "username": "zhangsan",
        "manifestCount": 150,
        "adjustmentCount": 5,
        "totalCount": 155,
        "hasManifests": true,
        "hasAdjustments": true
      },
      {
        "userId": 1002,
        "nickname": "李四",
        "username": "lisi",
        "manifestCount": 120,
        "adjustmentCount": 0,
        "totalCount": 120,
        "hasManifests": true,
        "hasAdjustments": false
      },
      {
        "userId": 1003,
        "nickname": "王五",
        "username": "wangwu",
        "manifestCount": 0,
        "adjustmentCount": 8,
        "totalCount": 8,
        "hasManifests": false,
        "hasAdjustments": true
      }
    ]
  }
}
```

### 错误响应

**HTTP 状态码**: 400/500

**响应体结构**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "查询参数绑定错误: 时间类型必须是 CREATE_TIME 或 SHIPMENT_TIME",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:30:00Z",
  "data": null
}
```

## 使用示例

### 示例 1: 查询 11 月份有业务发生的用户

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/users?startTime=2024-11-01%2000:00:00&endTime=2024-11-30%2023:59:59" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 2: 查询 12 月份有业务发生的用户

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/users?startTime=2024-12-01%2000:00:00&endTime=2024-12-31%2023:59:59" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 3: 查询特定日期范围

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/users?startTime=2024-12-01%2009:00:00&endTime=2024-12-01%2018:00:00" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## 响应字段说明

### GetUsersForBillingResponse 字段说明

| 字段名 | 类型  | 说明     |
| ------ | ----- | -------- |
| total  | int64 | 总用户数 |
| list   | array | 用户列表 |

### BillingUserDTO 字段说明

| 字段名          | 类型   | 说明                              |
| --------------- | ------ | --------------------------------- |
| userId          | int64  | 用户 ID                           |
| nickname        | string | 用户昵称                          |
| username        | string | 用户名                            |
| manifestCount   | int64  | 可生成账单的运单数量              |
| adjustmentCount | int64  | 财务调整记录数量                  |
| totalCount      | int64  | 总数量（运单数量 + 调整记录数量） |
| hasManifests    | bool   | 是否有运单记录                    |
| hasAdjustments  | bool   | 是否有调整记录                    |

## 时间类型说明

系统固定使用发货时间（SHIPMENT_TIME）进行查询，按运单的发货时间字段 `shipment_time` 进行过滤。

## 错误码说明

| 错误码 | 说明              |
| ------ | ----------------- |
| 100000 | 操作成功          |
| 100002 | 无效参数          |
| 100003 | 缺少必要参数      |
| 100004 | 未授权/未登录     |
| 100006 | 请求的资源未找到  |
| 100001 | 未知错误/系统繁忙 |

## 注意事项

1. **时间格式**: 所有时间参数必须使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **时间范围**: 结束时间必须晚于开始时间
3. **认证**: 需要在请求头中包含有效的 Bearer Token
4. **排序**: 结果按总数量（运单数量 + 调整记录数量）倒序排列
5. **性能**: 建议合理设置时间范围，避免查询过大的时间跨度
6. **数据完整性**: 如果用户信息查询失败，对应字段可能为空但不影响统计数据

## 业务场景

### 适用场景

1. **财务结算**: 财务人员需要了解哪些用户在特定时间段内有业务发生
2. **账单生成**: 批量生成账单前，先查看有哪些用户需要生成账单
3. **业务统计**: 分析用户活跃度和业务量分布
4. **异常处理**: 查看有财务调整的用户，便于后续处理

### 使用建议

1. **按月查询**: 通常按月度时间范围查询，便于月度结算
2. **结合生成账单**: 可配合账单生成接口使用，先查询用户列表再批量生成
3. **数据导出**: 可基于此接口数据进行进一步的数据分析和报表生成
4. **发货时间筛选**: 系统固定使用发货时间，确保数据统计的一致性

## 数据库查询说明

### 运单统计查询

```sql
SELECT
    m.user_id,
    u.username,
    u.nickname,
    COUNT(*) as manifest_count
FROM tb_manifest m
JOIN tb_user u ON m.user_id = u.id
WHERE m.status >= 3
    AND m.is_delete = 0
    AND m.shipment_time BETWEEN ? AND ?
GROUP BY m.user_id, u.username, u.nickname
ORDER BY manifest_count DESC
```

### 财务调整统计查询

```sql
SELECT
    adj.customer_account_id,
    u.username,
    u.nickname,
    COUNT(*) as adjustment_count
FROM manifest_financial_adjustments adj
JOIN tb_user u ON adj.customer_account_id = u.id
WHERE adj.effective_date BETWEEN ? AND ?
    AND adj.is_void = 0
GROUP BY adj.customer_account_id, u.username, u.nickname
ORDER BY adjustment_count DESC
```

## 版本历史

| 版本 | 日期       | 变更说明                                 |
| ---- | ---------- | ---------------------------------------- |
| v1.0 | 2024-12-01 | 初始版本，支持按时间范围查询用户统计信息 |

## 相关接口

- [生成账单接口](/docs/财务模块_生成账单_API文档.md)
- [分页查询账单记录接口](/docs/财务模块_分页查询账单记录_API文档.md)
