package persistence

import (
	"context"
	"fmt"
	"time"
	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/util"

	"gorm.io/gorm"
)

// BillingRepositoryImpl 账单仓储实现
type BillingRepositoryImpl struct {
	db *gorm.DB
}

// NewBillingRepository 创建账单仓储
func NewBillingRepository(db *gorm.DB) repository.BillingRepository {
	return &BillingRepositoryImpl{
		db: db,
	}
}

// parseAppliedFreightTemplates 解析应用的运费模板JSON数据
func (r *BillingRepositoryImpl) parseAppliedFreightTemplates(templatesJSON *model.AppliedFreightTemplatesJSON) (*entity.AppliedFreightTemplate, error) {
	if templatesJSON == nil {
		return nil, nil
	}

	result := &entity.AppliedFreightTemplate{}
	templateMap := map[string]interface{}(*templatesJSON)

	// 解析普货模板
	if generalTemplate, exists := templateMap["generalTemplate"]; exists && generalTemplate != nil {
		if templateData, ok := generalTemplate.(map[string]interface{}); ok {
			template, err := r.parseShippingFeeTemplate(templateData)
			if err == nil {
				result.GeneralTemplate = template
			}
		}
	}

	// 解析带电货物模板
	if batteryTemplate, exists := templateMap["batteryTemplate"]; exists && batteryTemplate != nil {
		if templateData, ok := batteryTemplate.(map[string]interface{}); ok {
			template, err := r.parseShippingFeeTemplate(templateData)
			if err == nil {
				result.BatteryTemplate = template
			}
		}
	}

	// 解析投函模板
	if postBoxTemplate, exists := templateMap["postBoxTemplate"]; exists && postBoxTemplate != nil {
		if templateData, ok := postBoxTemplate.(map[string]interface{}); ok {
			template, err := r.parseShippingFeeTemplate(templateData)
			if err == nil {
				result.PostBoxTemplate = template
			}
		}
	}

	return result, nil
}

// parseShippingFeeTemplate 解析单个运费模板数据
func (r *BillingRepositoryImpl) parseShippingFeeTemplate(templateData map[string]interface{}) (*entity.ShippingFeeTemplate, error) {
	template := &entity.ShippingFeeTemplate{}

	// 解析ID
	if id, exists := templateData["id"]; exists {
		if idValue, ok := id.(float64); ok {
			template.ID = int64(idValue)
		}
	}

	// 解析名称
	if name, exists := templateData["name"]; exists {
		if nameValue, ok := name.(string); ok {
			template.Name = nameValue
		}
	}

	// 解析首重价格
	if firstWeightPrice, exists := templateData["firstWeightPrice"]; exists {
		if price, ok := firstWeightPrice.(float64); ok {
			template.FirstWeightPrice = price
		}
	}

	// 解析首重范围
	if firstWeightRange, exists := templateData["firstWeightRange"]; exists {
		if rangeValue, ok := firstWeightRange.(float64); ok {
			template.FirstWeightRange = rangeValue
		}
	}

	// 解析续重价格
	if continuedWeightPrice, exists := templateData["continuedWeightPrice"]; exists {
		if price, ok := continuedWeightPrice.(float64); ok {
			template.ContinuedWeightPrice = price
		}
	}

	// 解析续重区间
	if continuedWeightInterval, exists := templateData["continuedWeightInterval"]; exists {
		if interval, ok := continuedWeightInterval.(float64); ok {
			template.ContinuedWeightInterval = interval
		}
	}

	// 解析轻抛系数
	if bulkCoefficient, exists := templateData["bulkCoefficient"]; exists {
		if coeff, ok := bulkCoefficient.(float64); ok {
			template.BulkCoefficient = int(coeff)
		}
	}

	// 解析三边和起始值
	if threeSidesStart, exists := templateData["threeSidesStart"]; exists {
		if start, ok := threeSidesStart.(float64); ok {
			template.ThreeSidesStart = start
		}
	}



	// 解析创建时间
	if createTime, exists := templateData["createTime"]; exists {
		if timeStr, ok := createTime.(string); ok {
			if parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local); err == nil {
				template.CreateTime = util.NewFormattedTime(parsedTime)
			}
		}
	}

	// 解析更新时间
	if updateTime, exists := templateData["updateTime"]; exists {
		if timeStr, ok := updateTime.(string); ok {
			if parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local); err == nil {
				template.UpdateTime = util.NewFormattedTime(parsedTime)
			}
		}
	}

	return template, nil
}

// SaveBillingRecord 保存账单记录
func (r *BillingRepositoryImpl) SaveBillingRecord(ctx context.Context, record *entity.BillingRecord) error {
	// 转换应用的运费模板信息为JSON
	var appliedTemplatesJSON *model.AppliedFreightTemplatesJSON
	if record.AppliedFreightTemplates != nil {
		templatesMap := make(map[string]interface{})
		if record.AppliedFreightTemplates.GeneralTemplate != nil {
			templatesMap["generalTemplate"] = record.AppliedFreightTemplates.GeneralTemplate
		}
		if record.AppliedFreightTemplates.BatteryTemplate != nil {
			templatesMap["batteryTemplate"] = record.AppliedFreightTemplates.BatteryTemplate
		}
		if record.AppliedFreightTemplates.PostBoxTemplate != nil {
			templatesMap["postBoxTemplate"] = record.AppliedFreightTemplates.PostBoxTemplate
		}
		jsonData := model.AppliedFreightTemplatesJSON(templatesMap)
		appliedTemplatesJSON = &jsonData
	}

	po := &model.BillingRecordPO{
		ID:                      record.ID,
		BillNumber:              record.BillNumber,
		CustomerAccountID:       record.CustomerAccountID,
		BillingCycleID:          record.BillingCycleID,
		BillDate:                record.BillDate,
		DueDate:                 record.DueDate,
		BillingPeriodStart:      record.BillingPeriodStart,
		BillingPeriodEnd:        record.BillingPeriodEnd,
		AppliedFreightTemplates: appliedTemplatesJSON,
		FreightChargesTotal:     record.FreightChargesTotal,
		AdjustmentChargesTotal:  record.AdjustmentChargesTotal,
		TotalAmount:             record.TotalAmount,
		AmountPaid:              record.AmountPaid,
		Currency:                record.Currency,
		Status:                  string(record.Status),
		PaymentMethod:           record.PaymentMethod,
		PaymentTransactionID:    record.PaymentTransactionID,
		PaymentDate:             record.PaymentDate,
		Notes:                   record.Notes,
		GeneratedByUserID:       record.GeneratedByUserID,
		CreateTime:              record.CreateTime,
		UpdateTime:              record.UpdateTime,
	}

	if record.ID == 0 {
		// 新增
		if err := r.db.WithContext(ctx).Create(po).Error; err != nil {
			return err
		}
		record.ID = po.ID
	} else {
		// 更新
		if err := r.db.WithContext(ctx).Save(po).Error; err != nil {
			return err
		}
	}

	return nil
}

// SaveBillingRecordItems 批量保存账单明细（支持分批处理，避免数据库参数过多错误）
func (r *BillingRepositoryImpl) SaveBillingRecordItems(ctx context.Context, items []*entity.BillingRecordItem) error {
	if len(items) == 0 {
		return nil
	}

	// 设置批量大小，每批最多保存200条记录
	// 这个数字是经过测试的安全值，既能保证性能又能避免参数过多错误
	const batchSize = 200

	// 分批处理
	for i := 0; i < len(items); i += batchSize {
		end := i + batchSize
		if end > len(items) {
			end = len(items)
		}

		// 当前批次的数据
		batch := items[i:end]

		// 转换为PO对象
		pos := make([]*model.BillingRecordItemPO, len(batch))
		for j, item := range batch {
			pos[j] = &model.BillingRecordItemPO{
				BillingRecordID:           item.BillingRecordID,
				ManifestID:                item.ManifestID,
				ExpressNumber:             item.ExpressNumber,
				OrderNo:                   item.OrderNo,
				TransferredTrackingNumber: item.TransferredTrackingNumber,
				OrderNumber:               item.OrderNumber,
				ManifestCreateTime:        item.ManifestCreateTime,
				ShipmentTime:              item.ShipmentTime,
				ReceiverName:              item.ReceiverName,
				ItemDescription:           item.ItemDescription,
				CargoType:                 int(item.CargoType),
				Weight:                    item.Weight,
				Length:                    item.Length,
				Width:                     item.Width,
				Height:                    item.Height,
				SumOfSides:                item.SumOfSides,
				DimensionalWeight:         item.DimensionalWeight,
				ChargeableWeight:          item.ChargeableWeight,
				BaseFreightFee:            item.BaseFreightFee,
				FirstWeightFee:            item.FirstWeightFee,
				ContinuedWeightFee:        item.ContinuedWeightFee,
				OverLengthSurcharge:       item.OverLengthSurcharge,
				RemoteAreaSurcharge:       item.RemoteAreaSurcharge,
				OverweightSurcharge:       item.OverweightSurcharge,
				ItemTotalAmount:           item.ItemTotalAmount,
				CreateTime:                item.CreateTime,
				UpdateTime:                item.UpdateTime,
			}
		}

		// 批量插入当前批次
		if err := r.db.WithContext(ctx).Create(pos).Error; err != nil {
			return fmt.Errorf("保存账单明细第 %d-%d 条记录失败: %w", i+1, end, err)
		}

		// 设置生成的ID回实体
		for j, po := range pos {
			batch[j].ID = po.ID
		}
	}

	return nil
}

// SaveBillingFinancialAdjustmentSnapshots 批量保存财务调整快照（支持分批处理，避免数据库参数过多错误）
func (r *BillingRepositoryImpl) SaveBillingFinancialAdjustmentSnapshots(ctx context.Context, snapshots []*entity.BillingFinancialAdjustmentSnapshot) error {
	if len(snapshots) == 0 {
		return nil
	}

	// 设置批量大小，每批最多保存200条记录
	const batchSize = 200

	// 分批处理
	for i := 0; i < len(snapshots); i += batchSize {
		end := i + batchSize
		if end > len(snapshots) {
			end = len(snapshots)
		}

		// 当前批次的数据
		batch := snapshots[i:end]

		// 转换为PO对象
		pos := make([]*model.BillingFinancialAdjustmentSnapshotPO, len(batch))
		for j, snapshot := range batch {
			var additionalDetailsJSON *model.AdditionalDetailsJSON
			if snapshot.AdditionalDetails != nil {
				jsonData := model.AdditionalDetailsJSON(snapshot.AdditionalDetails)
				additionalDetailsJSON = &jsonData
			}

			pos[j] = &model.BillingFinancialAdjustmentSnapshotPO{
				BillingRecordID:      snapshot.BillingRecordID,
				OriginalAdjustmentID: snapshot.OriginalAdjustmentID,
				AdjustmentType:       snapshot.AdjustmentType,
				Description:          snapshot.Description,
				AdditionalDetails:    additionalDetailsJSON,
				Amount:               snapshot.Amount,
				Currency:             snapshot.Currency,
				EffectiveDate:        snapshot.EffectiveDate,

				// 运单信息快照
				ManifestID:                        snapshot.ManifestID,
				ManifestExpressNumber:             snapshot.ManifestExpressNumber,
				ManifestOrderNo:                   snapshot.ManifestOrderNo,
				ManifestTransferredTrackingNumber: snapshot.ManifestTransferredTrackingNumber,
				ManifestCustomerOrderNumber:       snapshot.ManifestCustomerOrderNumber,
				ManifestCreateTime:                snapshot.ManifestCreateTime,
				ManifestShipmentTime:              snapshot.ManifestShipmentTime,
				ManifestReceiverName:              snapshot.ManifestReceiverName,
				ManifestItemDescription:           snapshot.ManifestItemDescription,
				ManifestCargoType:                 snapshot.ManifestCargoType,

				// 运单尺寸重量信息快照
				ManifestWeight:            snapshot.ManifestWeight,
				ManifestLength:            snapshot.ManifestLength,
				ManifestWidth:             snapshot.ManifestWidth,
				ManifestHeight:            snapshot.ManifestHeight,
				ManifestSumOfSides:        snapshot.ManifestSumOfSides,
				ManifestDimensionalWeight: snapshot.ManifestDimensionalWeight,
				ManifestChargeableWeight:  snapshot.ManifestChargeableWeight,

				// 快照创建时间
				SnapshotTime: snapshot.SnapshotTime,
			}
		}

		// 批量插入当前批次
		if err := r.db.WithContext(ctx).Create(pos).Error; err != nil {
			return fmt.Errorf("保存财务调整快照第 %d-%d 条记录失败: %w", i+1, end, err)
		}

		// 设置生成的ID回实体
		for j, po := range pos {
			batch[j].ID = po.ID
		}
	}

	return nil
}

// FindManifestsForBilling 根据条件查询用于生成账单的运单
func (r *BillingRepositoryImpl) FindManifestsForBilling(ctx context.Context, timeType entity.BillingTimeType, startTime, endTime time.Time, userID int64) ([]*entity.Manifest, error) {
	var manifestPOs []*model.ManifestPO

	query := r.db.WithContext(ctx).Model(&model.ManifestPO{}).
		Where("status >= ? AND is_delete = ? AND user_id = ?", 3, 0, userID)

	// 根据时间类型选择时间字段
	switch timeType {
	case entity.BillingTimeTypeCreateTime:
		query = query.Where("create_time BETWEEN ? AND ?", startTime, endTime)
	case entity.BillingTimeTypeShipmentTime:
		query = query.Where("shipment_time BETWEEN ? AND ?", startTime, endTime)
	default:
		return nil, fmt.Errorf("无效的时间类型: %s", timeType)
	}

	if err := query.Find(&manifestPOs).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	manifests := make([]*entity.Manifest, len(manifestPOs))
	for i, po := range manifestPOs {
		manifests[i] = po.ToEntity()
	}

	// 如果有查询到运单，批量获取物品信息
	if len(manifests) > 0 {
		// 收集所有运单ID
		manifestIDs := make([]int64, 0, len(manifests))
		for _, m := range manifests {
			manifestIDs = append(manifestIDs, m.ID)
		}

		// 批量查询运单物品
		items, err := r.getManifestItemsByManifestIDs(ctx, manifestIDs)
		if err != nil {
			return nil, fmt.Errorf("批量获取运单物品失败: %w", err)
		}

		// 将物品与运单关联
		itemMap := make(map[int64][]*entity.ManifestItem)
		for _, item := range items {
			itemMap[item.ManifestID] = append(itemMap[item.ManifestID], item)
		}

		// 为每个运单设置物品列表
		for i := range manifests {
			if itemsPtr, exists := itemMap[manifests[i].ID]; exists {
				manifests[i].Items = make([]entity.ManifestItem, len(itemsPtr))
				for j, item := range itemsPtr {
					manifests[i].Items[j] = *item
				}
			} else {
				// 如果没有物品，设置为空数组
				manifests[i].Items = []entity.ManifestItem{}
			}
		}
	}

	return manifests, nil
}

// getManifestItemsByManifestIDs 根据多个运单ID批量获取物品列表
func (r *BillingRepositoryImpl) getManifestItemsByManifestIDs(ctx context.Context, manifestIDs []int64) ([]*entity.ManifestItem, error) {
	if len(manifestIDs) == 0 {
		return []*entity.ManifestItem{}, nil
	}

	var itemPOs []*model.ManifestItemPO
	if err := r.db.WithContext(ctx).Where("manifest_id IN ?", manifestIDs).Find(&itemPOs).Error; err != nil {
		return nil, err
	}

	items := make([]*entity.ManifestItem, 0, len(itemPOs))
	for _, po := range itemPOs {
		items = append(items, po.ToEntity())
	}
	return items, nil
}

// FindFinancialAdjustmentsForBilling 根据用户ID和生效日期范围查询财务调整记录
func (r *BillingRepositoryImpl) FindFinancialAdjustmentsForBilling(ctx context.Context, userID int64, startDate, endDate time.Time) ([]*entity.ManifestFinancialAdjustment, error) {
	var adjustmentPOs []*model.ManifestFinancialAdjustmentPO

	if err := r.db.WithContext(ctx).Model(&model.ManifestFinancialAdjustmentPO{}).
		Where("customer_account_id = ?", userID).
		Where("effective_date BETWEEN ? AND ?", startDate, endDate).
		Where("is_void = ?", false).
		Find(&adjustmentPOs).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	adjustments := make([]*entity.ManifestFinancialAdjustment, len(adjustmentPOs))
	for i, po := range adjustmentPOs {
		adjustment, err := po.ToEntity()
		if err != nil {
			return nil, err
		}
		adjustments[i] = adjustment
	}

	return adjustments, nil
}

// FindManifestsByIDs 根据运单ID列表批量查询运单信息
func (r *BillingRepositoryImpl) FindManifestsByIDs(ctx context.Context, manifestIDs []int64) ([]*entity.Manifest, error) {
	if len(manifestIDs) == 0 {
		return []*entity.Manifest{}, nil
	}

	var manifestPOs []*model.ManifestPO
	if err := r.db.WithContext(ctx).Where("id IN ?", manifestIDs).Find(&manifestPOs).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	manifests := make([]*entity.Manifest, len(manifestPOs))
	for i, po := range manifestPOs {
		manifests[i] = po.ToEntity()
	}

	// 批量获取物品信息
	if len(manifests) > 0 {
		// 收集所有运单ID
		manifestIDsForItems := make([]int64, 0, len(manifests))
		for _, m := range manifests {
			manifestIDsForItems = append(manifestIDsForItems, m.ID)
		}

		// 批量查询运单物品
		items, err := r.getManifestItemsByManifestIDs(ctx, manifestIDsForItems)
		if err != nil {
			return nil, fmt.Errorf("批量获取运单物品失败: %w", err)
		}

		// 将物品与运单关联
		itemMap := make(map[int64][]*entity.ManifestItem)
		for _, item := range items {
			itemMap[item.ManifestID] = append(itemMap[item.ManifestID], item)
		}

		// 为每个运单设置物品列表
		for i := range manifests {
			if itemsPtr, exists := itemMap[manifests[i].ID]; exists {
				manifests[i].Items = make([]entity.ManifestItem, len(itemsPtr))
				for j, item := range itemsPtr {
					manifests[i].Items[j] = *item
				}
			}
		}
	}

	return manifests, nil
}

// GenerateBillNumber 生成唯一的账单编号
func (r *BillingRepositoryImpl) GenerateBillNumber(ctx context.Context) (string, error) {
	// 生成格式: BILL-YYYYMMDD-HHMMSS-随机数
	now := time.Now()
	baseNumber := fmt.Sprintf("BILL-%s-%06d",
		now.Format("20060102-150405"),
		now.Nanosecond()/1000) // 使用微秒作为随机数

	// 检查是否已存在，如果存在则添加序号
	var count int64
	originalNumber := baseNumber

	for {
		if err := r.db.WithContext(ctx).Model(&model.BillingRecordPO{}).
			Where("bill_number = ?", baseNumber).Count(&count).Error; err != nil {
			return "", err
		}

		if count == 0 {
			break
		}

		// 如果存在重复，添加序号
		baseNumber = fmt.Sprintf("%s-%d", originalNumber, count+1)
	}

	return baseNumber, nil
}

// FindBillingRecordByID 根据ID查询账单记录
func (r *BillingRepositoryImpl) FindBillingRecordByID(ctx context.Context, id int64) (*entity.BillingRecord, error) {
	var po model.BillingRecordPO

	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&po).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	record := &entity.BillingRecord{
		ID:                     po.ID,
		BillNumber:             po.BillNumber,
		CustomerAccountID:      po.CustomerAccountID,
		BillingCycleID:         po.BillingCycleID,
		BillDate:               po.BillDate,
		DueDate:                po.DueDate,
		BillingPeriodStart:     po.BillingPeriodStart,
		BillingPeriodEnd:       po.BillingPeriodEnd,
		FreightChargesTotal:    po.FreightChargesTotal,
		AdjustmentChargesTotal: po.AdjustmentChargesTotal,
		TotalAmount:            po.TotalAmount,
		AmountPaid:             po.AmountPaid,
		BalanceDue:             po.TotalAmount - po.AmountPaid,
		Currency:               po.Currency,
		Status:                 entity.BillingStatus(po.Status),
		PaymentMethod:          po.PaymentMethod,
		PaymentTransactionID:   po.PaymentTransactionID,
		PaymentDate:            po.PaymentDate,
		Notes:                  po.Notes,
		GeneratedByUserID:      po.GeneratedByUserID,
		CreateTime:             po.CreateTime,
		UpdateTime:             po.UpdateTime,
	}

	// 解析应用的运费模板信息
	if po.AppliedFreightTemplates != nil {
		appliedTemplates, err := r.parseAppliedFreightTemplates(po.AppliedFreightTemplates)
		if err == nil && appliedTemplates != nil {
			record.AppliedFreightTemplates = appliedTemplates
		} else {
			// 如果解析失败，创建空的模板结构
			record.AppliedFreightTemplates = &entity.AppliedFreightTemplate{}
		}
	}

	return record, nil
}

// CountBillingRecords 统计账单记录总数
func (r *BillingRepositoryImpl) CountBillingRecords(ctx context.Context, filters interface{}) (int64, error) {
	// 将filters转换为具体类型
	filter, ok := filters.(*service.BillingRecordFilters)
	if !ok {
		return 0, fmt.Errorf("invalid filter type")
	}

	query := r.db.WithContext(ctx).Model(&model.BillingRecordPO{})
	query = r.applyBillingRecordFilters(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// FindBillingRecords 分页查询账单记录
func (r *BillingRepositoryImpl) FindBillingRecords(ctx context.Context, filters interface{}, page, pageSize int) ([]*entity.BillingRecord, error) {
	// 将filters转换为具体类型
	filter, ok := filters.(*service.BillingRecordFilters)
	if !ok {
		return nil, fmt.Errorf("invalid filter type")
	}

	var pos []*model.BillingRecordPO

	query := r.db.WithContext(ctx).Model(&model.BillingRecordPO{})
	query = r.applyBillingRecordFilters(query, filter)

	// 分页查询，按创建时间倒序
	offset := (page - 1) * pageSize
	if err := query.Order("create_time DESC").Offset(offset).Limit(pageSize).Find(&pos).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	records := make([]*entity.BillingRecord, len(pos))
	for i, po := range pos {
		record := &entity.BillingRecord{
			ID:                     po.ID,
			BillNumber:             po.BillNumber,
			CustomerAccountID:      po.CustomerAccountID,
			BillingCycleID:         po.BillingCycleID,
			BillDate:               po.BillDate,
			DueDate:                po.DueDate,
			BillingPeriodStart:     po.BillingPeriodStart,
			BillingPeriodEnd:       po.BillingPeriodEnd,
			FreightChargesTotal:    po.FreightChargesTotal,
			AdjustmentChargesTotal: po.AdjustmentChargesTotal,
			TotalAmount:            po.TotalAmount,
			AmountPaid:             po.AmountPaid,
			BalanceDue:             po.TotalAmount - po.AmountPaid,
			Currency:               po.Currency,
			Status:                 entity.BillingStatus(po.Status),
			PaymentMethod:          po.PaymentMethod,
			PaymentTransactionID:   po.PaymentTransactionID,
			PaymentDate:            po.PaymentDate,
			Notes:                  po.Notes,
			GeneratedByUserID:      po.GeneratedByUserID,
			CreateTime:             po.CreateTime,
			UpdateTime:             po.UpdateTime,
		}

		// 解析应用的运费模板信息
		if po.AppliedFreightTemplates != nil {
			appliedTemplates, err := r.parseAppliedFreightTemplates(po.AppliedFreightTemplates)
			if err == nil && appliedTemplates != nil {
				record.AppliedFreightTemplates = appliedTemplates
			} else {
				// 如果解析失败，创建空的模板结构
				record.AppliedFreightTemplates = &entity.AppliedFreightTemplate{}
			}
		}

		records[i] = record
	}

	return records, nil
}

// FindBillingRecordItemsByBillingRecordID 根据账单记录ID查询账单明细列表
func (r *BillingRepositoryImpl) FindBillingRecordItemsByBillingRecordID(ctx context.Context, billingRecordID int64, page, pageSize int) ([]*entity.BillingRecordItem, int64, error) {
	// 先统计总数
	var total int64
	if err := r.db.WithContext(ctx).Model(&model.BillingRecordItemPO{}).
		Where("billing_record_id = ?", billingRecordID).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询明细
	var pos []*model.BillingRecordItemPO
	offset := (page - 1) * pageSize
	if err := r.db.WithContext(ctx).
		Where("billing_record_id = ?", billingRecordID).
		Order("id ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&pos).Error; err != nil {
		return nil, 0, err
	}

	// 转换为实体
	items := make([]*entity.BillingRecordItem, len(pos))
	for i, po := range pos {
		items[i] = &entity.BillingRecordItem{
			ID:                        po.ID,
			BillingRecordID:           po.BillingRecordID,
			ManifestID:                po.ManifestID,
			ExpressNumber:             po.ExpressNumber,
			OrderNo:                   po.OrderNo,
			TransferredTrackingNumber: po.TransferredTrackingNumber,
			OrderNumber:               po.OrderNumber,
			ManifestCreateTime:        po.ManifestCreateTime,
			ShipmentTime:              po.ShipmentTime,
			ReceiverName:              po.ReceiverName,
			ItemDescription:           po.ItemDescription,
			CargoType:                 entity.CargoType(po.CargoType),
			Weight:                    po.Weight,
			Length:                    po.Length,
			Width:                     po.Width,
			Height:                    po.Height,
			SumOfSides:                po.SumOfSides,
			DimensionalWeight:         po.DimensionalWeight,
			ChargeableWeight:          po.ChargeableWeight,
			BaseFreightFee:            po.BaseFreightFee,
			FirstWeightFee:            po.FirstWeightFee,
			ContinuedWeightFee:        po.ContinuedWeightFee,
			OverLengthSurcharge:       po.OverLengthSurcharge,
			RemoteAreaSurcharge:       po.RemoteAreaSurcharge,
			OverweightSurcharge:       po.OverweightSurcharge,
			ItemTotalAmount:           po.ItemTotalAmount,
			CreateTime:                po.CreateTime,
			UpdateTime:                po.UpdateTime,
		}
	}

	return items, total, nil
}

// applyBillingRecordFilters 应用查询过滤器
func (r *BillingRepositoryImpl) applyBillingRecordFilters(query *gorm.DB, filter *service.BillingRecordFilters) *gorm.DB {
	// 客户账户ID过滤
	if filter.CustomerAccountID != nil {
		query = query.Where("customer_account_id = ?", *filter.CustomerAccountID)
	}

	// 账期批次ID过滤
	if filter.BillingCycleID != nil {
		query = query.Where("billing_cycle_id = ?", *filter.BillingCycleID)
	}

	// 账单状态过滤
	if filter.Status != nil && *filter.Status != "" {
		query = query.Where("status = ?", *filter.Status)
	}

	// 账单编号模糊查询
	if filter.BillNumber != nil && *filter.BillNumber != "" {
		query = query.Where("bill_number LIKE ?", "%"+*filter.BillNumber+"%")
	}

	// 账单日期范围
	if filter.BillDateStart != nil {
		query = query.Where("DATE(bill_date) >= ?", filter.BillDateStart.Format("2006-01-02"))
	}
	if filter.BillDateEnd != nil {
		query = query.Where("DATE(bill_date) <= ?", filter.BillDateEnd.Format("2006-01-02"))
	}

	// 账期日期范围
	if filter.BillingPeriodStart != nil {
		query = query.Where("DATE(billing_period_start) >= ?", filter.BillingPeriodStart.Format("2006-01-02"))
	}
	if filter.BillingPeriodEnd != nil {
		query = query.Where("DATE(billing_period_end) <= ?", filter.BillingPeriodEnd.Format("2006-01-02"))
	}

	// 金额范围过滤
	if filter.MinAmount != nil {
		query = query.Where("total_amount >= ?", *filter.MinAmount)
	}
	if filter.MaxAmount != nil {
		query = query.Where("total_amount <= ?", *filter.MaxAmount)
	}

	// 货币单位过滤
	if filter.Currency != nil && *filter.Currency != "" {
		query = query.Where("currency = ?", *filter.Currency)
	}

	return query
}

// FindUsersWithManifestsForBilling 查询在指定时间范围内有可生成账单运单的用户统计
func (r *BillingRepositoryImpl) FindUsersWithManifestsForBilling(ctx context.Context, timeType entity.BillingTimeType, startTime, endTime time.Time) ([]*entity.BillingUserStats, error) {
	// 构建查询SQL
	var timeField string
	switch timeType {
	case entity.BillingTimeTypeCreateTime:
		timeField = "create_time"
	case entity.BillingTimeTypeShipmentTime:
		timeField = "shipment_time"
	default:
		return nil, fmt.Errorf("无效的时间类型: %s", timeType)
	}

	// 执行统计查询
	type userManifestStats struct {
		UserID        int64  `db:"user_id"`
		Username      string `db:"username"`
		Nickname      string `db:"nickname"`
		ManifestCount int64  `db:"manifest_count"`
	}

	var results []userManifestStats

	sql := fmt.Sprintf(`
		SELECT 
			m.user_id,
			u.username,
			u.nickname,
			COUNT(*) as manifest_count
		FROM tb_manifest m
		JOIN tb_user u ON m.user_id = u.id
		WHERE m.status >= 3 
			AND m.is_delete = 0 
			AND m.%s BETWEEN ? AND ?
		GROUP BY m.user_id, u.username, u.nickname
		ORDER BY manifest_count DESC
	`, timeField)

	if err := r.db.WithContext(ctx).Raw(sql, startTime, endTime).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	userStats := make([]*entity.BillingUserStats, len(results))
	for i, result := range results {
		userStats[i] = &entity.BillingUserStats{
			UserID:        result.UserID,
			Username:      result.Username,
			Nickname:      result.Nickname,
			ManifestCount: result.ManifestCount,
		}
	}

	return userStats, nil
}

// FindUsersWithAdjustmentsForBilling 查询在指定时间范围内有财务调整记录的用户统计
func (r *BillingRepositoryImpl) FindUsersWithAdjustmentsForBilling(ctx context.Context, startTime, endTime time.Time) ([]*entity.BillingUserStats, error) {
	// 执行统计查询
	type userAdjustmentStats struct {
		CustomerAccountID int64  `db:"customer_account_id"`
		Username          string `db:"username"`
		Nickname          string `db:"nickname"`
		AdjustmentCount   int64  `db:"adjustment_count"`
	}

	var results []userAdjustmentStats

	sql := `
		SELECT 
			adj.customer_account_id,
			u.username,
			u.nickname,
			COUNT(*) as adjustment_count
		FROM manifest_financial_adjustments adj
		JOIN tb_user u ON adj.customer_account_id = u.id
		WHERE adj.effective_date BETWEEN ? AND ?
			AND adj.is_void = 0
		GROUP BY adj.customer_account_id, u.username, u.nickname
		ORDER BY adjustment_count DESC
	`

	if err := r.db.WithContext(ctx).Raw(sql, startTime, endTime).Scan(&results).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	userStats := make([]*entity.BillingUserStats, len(results))
	for i, result := range results {
		userStats[i] = &entity.BillingUserStats{
			UserID:          result.CustomerAccountID,
			Username:        result.Username,
			Nickname:        result.Nickname,
			AdjustmentCount: result.AdjustmentCount,
		}
	}

	return userStats, nil
}

// FindBillingFinancialAdjustmentSnapshotsByBillingRecordID 根据账单记录ID分页查询财务调整快照列表
func (r *BillingRepositoryImpl) FindBillingFinancialAdjustmentSnapshotsByBillingRecordID(ctx context.Context, billingRecordID int64, page, pageSize int) ([]*entity.BillingFinancialAdjustmentSnapshot, int64, error) {
	// 先统计总数
	var total int64
	if err := r.db.WithContext(ctx).Model(&model.BillingFinancialAdjustmentSnapshotPO{}).
		Where("billing_record_id = ?", billingRecordID).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询快照
	var pos []*model.BillingFinancialAdjustmentSnapshotPO
	offset := (page - 1) * pageSize
	if err := r.db.WithContext(ctx).
		Where("billing_record_id = ?", billingRecordID).
		Order("id ASC").
		Offset(offset).
		Limit(pageSize).
		Find(&pos).Error; err != nil {
		return nil, 0, err
	}

	// 转换为实体
	snapshots := make([]*entity.BillingFinancialAdjustmentSnapshot, len(pos))
	for i, po := range pos {
		var additionalDetails map[string]interface{}
		if po.AdditionalDetails != nil {
			additionalDetails = map[string]interface{}(*po.AdditionalDetails)
		}

		snapshot := &entity.BillingFinancialAdjustmentSnapshot{
			ID:                   po.ID,
			BillingRecordID:      po.BillingRecordID,
			OriginalAdjustmentID: po.OriginalAdjustmentID,
			AdjustmentType:       po.AdjustmentType,
			Description:          po.Description,
			AdditionalDetails:    additionalDetails,
			Amount:               po.Amount,
			Currency:             po.Currency,
			EffectiveDate:        po.EffectiveDate,

			// 运单信息快照
			ManifestID:                        po.ManifestID,
			ManifestExpressNumber:             po.ManifestExpressNumber,
			ManifestOrderNo:                   po.ManifestOrderNo,
			ManifestTransferredTrackingNumber: po.ManifestTransferredTrackingNumber,
			ManifestCustomerOrderNumber:       po.ManifestCustomerOrderNumber,
			ManifestCreateTime:                po.ManifestCreateTime,
			ManifestShipmentTime:              po.ManifestShipmentTime,
			ManifestReceiverName:              po.ManifestReceiverName,
			ManifestItemDescription:           po.ManifestItemDescription,
			ManifestCargoType:                 po.ManifestCargoType,

			// 运单尺寸重量信息快照
			ManifestWeight:            po.ManifestWeight,
			ManifestLength:            po.ManifestLength,
			ManifestWidth:             po.ManifestWidth,
			ManifestHeight:            po.ManifestHeight,
			ManifestSumOfSides:        po.ManifestSumOfSides,
			ManifestDimensionalWeight: po.ManifestDimensionalWeight,
			ManifestChargeableWeight:  po.ManifestChargeableWeight,

			// 快照创建时间
			SnapshotTime: po.SnapshotTime,
		}

		snapshots[i] = snapshot
	}

	return snapshots, total, nil
}
