package handler

import (
	"net/http"
	"strconv"

	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// ProblemTicketHandler 问题工单处理器
type ProblemTicketHandler struct {
	problemTicketService *service.ProblemTicketService
}

// NewProblemTicketHandler 创建问题工单处理器
func NewProblemTicketHandler(problemTicketService *service.ProblemTicketService) *ProblemTicketHandler {
	return &ProblemTicketHandler{
		problemTicketService: problemTicketService,
	}
}

// GetProblemTickets 获取问题工单列表
// @Summary 分页查询问题工单
// @Description 根据查询条件分页获取问题工单列表
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1" default(1)
// @Param pageSize query int false "每页记录数，默认为10" default(10)
// @Param status query string false "工单状态"
// @Param problemTypeCode query string false "问题类型代码"
// @Param trackingNumber query string false "物流单号(支持模糊查询)"
// @Param customerId query int false "客户ID"
// @Param assignedUserId query int false "处理人ID"
// @Param startTime query string false "开始时间，格式：yyyy-MM-dd HH:mm:ss"
// @Param endTime query string false "结束时间，格式：yyyy-MM-dd HH:mm:ss"
// @Param manifestId query int false "运单ID"
// @Param priority query int false "优先级"
// @Success 200 {object} util.Response{data=service.ListProblemTicketsResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets [get]
func (h *ProblemTicketHandler) GetProblemTickets(c *gin.Context) {
	var req service.ListProblemTicketsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层查询
	resp, code, err := h.problemTicketService.ListProblemTickets(c.Request.Context(), &req)
	if err != nil {
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}
	
	// 返回结果
	util.ResponseSuccess(c, resp)
}

// GetProblemTicketByID 根据ID获取问题工单详情
// @Summary 获取问题工单详情
// @Description 根据ID获取问题工单详细信息
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param id path int true "问题工单ID"
// @Success 200 {object} util.Response{data=service.ProblemTicketDetailResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "问题工单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/{id} [get]
func (h *ProblemTicketHandler) GetProblemTicketByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "无效的工单ID", http.StatusBadRequest)
		return
	}
	
	resp, code, err := h.problemTicketService.GetProblemTicketByID(c.Request.Context(), id)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		switch code {
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}
	
	util.ResponseSuccess(c, resp)
}

// ResolveProblemTicket 处理问题工单
// @Summary 处理问题工单
// @Description 将问题工单状态从待处理更新为已解决，并记录处理备注
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param id path int true "问题工单ID"
// @Param request body service.ResolveProblemTicketRequest true "处理请求体 (只需要remarks)"
// @Success 200 {object} util.Response{data=service.ResolveProblemTicketResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 401 {object} util.Response "未授权或无法获取用户信息"
// @Failure 404 {object} util.Response "问题工单不存在"
// @Failure 409 {object} util.Response "工单状态不正确"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/{id}/resolve [put]
func (h *ProblemTicketHandler) ResolveProblemTicket(c *gin.Context) {
	var req service.ResolveProblemTicketRequest

	// 从路径参数绑定ID
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "无效的工单ID: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从请求体绑定其他参数，如Remarks
	// 注意：即使请求体为空，ShouldBindJSON也不会报错，如果remarks是可选的，这样可以
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果确实需要remarks，但又允许请求体为空，这里的处理逻辑可能需要调整
		// 但当前需求是只需要remarks，如果绑定失败，则认为是参数错误
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请求体参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.problemTicketService.ResolveProblemTicket(c, &req) // 传递gin.Context以获取用户信息
	if err != nil {
		httpStatus := http.StatusInternalServerError
		switch code {
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_PROBLEM_TICKET_STATUS_INVALID_OPERATION:
			httpStatus = http.StatusConflict // 409 Conflict
		case valueobject.ERROR_UNAUTHORIZED:
			httpStatus = http.StatusUnauthorized // 401 Unauthorized
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// AddProblemTicketRemark 添加问题工单备注
// @Summary 添加问题工单备注
// @Description 为指定ID的问题工单添加或更新处理备注
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param id path int true "问题工单ID"
// @Param request body service.AddProblemTicketRemarkRequest true "备注请求体 (只需要remarks)"
// @Success 200 {object} util.Response{data=service.AddProblemTicketRemarkResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误 (ID或备注无效)"
// @Failure 404 {object} util.Response "问题工单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/{id}/remark [put]
func (h *ProblemTicketHandler) AddProblemTicketRemark(c *gin.Context) {
	var req service.AddProblemTicketRemarkRequest

	// 从路径参数绑定ID
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "无效的工单ID: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从请求体绑定备注 (remarks 必填)
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请求体参数绑定错误 (备注为必填): "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.problemTicketService.AddRemarkToProblemTicket(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		switch code {
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// MarkAllProblemTicketsAsResolved 批量处理所有待处理问题工单
// @Summary 批量将所有待处理问题工单标记为已处理
// @Description 将系统中所有状态为待处理的问题工单统一标记为已处理状态
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param request body service.MarkAllProblemTicketsAsResolvedRequest false "处理请求体，包含可选的处理备注"
// @Success 200 {object} util.Response{data=service.MarkAllProblemTicketsAsResolvedResponse} "成功响应，返回处理的工单数量"
// @Failure 401 {object} util.Response "未授权或无法获取用户信息"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/resolve-all [post]
func (h *ProblemTicketHandler) MarkAllProblemTicketsAsResolved(c *gin.Context) {
	var req service.MarkAllProblemTicketsAsResolvedRequest
	
	// 从请求体绑定参数（备注是可选的）
	if err := c.ShouldBindJSON(&req); err != nil {
		// 如果请求体为空，使用空的请求结构体继续处理
		// 因为备注是可选的，所以这里不返回错误
	}

	resp, code, err := h.problemTicketService.MarkAllProblemTicketsAsResolved(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		if code == valueobject.ERROR_UNAUTHORIZED {
			httpStatus = http.StatusUnauthorized
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// MarkSpecificProblemTicketsAsResolved 批量处理指定的问题工单
// @Summary 批量将指定问题工单标记为已处理
// @Description 将指定ID列表中状态为待处理的问题工单标记为已处理状态
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param request body service.MarkSpecificProblemTicketsAsResolvedRequest true "处理请求体，包含工单ID列表和可选的处理备注"
// @Success 200 {object} util.Response{data=service.MarkSpecificProblemTicketsAsResolvedResponse} "成功响应，返回处理的工单数量"
// @Failure 400 {object} util.Response "请求参数错误（ID列表为空）"
// @Failure 401 {object} util.Response "未授权或无法获取用户信息"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/resolve-specific [post]
func (h *ProblemTicketHandler) MarkSpecificProblemTicketsAsResolved(c *gin.Context) {
	var req service.MarkSpecificProblemTicketsAsResolvedRequest
	
	// 从请求体绑定参数
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请求体参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 验证工单ID列表不为空
	if len(req.TicketIDs) == 0 {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "工单ID列表不能为空", http.StatusBadRequest)
		return
	}

	resp, code, err := h.problemTicketService.MarkSpecificProblemTicketsAsResolved(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_UNAUTHORIZED:
			httpStatus = http.StatusUnauthorized
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// MarkProblemTicketAsPending 将问题工单标记为待处理
// @Summary 将问题工单标记为待处理
// @Description 将指定ID的问题工单状态标记为待处理
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param id path int true "问题工单ID"
// @Param request body service.MarkProblemTicketAsPendingRequest true "处理请求体 (只需要remarks)"
// @Success 200 {object} util.Response{data=service.MarkProblemTicketAsPendingResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 401 {object} util.Response "未授权或无法获取用户信息"
// @Failure 404 {object} util.Response "问题工单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/{id}/mark-pending [put]
func (h *ProblemTicketHandler) MarkProblemTicketAsPending(c *gin.Context) {
	var req service.MarkProblemTicketAsPendingRequest

	// 从路径参数绑定ID
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "无效的工单ID: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从请求体绑定其他参数，如Remarks (可选)
	if err := c.ShouldBindJSON(&req); err != nil {
		// 由于备注是可选的，这里可以允许请求体为空
		// c.ShouldBindJSON 即使请求体为空也不会报错
	}

	resp, code, err := h.problemTicketService.MarkProblemTicketAsPending(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		switch code {
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_UNAUTHORIZED:
			httpStatus = http.StatusUnauthorized
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// MarkSpecificProblemTicketsAsPending 批量将指定问题工单标记为待处理
// @Summary 批量将指定问题工单标记为待处理
// @Description 将指定ID列表中的问题工单标记为待处理状态
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param request body service.MarkSpecificProblemTicketsAsPendingRequest true "处理请求体，包含工单ID列表和可选的处理备注"
// @Success 200 {object} util.Response{data=service.MarkSpecificProblemTicketsAsPendingResponse} "成功响应，返回处理的工单数量"
// @Failure 400 {object} util.Response "请求参数错误（ID列表为空）"
// @Failure 401 {object} util.Response "未授权或无法获取用户信息"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/problem-tickets/mark-pending [post]
func (h *ProblemTicketHandler) MarkSpecificProblemTicketsAsPending(c *gin.Context) {
	var req service.MarkSpecificProblemTicketsAsPendingRequest
	
	// 从请求体绑定参数
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请求体参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 验证工单ID列表不为空
	if len(req.TicketIDs) == 0 {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "工单ID列表不能为空", http.StatusBadRequest)
		return
	}

	resp, code, err := h.problemTicketService.MarkSpecificProblemTicketsAsPending(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_UNAUTHORIZED:
			httpStatus = http.StatusUnauthorized
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
} 

// GetUsersWithProblemManifests godoc
// @Summary 获取拥有问题运单的用户列表
// @Description 获取拥有问题运单的用户列表，并返回每个用户有多少单待处理
// @Tags 问题工单管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} util.Response{data=service.UsersWithProblemManifestsResponse} "成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器错误"
// @Router /api/v1/problem-tickets/users-with-problems [get]
// @Security ApiKeyAuth
func (h *ProblemTicketHandler) GetUsersWithProblemManifests(c *gin.Context) {
	var req service.UsersWithProblemManifestsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, errCode, err := h.problemTicketService.GetUsersWithProblemManifests(c, &req)
	if err != nil {
		util.ResponseError(c, errCode, err.Error(), http.StatusInternalServerError)
		return
	}
	util.ResponseSuccess(c, resp)
}

// GetProblemManifestCount 获取问题运单数量
func (h *ProblemTicketHandler) GetProblemManifestCount(c *gin.Context) {
	// 调用service层获取所有问题运单数量
	count, err := h.problemTicketService.GetProblemManifestCount(c)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_SERVICE_UNAVAILABLE, "获取问题运单数量失败", http.StatusServiceUnavailable)
		return
	}
	
	// 返回结果
	util.ResponseSuccess(c, gin.H{
		"count": count,
	})
} 