package entity

import (
	"time"
)

// BillingTimeType 账期时间类型
type BillingTimeType string

const (
	BillingTimeTypeCreateTime   BillingTimeType = "CREATE_TIME"   // 预报时间
	BillingTimeTypeShipmentTime BillingTimeType = "SHIPMENT_TIME" // 发货时间
)

// BillingStatus 账单状态
type BillingStatus string

const (
	BillingStatusUnpaid        BillingStatus = "UNPAID"         // 未付款
	BillingStatusPaid          BillingStatus = "PAID"           // 已付款
	BillingStatusPartiallyPaid BillingStatus = "PARTIALLY_PAID" // 部分付款
	BillingStatusOverdue       BillingStatus = "OVERDUE"        // 逾期
	BillingStatusVoid          BillingStatus = "VOID"           // 作废
	BillingStatusError         BillingStatus = "ERROR"          // 异常
)

// CargoType 货物类型
type CargoType int

const (
	CargoTypeGeneral   CargoType = 1 // 普货
	CargoTypeBattery   CargoType = 2 // 带电货物
	CargoTypePostBox   CargoType = 3 // 投函货物
)

// AppliedFreightTemplate 应用的运费模板信息
type AppliedFreightTemplate struct {
	GeneralTemplate *ShippingFeeTemplate `json:"generalTemplate,omitempty"`  // 普货模板
	BatteryTemplate *ShippingFeeTemplate `json:"batteryTemplate,omitempty"`  // 带电货物模板
	PostBoxTemplate *ShippingFeeTemplate `json:"postBoxTemplate,omitempty"`  // 投函货物模板
}

// BillingRecord 账单主记录实体
type BillingRecord struct {
	ID                       int64                     `json:"id"`                       // 账单主键ID
	BillNumber               string                    `json:"billNumber"`               // 账单编号
	CustomerAccountID        int64                     `json:"customerAccountId"`        // 客户账户ID
	BillingCycleID           int64                     `json:"billingCycleId"`           // 所属账期批次ID
	BillDate                 time.Time                 `json:"billDate"`                 // 账单日期
	DueDate                  *time.Time                `json:"dueDate,omitempty"`        // 付款截止日期
	BillingPeriodStart       time.Time                 `json:"billingPeriodStart"`       // 账期开始日期
	BillingPeriodEnd         time.Time                 `json:"billingPeriodEnd"`         // 账期结束日期
	AppliedFreightTemplates  *AppliedFreightTemplate   `json:"appliedFreightTemplates"`  // 应用的运费模板信息
	FreightChargesTotal      float64                   `json:"freightChargesTotal"`      // 运费明细总费用 (来自运单直接计费)
	AdjustmentChargesTotal   float64                   `json:"adjustmentChargesTotal"`   // 调整明细总费用 (来自财务调整)
	TotalAmount              float64                   `json:"totalAmount"`              // 账单总金额
	AmountPaid               float64                   `json:"amountPaid"`               // 已付金额
	BalanceDue               float64                   `json:"balanceDue"`               // 应付余额
	Currency                 string                    `json:"currency"`                 // 货币单位
	Status                   BillingStatus             `json:"status"`                   // 账单状态
	PaymentMethod            *string                   `json:"paymentMethod,omitempty"`  // 支付方式
	PaymentTransactionID     *string                   `json:"paymentTransactionId,omitempty"` // 支付交易号
	PaymentDate              *time.Time                `json:"paymentDate,omitempty"`    // 支付日期
	Notes                    *string                   `json:"notes,omitempty"`          // 账单备注
	GeneratedByUserID        *int64                    `json:"generatedByUserId,omitempty"` // 账单生成操作员ID
	CreateTime               time.Time                 `json:"createTime"`               // 记录创建时间
	UpdateTime               time.Time                 `json:"updateTime"`               // 记录更新时间
	
	// 关联的明细项
	Items                    []*BillingRecordItem      `json:"items,omitempty"`          // 账单明细
}

// BillingRecordItem 账单明细实体
type BillingRecordItem struct {
	ID                        int64     `json:"id"`                        // 账单明细主键ID
	BillingRecordID           int64     `json:"billingRecordId"`           // 所属账单ID
	ManifestID                *int64    `json:"manifestId,omitempty"`      // 原始运单ID
	ExpressNumber             *string   `json:"expressNumber,omitempty"`   // 快递单号
	OrderNo                   *string   `json:"orderNo,omitempty"`         // 系统订单号
	TransferredTrackingNumber *string   `json:"transferredTrackingNumber,omitempty"` // 转单号
	OrderNumber               *string   `json:"orderNumber,omitempty"`     // 客户订单号
	ManifestCreateTime        *time.Time `json:"manifestCreateTime,omitempty"` // 运单创建时间
	ShipmentTime              *time.Time `json:"shipmentTime,omitempty"`    // 发货时间
	ReceiverName              *string   `json:"receiverName,omitempty"`    // 收件人姓名
	ItemDescription           string    `json:"itemDescription"`           // 物品名称
	CargoType                 CargoType `json:"cargoType"`                 // 货物类型
	Weight                    *float64  `json:"weight,omitempty"`          // 实际重量(KG)
	Length                    *float64  `json:"length,omitempty"`          // 长(cm)
	Width                     *float64  `json:"width,omitempty"`           // 宽(cm)
	Height                    *float64  `json:"height,omitempty"`          // 高(cm)
	SumOfSides                *float64  `json:"sumOfSides,omitempty"`      // 三边和(cm)
	DimensionalWeight         *float64  `json:"dimensionalWeight,omitempty"` // 体积重(KG)
	ChargeableWeight          *float64  `json:"chargeableWeight,omitempty"` // 计费重量(KG)
	BaseFreightFee            *float64  `json:"baseFreightFee,omitempty"`   // 基础运费
	FirstWeightFee            *float64  `json:"firstWeightFee,omitempty"`   // 首重费用
	ContinuedWeightFee        *float64  `json:"continuedWeightFee,omitempty"` // 续重费用
	OverLengthSurcharge       *float64  `json:"overLengthSurcharge,omitempty"` // 超长费
	RemoteAreaSurcharge       *float64  `json:"remoteAreaSurcharge,omitempty"` // 偏远费
	OverweightSurcharge       *float64  `json:"overweightSurcharge,omitempty"` // 超重费
	ItemTotalAmount           float64   `json:"itemTotalAmount"`           // 总费用
	CreateTime                time.Time `json:"createTime"`                // 创建时间
	UpdateTime                time.Time `json:"updateTime"`                // 更新时间
	
	// 关联的财务调整快照
	FinancialAdjustmentSnapshots []*BillingFinancialAdjustmentSnapshot `json:"financialAdjustmentSnapshots,omitempty"`
}

// BillingFinancialAdjustmentSnapshot 账单中财务调整的快照实体
type BillingFinancialAdjustmentSnapshot struct {
	ID                   int64                  `json:"id"`                   // 快照主键ID
	BillingRecordID      int64                  `json:"billingRecordId"`      // 关联的账单ID
	OriginalAdjustmentID int64                  `json:"originalAdjustmentId"` // 原始财务调整ID
	
	// 财务调整信息快照
	AdjustmentType       string                 `json:"adjustmentType"`       // 调整类型
	Description          *string                `json:"description,omitempty"` // 调整描述/原因
	AdditionalDetails    map[string]interface{} `json:"additionalDetails,omitempty"` // 特定调整类型的附加详情
	Amount               float64                `json:"amount"`               // 调整金额
	Currency             string                 `json:"currency"`             // 货币单位
	EffectiveDate        time.Time              `json:"effectiveDate"`        // 费用实际发生/确认日期
	
	// 关联运单信息快照
	ManifestID                      *int64     `json:"manifestId,omitempty"`
	ManifestExpressNumber           *string    `json:"manifestExpressNumber,omitempty"`
	ManifestOrderNo                 *string    `json:"manifestOrderNo,omitempty"`
	ManifestTransferredTrackingNumber *string  `json:"manifestTransferredTrackingNumber,omitempty"`
	ManifestCustomerOrderNumber     *string    `json:"manifestCustomerOrderNumber,omitempty"`
	ManifestCreateTime              *time.Time `json:"manifestCreateTime,omitempty"`
	ManifestShipmentTime            *time.Time `json:"manifestShipmentTime,omitempty"`
	ManifestReceiverName            *string    `json:"manifestReceiverName,omitempty"`
	ManifestItemDescription         *string    `json:"manifestItemDescription,omitempty"`
	ManifestCargoType               *string    `json:"manifestCargoType,omitempty"`
	
	// 运单尺寸重量信息快照
	ManifestWeight              *float64 `json:"manifestWeight,omitempty"`
	ManifestLength              *float64 `json:"manifestLength,omitempty"`
	ManifestWidth               *float64 `json:"manifestWidth,omitempty"`
	ManifestHeight              *float64 `json:"manifestHeight,omitempty"`
	ManifestSumOfSides          *float64 `json:"manifestSumOfSides,omitempty"`
	ManifestDimensionalWeight   *float64 `json:"manifestDimensionalWeight,omitempty"`
	ManifestChargeableWeight    *float64 `json:"manifestChargeableWeight,omitempty"`
	
	// 快照创建时间
	SnapshotTime         time.Time `json:"snapshotTime"`         // 快照创建时间
}