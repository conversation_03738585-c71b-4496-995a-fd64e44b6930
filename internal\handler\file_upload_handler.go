package handler

import (
	"net/http"
	"zebra-hub-system/internal/domain/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// FileUploadHandler 文件上传处理器
type FileUploadHandler struct {
	fileUploadService service.FileUploadService
	logger           *zap.Logger
}

// NewFileUploadHandler 创建文件上传处理器
func NewFileUploadHandler(
	fileUploadService service.FileUploadService,
	logger *zap.Logger,
) *FileUploadHandler {
	return &FileUploadHandler{
		fileUploadService: fileUploadService,
		logger:           logger,
	}
}

// UploadImageRequest 上传图片请求
type UploadImageRequest struct {
	BusinessType string `form:"businessType" binding:"required"` // 业务类型
}

// UploadImage 上传图片
// @Summary 上传图片
// @Description 上传图片文件到阿里云OSS，支持jpg、png、gif等格式，最大10MB
// @Tags 文件上传
// @Accept multipart/form-data
// @Produce json
// @Param businessType formData string true "业务类型" Enums(compensation_proof,adjustment_proof)
// @Param file formData file true "图片文件"
// @Success 200 {object} valueobject.Response{data=service.UploadFileResponse} "上传成功"
// @Failure 400 {object} valueobject.Response "参数错误"
// @Failure 500 {object} valueobject.Response "服务器错误"
// @Router /api/v1/upload/image [post]
func (h *FileUploadHandler) UploadImage(c *gin.Context) {
	// 1. 绑定请求参数
	var req UploadImageRequest
	if err := c.ShouldBind(&req); err != nil {
		h.logger.Warn("上传图片参数绑定失败", zap.Error(err))
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 2. 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		h.logger.Warn("获取上传文件失败", zap.Error(err))
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请选择要上传的文件", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// 3. 构建上传请求
	uploadReq := &service.UploadFileRequest{
		FileName:     header.Filename,
		FileSize:     header.Size,
		ContentType:  header.Header.Get("Content-Type"),
		FileData:     file,
		BusinessType: req.BusinessType,
	}

	// 4. 调用上传服务
	response, errorCode, err := h.fileUploadService.UploadImage(c.Request.Context(), uploadReq)
	if err != nil {
		h.logger.Error("上传图片失败",
			zap.String("fileName", header.Filename),
			zap.String("businessType", req.BusinessType),
			zap.Int("errorCode", errorCode),
			zap.Error(err))

		util.ResponseError(c, errorCode, err.Error(), http.StatusInternalServerError)
		return
	}

	// 5. 返回成功响应
	h.logger.Info("图片上传成功",
		zap.String("fileName", header.Filename),
		zap.String("fileUrl", response.FileUrl),
		zap.String("businessType", req.BusinessType))

	util.ResponseSuccess(c, response)
}

// DeleteImage 删除图片
// @Summary 删除图片
// @Description 删除已上传的图片文件
// @Tags 文件上传
// @Accept json
// @Produce json
// @Param fileUrl query string true "文件URL"
// @Success 200 {object} util.Response "删除成功"
// @Failure 400 {object} util.Response "参数错误"
// @Failure 500 {object} util.Response "服务器错误"
// @Router /api/v1/upload/image [delete]
func (h *FileUploadHandler) DeleteImage(c *gin.Context) {
	// 1. 获取文件URL参数
	fileUrl := c.Query("fileUrl")
	if fileUrl == "" {
		util.ResponseError(c, valueobject.ERROR_PARAMETER_MISSING, "缺少文件URL参数", http.StatusBadRequest)
		return
	}

	// 2. 调用删除服务
	if err := h.fileUploadService.DeleteFile(c.Request.Context(), fileUrl); err != nil {
		h.logger.Error("删除图片失败",
			zap.String("fileUrl", fileUrl),
			zap.Error(err))

		util.ResponseError(c, valueobject.ERROR_UNKNOWN, "删除文件失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 3. 返回成功响应
	h.logger.Info("图片删除成功", zap.String("fileUrl", fileUrl))
	util.ResponseSuccess(c, gin.H{
		"message": "文件删除成功",
	})
}

// GetUploadInfo 获取上传配置信息
// @Summary 获取上传配置信息
// @Description 获取文件上传的限制信息，如支持的格式、最大文件大小等
// @Tags 文件上传
// @Produce json
// @Success 200 {object} util.Response{data=map[string]interface{}} "获取成功"
// @Router /api/v1/upload/info [get]
func (h *FileUploadHandler) GetUploadInfo(c *gin.Context) {
	info := gin.H{
		"maxFileSize":     "10MB",
		"supportedTypes":  []string{"jpg", "jpeg", "png", "gif", "bmp", "webp"},
		"businessTypes":   []string{"compensation_proof", "adjustment_proof"},
		"uploadEndpoint":  "/api/v1/upload/image",
		"deleteEndpoint":  "/api/v1/upload/image",
	}

	util.ResponseSuccess(c, info)
} 