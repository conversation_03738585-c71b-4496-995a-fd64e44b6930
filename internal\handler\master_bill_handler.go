package handler

import (
	"net/http"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// MasterBillHandler 主提单相关处理器
type MasterBillHandler struct {
	masterBillService service.MasterBillService
}

// NewMasterBillHandler 创建主提单处理器
func NewMasterBillHandler(masterBillService service.MasterBillService) *MasterBillHandler {
	return &MasterBillHandler{
		masterBillService: masterBillService,
	}
}

// ListMasterBills godoc
// @Summary 获取主提单列表
// @Description 获取主提单列表数据，用于前端下拉框
// @Tags MasterBills
// @Accept json
// @Produce json
// @Param keyword query string false "关键字(提单号)"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(20)
// @Success 200 {object} util.Response{data=service.ListMasterBillsResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /master-bills/options [get]
// @Security ApiKeyAuth
func (h *MasterBillHandler) ListMasterBills(c *gin.Context) {
	var req service.ListMasterBillsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层方法
	resp, code, err := h.masterBillService.ListMasterBills(c, &req)
	if err != nil {
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}
	
	// 返回成功响应
	util.ResponseSuccess(c, resp)
} 