#!/bin/bash

# 启用BuildKit进行快速构建
export DOCKER_BUILDKIT=1

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 [服务名]"
    echo ""
    echo "可用的服务:"
    echo "  api-server      - 构建API服务器"
    echo "  billing-consumer - 构建账单消费者"
    echo "  all             - 构建所有服务"
    echo ""
    echo "示例:"
    echo "  $0 api-server"
    echo "  $0 billing-consumer"
    echo "  $0 all"
}

# 构建指定服务
build_service() {
    local service=$1
    echo -e "${YELLOW}正在构建 $service...${NC}"
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 执行构建
    if docker-compose -f docker-compose.simple.yml build --no-cache $service; then
        # 计算构建时间
        end_time=$(date +%s)
        build_time=$((end_time - start_time))
        
        echo -e "${GREEN}✅ $service 构建成功！耗时: ${build_time}秒${NC}"
        
        # 重启服务
        echo -e "${YELLOW}正在重启 $service...${NC}"
        docker-compose -f docker-compose.simple.yml up -d $service
        
        # 显示日志
        echo -e "${YELLOW}显示 $service 的最新日志:${NC}"
        docker-compose -f docker-compose.simple.yml logs --tail=20 $service
        
    else
        echo -e "${RED}❌ $service 构建失败！${NC}"
        return 1
    fi
}

# 主逻辑
case "$1" in
    "api-server")
        build_service "api-server"
        ;;
    "billing-consumer")
        build_service "billing-consumer"
        ;;
    "all")
        echo -e "${YELLOW}构建所有服务...${NC}"
        build_service "api-server"
        if [ $? -eq 0 ]; then
            build_service "billing-consumer"
        fi
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    "")
        echo -e "${RED}错误: 请指定要构建的服务${NC}"
        echo ""
        show_help
        exit 1
        ;;
    *)
        echo -e "${RED}错误: 未知的服务 '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac 