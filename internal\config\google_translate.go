package config

import (
	"context"
	"zebra-hub-system/internal/adapter/external_service"
	"zebra-hub-system/internal/domain/service"

	"go.uber.org/zap"
)

// InitGoogleTranslateService 初始化Google翻译服务
func InitGoogleTranslateService(ctx context.Context, cfg *GoogleTranslate, logger *zap.Logger) (service.TranslateService, error) {
	// 如果翻译服务未启用，则返回nil
	if !cfg.Enabled {
		logger.Info("Google Translate service is disabled")
		return nil, nil
	}

	// 验证配置
	// 如果没有提供JSON密钥文件，则检查API Key
	if cfg.CredentialFile == "" && cfg.APIKey == "" {
		logger.Warn("Neither credential file nor API key is configured for Google Translate")
		return nil, nil
	}

	// 如果使用JSON密钥文件，项目ID可以从文件中获取
	if cfg.CredentialFile != "" && cfg.Project == "" {
		logger.Info("Project ID is not specified, it will be extracted from credential file")
	} else if cfg.Project == "" {
		logger.Warn("Google Cloud project ID is not configured")
		return nil, nil
	}

	// 创建Google翻译服务
	translateService, err := external_service.NewGoogleTranslateService(ctx, cfg.APIKey, cfg.Project, cfg.CredentialFile)
	if err != nil {
		logger.Error("Failed to initialize Google Translate service", zap.Error(err))
		return nil, err
	}

	logger.Info("Google Translate service initialized successfully", 
		zap.Bool("using_credential_file", cfg.CredentialFile != ""))
	return translateService, nil
} 