# 财务模块财务调整快照表结构升级修改说明

## 修改背景

根据业务需求，`billing_financial_adjustment_snapshots` 表结构进行了重大升级，增加了详尽的运单信息快照字段，并建立了与账单记录的一对一关系。

## 新表结构特点

### 1. 关联关系变化

**修改前**:

- 快照记录独立存在，通过时间范围和用户 ID 关联

**修改后**:

- 每个账单记录对应一个财务调整快照记录（一对一关系）
- 通过 `billing_record_id` 字段建立唯一关联

### 2. 新增运单信息快照字段

新表结构包含了完整的运单信息快照，确保财务调整的上下文信息完整保存：

#### 基本运单信息

- `manifest_id` - 原始运单 ID
- `manifest_express_number` - 快递单号
- `manifest_order_no` - 系统订单号
- `manifest_transferred_tracking_number` - 转单号
- `manifest_customer_order_number` - 客户订单号
- `manifest_create_time` - 运单创建时间
- `manifest_shipment_time` - 发货时间
- `manifest_receiver_name` - 收件人姓名

#### 货物信息

- `manifest_item_description` - 物品描述
- `manifest_cargo_type` - 货物类型

#### 尺寸重量信息

- `manifest_weight` - 实际重量(KG)
- `manifest_length` - 长(cm)
- `manifest_width` - 宽(cm)
- `manifest_height` - 高(cm)
- `manifest_sum_of_sides` - 三边和(cm)
- `manifest_dimensional_weight` - 体积重(KG)
- `manifest_chargeable_weight` - 计费重量(KG)

#### 费用构成信息

- `manifest_base_freight_fee` - 基础运费
- `manifest_first_weight_fee` - 首重费用
- `manifest_continued_weight_fee` - 续重费用
- `manifest_over_length_surcharge` - 超长费
- `manifest_remote_area_surcharge` - 偏远费
- `manifest_original_total_fee` - 原始总费用

## 代码修改内容

### 1. 数据库模型更新

**文件**: `internal/adapter/persistence/model/billing_model.go`

**主要变化**:

- 添加 `BillingRecordID` 字段建立与账单的关联
- 新增所有运单信息快照字段
- 更新字段类型和约束

```go
type BillingFinancialAdjustmentSnapshotPO struct {
    ID                   int64                  `gorm:"column:id;primaryKey;autoIncrement"`
    BillingRecordID      int64                  `gorm:"column:billing_record_id;uniqueIndex;not null"`
    OriginalAdjustmentID int64                  `gorm:"column:original_adjustment_id;not null"`

    // 财务调整信息快照
    AdjustmentType       string                 `gorm:"column:adjustment_type;not null"`
    // ... 其他调整信息字段

    // 关联运单信息快照
    ManifestID                      *int64     `gorm:"column:manifest_id"`
    ManifestExpressNumber           *string    `gorm:"column:manifest_express_number"`
    // ... 其他运单信息字段
}
```

### 2. 领域实体更新

**文件**: `internal/domain/entity/billing_record.go`

**主要变化**:

- 在 `BillingFinancialAdjustmentSnapshot` 实体中添加对应的字段
- 保持与数据库模型的一致性

### 3. 仓储层修改

**文件**: `internal/adapter/persistence/billing_repository_impl.go`

#### 新增方法

- `FindManifestsByIDs` - 根据运单 ID 列表批量查询运单信息

#### 更新方法

- `SaveBillingFinancialAdjustmentSnapshots` - 支持保存新的快照字段

### 4. 服务层逻辑重构

**文件**: `internal/app/service/billing_service.go`

#### 重构 `generateFinancialAdjustmentSnapshots` 方法

**修改前**:

```go
func (s *BillingServiceImpl) generateFinancialAdjustmentSnapshots(
    ctx context.Context,
    billingItems []*entity.BillingRecordItem,
    userID int64,
    startDate, endDate time.Time
) error
```

**修改后**:

```go
func (s *BillingServiceImpl) generateFinancialAdjustmentSnapshots(
    ctx context.Context,
    billingRecordID int64,
    userID int64,
    startDate, endDate time.Time
) error
```

#### 新增功能

1. **批量查询运单信息**: 根据财务调整记录中的运单 ID 批量查询完整的运单信息
2. **完整快照生成**: 为每个财务调整记录生成包含完整运单信息的快照
3. **智能字段填充**: 自动计算和填充衍生字段（如三边和、计费重量等）

### 5. 仓储接口扩展

**文件**: `internal/domain/repository/billing_repository.go`

**新增方法**:

```go
// FindManifestsByIDs 根据运单ID列表批量查询运单信息
FindManifestsByIDs(ctx context.Context, manifestIDs []int64) ([]*entity.Manifest, error)
```

## 业务逻辑优化

### 1. 快照生成流程

**新的生成流程**:

1. 查询指定时间范围内的财务调整记录
2. 收集所有相关的运单 ID
3. 批量查询运单的完整信息（包括物品信息）
4. 为每个财务调整记录生成包含完整运单上下文的快照
5. 计算衍生字段（三边和、计费重量、货物类型等）
6. 保存快照到数据库

### 2. 数据完整性保障

- **运单信息完整性**: 快照包含运单的所有关键信息
- **费用计算准确性**: 保存原始费用构成，便于后续分析
- **货物类型识别**: 根据运单的运费模板类型正确判断货物类型
- **时间信息保留**: 保存运单的创建时间和发货时间

### 3. 性能优化

- **批量查询**: 使用批量查询减少数据库访问次数
- **内存映射**: 构建运单 ID 到运单实体的映射，提高查找效率
- **分批处理**: 支持大量快照记录的分批保存

## 数据迁移建议

### 1. 数据库结构迁移

```sql
-- 添加新字段到现有表
ALTER TABLE billing_financial_adjustment_snapshots
ADD COLUMN billing_record_id BIGINT UNIQUE NOT NULL COMMENT '关联的账单ID',
ADD COLUMN manifest_id BIGINT NULL COMMENT '原始运单ID',
ADD COLUMN manifest_express_number VARCHAR(64) NULL COMMENT '运单的快递单号',
-- ... 添加其他所有新字段

-- 添加索引
CREATE INDEX idx_snap_original_adj ON billing_financial_adjustment_snapshots(original_adjustment_id);
CREATE INDEX idx_snap_manifest_id_val ON billing_financial_adjustment_snapshots(manifest_id);
```

### 2. 数据迁移脚本

建议编写数据迁移脚本，为现有的快照记录补充运单信息：

1. 查询现有的快照记录
2. 根据原始调整记录 ID 查找对应的运单信息
3. 更新快照记录，填充运单信息字段

## 验证结果

- ✅ 代码编译成功
- ✅ 新表结构支持完整的运单信息快照
- ✅ 与账单记录建立了一对一关系
- ✅ 批量查询和处理逻辑正确实现
- ✅ 保持了向后兼容性

## 相关文件

- `internal/adapter/persistence/model/billing_model.go` - 数据库模型
- `internal/domain/entity/billing_record.go` - 领域实体
- `internal/adapter/persistence/billing_repository_impl.go` - 仓储实现
- `internal/domain/repository/billing_repository.go` - 仓储接口
- `internal/app/service/billing_service.go` - 业务服务

## 测试建议

1. **单元测试**: 验证快照生成逻辑的正确性
2. **集成测试**: 测试完整的账单生成流程
3. **性能测试**: 验证批量处理的性能表现
4. **数据一致性测试**: 确保快照数据与原始数据的一致性
