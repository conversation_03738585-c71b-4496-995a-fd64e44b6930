#!/bin/bash

# Docker部署脚本
# 用法: ./scripts/deploy-docker.sh [simple|full] [up|down|restart]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 默认参数
COMPOSE_FILE="docker-compose.yml"
ACTION="up"

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        "simple")
            COMPOSE_FILE="docker-compose.simple.yml"
            print_info "使用简化版docker-compose配置（无数据库和消息队列）"
            ;;
        "full")
            COMPOSE_FILE="docker-compose.yml"
            print_info "使用完整版docker-compose配置（包含数据库和消息队列）"
            ;;
        "up"|"down"|"restart")
            ACTION="$1"
            ;;
        *)
            print_error "未知参数: $1"
            echo "用法: $0 [simple|full] [up|down|restart]"
            echo "  simple  - 使用简化版配置（仅应用服务）"
            echo "  full    - 使用完整版配置（包含数据库和消息队列）"
            echo "  up      - 启动服务（默认）"
            echo "  down    - 停止服务"
            echo "  restart - 重启服务"
            exit 1
            ;;
    esac
    shift
done

print_info "使用配置文件: $COMPOSE_FILE"
print_info "执行操作: $ACTION"

# 检查配置文件是否存在
if [ ! -f "$COMPOSE_FILE" ]; then
    print_error "配置文件 $COMPOSE_FILE 不存在"
    exit 1
fi

# 执行操作
case "$ACTION" in
    "up")
        print_info "启动服务..."
        docker-compose -f "$COMPOSE_FILE" up -d
        print_success "服务启动完成"
        ;;
    "down")
        print_info "停止服务..."
        docker-compose -f "$COMPOSE_FILE" down
        print_success "服务停止完成"
        ;;
    "restart")
        print_info "重启服务..."
        docker-compose -f "$COMPOSE_FILE" down
        docker-compose -f "$COMPOSE_FILE" up -d
        print_success "服务重启完成"
        ;;
esac

# 显示服务状态
print_info "当前服务状态："
docker-compose -f "$COMPOSE_FILE" ps 