# 运费模板管理 API 接口文档

## 概述

运费模板管理模块提供运费模板的增删改查功能，支持多种模板类型的管理。

**基础路径**: `/api/v1/shipping-fee-templates`

**认证方式**: <PERSON><PERSON> (JWT)

**支持的模板类型**:

- `1` - 普通模板
- `2` - 带电模板
- `3` - 投函模板
- `6` - 特殊模板

---

## 接口列表

### 1. 获取所有运费模板

获取系统中的所有运费模板列表。

**请求信息**:

- **方法**: `GET`
- **路径**: `/api/v1/shipping-fee-templates`
- **认证**: 需要

**请求参数**: 无

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "templates": [
      {
        "id": 1,
        "name": "标准普通模板",
        "type": 1,
        "typeName": "普通模板",
        "firstWeightPrice": 12.0,
        "firstWeightRange": 1.0,
        "continuedWeightPrice": 6.0,
        "continuedWeightInterval": 0.5,
        "bulkCoefficient": 5000,
        "threeSidesStart": 60.0,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

---

### 2. 根据类型获取运费模板

根据指定类型获取对应的运费模板列表。

**请求信息**:

- **方法**: `GET`
- **路径**: `/api/v1/shipping-fee-templates/type/{type}`
- **认证**: 需要

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | int | 是 | 模板类型：1-普通模板，2-带电模板，3-投函模板，6-特殊模板 |

**请求示例**: `GET /api/v1/shipping-fee-templates/type/1`

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "type": 1,
    "typeName": "普通模板",
    "templates": [
      {
        "id": 1,
        "name": "标准普通模板",
        "type": 1,
        "typeName": "普通模板",
        "firstWeightPrice": 12.0,
        "firstWeightRange": 1.0,
        "continuedWeightPrice": 6.0,
        "continuedWeightInterval": 0.5,
        "bulkCoefficient": 5000,
        "threeSidesStart": 60.0,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

---

### 3. 根据 ID 获取运费模板

根据模板 ID 获取运费模板详情。

**请求信息**:

- **方法**: `GET`
- **路径**: `/api/v1/shipping-fee-templates/{id}`
- **认证**: 需要

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 模板 ID |

**请求示例**: `GET /api/v1/shipping-fee-templates/1`

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "id": 1,
    "name": "标准普通模板",
    "type": 1,
    "typeName": "普通模板",
    "firstWeightPrice": 12.0,
    "firstWeightRange": 1.0,
    "continuedWeightPrice": 6.0,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 5000,
    "threeSidesStart": 60.0,
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-01 10:00:00"
  }
}
```

---

### 4. 创建运费模板

创建新的运费模板。

**请求信息**:

- **方法**: `POST`
- **路径**: `/api/v1/shipping-fee-templates`
- **认证**: 需要
- **Content-Type**: `application/json`

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| name | string | 是 | 模板名称 | "测试模板" |
| firstWeightPrice | float64 | 是 | 首重价格 | 10.5 |
| firstWeightRange | float64 | 是 | 首重范围(kg) | 1.0 |
| continuedWeightPrice | float64 | 是 | 续重价格 | 5.5 |
| continuedWeightInterval | float64 | 是 | 续重计费间隔(kg) | 0.5 |
| bulkCoefficient | int | 是 | 轻抛系数 | 5000 |
| threeSidesStart | float64 | 是 | 三边和阈值(cm) | 60.0 |
| type | int | 是 | 模板类型：1=普通模板 2=带电模板 3=投函模板 6=特殊模板 | 1 |

**请求示例**:

```json
{
  "name": "新建普通模板",
  "firstWeightPrice": 12.0,
  "firstWeightRange": 1.0,
  "continuedWeightPrice": 6.0,
  "continuedWeightInterval": 0.5,
  "bulkCoefficient": 5000,
  "threeSidesStart": 60.0,
  "type": 1
}
```

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "id": 2,
    "name": "新建普通模板",
    "type": 1,
    "typeName": "普通模板",
    "firstWeightPrice": 12.0,
    "firstWeightRange": 1.0,
    "continuedWeightPrice": 6.0,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 5000,
    "threeSidesStart": 60.0,
    "createTime": "2024-01-15 14:30:00",
    "updateTime": "2024-01-15 14:30:00"
  }
}
```

---

### 5. 更新运费模板

更新现有的运费模板。

**请求信息**:

- **方法**: `PUT`
- **路径**: `/api/v1/shipping-fee-templates`
- **认证**: 需要
- **Content-Type**: `application/json`

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| id | int64 | 是 | 模板 ID | 1 |
| name | string | 是 | 模板名称 | "更新后的模板" |
| firstWeightPrice | float64 | 是 | 首重价格 | 10.5 |
| firstWeightRange | float64 | 是 | 首重范围(kg) | 1.0 |
| continuedWeightPrice | float64 | 是 | 续重价格 | 5.5 |
| continuedWeightInterval | float64 | 是 | 续重计费间隔(kg) | 0.5 |
| bulkCoefficient | int | 是 | 轻抛系数 | 5000 |
| threeSidesStart | float64 | 是 | 三边和阈值(cm) | 60.0 |
| type | int | 是 | 模板类型：1=普通模板 2=带电模板 3=投函模板 6=特殊模板 | 1 |

**请求示例**:

```json
{
  "id": 1,
  "name": "更新后的普通模板",
  "firstWeightPrice": 15.0,
  "firstWeightRange": 1.0,
  "continuedWeightPrice": 7.0,
  "continuedWeightInterval": 0.5,
  "bulkCoefficient": 5000,
  "threeSidesStart": 60.0,
  "type": 1
}
```

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "id": 1,
    "name": "更新后的普通模板",
    "type": 1,
    "typeName": "普通模板",
    "firstWeightPrice": 15.0,
    "firstWeightRange": 1.0,
    "continuedWeightPrice": 7.0,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 5000,
    "threeSidesStart": 60.0,
    "createTime": "2024-01-01 10:00:00",
    "updateTime": "2024-01-15 14:45:00"
  }
}
```

---

### 6. 删除运费模板

删除指定的运费模板。如果模板正在被用户使用，则无法删除。

**请求信息**:

- **方法**: `DELETE`
- **路径**: `/api/v1/shipping-fee-templates/{id}`
- **认证**: 需要

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int64 | 是 | 模板 ID |

**请求示例**: `DELETE /api/v1/shipping-fee-templates/1`

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": null
}
```

---

## 数据结构

### ShippingFeeTemplateDTO

运费模板数据传输对象。

| 字段名                  | 类型    | 说明                                                     |
| ----------------------- | ------- | -------------------------------------------------------- |
| id                      | int64   | 模板 ID                                                  |
| name                    | string  | 模板名称                                                 |
| type                    | int     | 模板类型：1-普通模板；2-带电模板；3-投函模板；6-特殊模板 |
| typeName                | string  | 模板类型名称                                             |
| firstWeightPrice        | float64 | 首重价格                                                 |
| firstWeightRange        | float64 | 首重范围(kg)                                             |
| continuedWeightPrice    | float64 | 续重价格                                                 |
| continuedWeightInterval | float64 | 续重区间大小(kg)                                         |
| bulkCoefficient         | int     | 轻抛系数                                                 |
| threeSidesStart         | float64 | 三边和超过多少开始计算体积重量(cm)                       |
| createTime              | string  | 创建时间，格式：yyyy-MM-dd HH:mm:ss                      |
| updateTime              | string  | 更新时间，格式：yyyy-MM-dd HH:mm:ss                      |

---

## 错误码说明

| 错误码 | 说明              | HTTP 状态码 |
| ------ | ----------------- | ----------- |
| 100000 | 操作成功          | 200         |
| 100001 | 未知错误/系统繁忙 | 500         |
| 100002 | 无效参数          | 400         |
| 100006 | 请求的资源未找到  | 404         |

## 错误响应示例

### 参数错误

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "参数格式错误: Key: 'CreateShippingFeeTemplateRequest.Name' Error:Field validation for 'Name' failed on the 'required' tag",
  "data": null
}
```

### 资源不存在

```json
{
  "success": false,
  "errorCode": 100006,
  "errorMessage": "运费模板不存在: ID=999",
  "data": null
}
```

### 模板正在使用

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "运费模板正在被用户使用，无法删除: ID=1",
  "data": null
}
```

### 服务器内部错误

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "创建运费模板失败: 数据库连接异常",
  "data": null
}
```

---

## 注意事项

1. **认证**: 所有接口都需要在请求头中包含有效的 JWT Token

   ```
   Authorization: Bearer <your-jwt-token>
   ```

2. **模板类型**: 仅支持类型 1、2、3、6，其他类型会返回参数错误

3. **数据验证**: 所有必填字段都会进行验证，缺失时会返回详细的错误信息

4. **幂等性**:

   - GET 请求是幂等的
   - PUT 请求是幂等的
   - DELETE 请求是幂等的
   - POST 请求不是幂等的

5. **并发安全**: 系统支持并发操作，但建议在更新操作时注意版本控制

6. **性能**:

   - 获取列表接口支持大量数据，建议根据实际需求使用分页
   - 删除操作会立即生效，请谨慎操作

7. **删除限制**:
   - 如果运费模板正在被用户使用，则无法删除
   - 系统会自动检查 `tb_shipping_fee_template_user` 表中的关联关系
   - 建议在删除前先解除所有用户与该模板的关联

---

_文档版本: v1.0_  
_更新时间: 2024-01-15_
