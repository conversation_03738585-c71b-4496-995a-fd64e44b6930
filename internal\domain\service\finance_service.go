package service

import (
	"context"
	"zebra-hub-system/internal/domain/dto"
)

// FinanceService 财务服务接口
type FinanceService interface {
	// GenerateShippingBillExcel 生成运费账单Excel
	GenerateShippingBillExcel(ctx context.Context, req dto.ShippingBillExportRequest) ([]byte, string, error)
	
	// GenerateBillingRecordExcel 根据账单记录ID生成Excel
	GenerateBillingRecordExcel(ctx context.Context, req dto.BillingRecordExportRequest) ([]byte, string, error)
	
	// BatchGenerateBillingRecordExcel 批量生成账单记录Excel并打包成zip文件
	BatchGenerateBillingRecordExcel(ctx context.Context, req dto.BatchBillingRecordExportRequest) ([]byte, string, error)
} 