package entity

import (
	"time"
)

// BillingCycleStatus 账期批次状态
type BillingCycleStatus string

const (
	BillingCycleStatusPending    BillingCycleStatus = "PENDING"    // 待处理
	BillingCycleStatusProcessing BillingCycleStatus = "PROCESSING" // 处理中
	BillingCycleStatusCompleted  BillingCycleStatus = "COMPLETED"  // 已完成
	BillingCycleStatusFailed     BillingCycleStatus = "FAILED"     // 失败
	BillingCycleStatusCancelled  BillingCycleStatus = "CANCELLED"  // 已取消
)

// BillingCycle 账期批次实体
type BillingCycle struct {
	ID                        int64              `json:"id"`                        // 账期批次ID
	CycleYear                 int                `json:"cycleYear"`                 // 账期年份
	CycleMonth                int                `json:"cycleMonth"`                // 账期月份 (1-12)
	CycleName                 *string            `json:"cycleName,omitempty"`       // 账期名称
	Status                    BillingCycleStatus `json:"status"`                    // 账期批次状态
	TotalCustomersBilled      *int               `json:"totalCustomersBilled,omitempty"`      // 本周期内出账客户数
	TotalBillsGenerated       *int               `json:"totalBillsGenerated,omitempty"`       // 本周期内生成的账单总数
	TotalBilledAmount         *float64           `json:"totalBilledAmount,omitempty"`         // 本周期内账单总金额合计
	TotalAmountPaidInCycle    *float64           `json:"totalAmountPaidInCycle,omitempty"`    // 本周期内已付金额合计
	TotalBalanceDueInCycle    *float64           `json:"totalBalanceDueInCycle,omitempty"`    // 本周期内待支付金额合计
	GeneratedByUserID         *int64             `json:"generatedByUserId,omitempty"`         // 批次生成操作员ID
	GenerationStartTime       *time.Time         `json:"generationStartTime,omitempty"`       // 批次账单开始生成时间
	GenerationEndTime         *time.Time         `json:"generationEndTime,omitempty"`         // 批次账单完成生成时间
	Notes                     *string            `json:"notes,omitempty"`                     // 批次备注
	CreateTime                time.Time          `json:"createTime"`                          // 记录创建时间
	UpdateTime                time.Time          `json:"updateTime"`                          // 记录更新时间
} 