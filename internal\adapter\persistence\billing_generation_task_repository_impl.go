package persistence

import (
	"context"
	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// BillingGenerationTaskRepositoryImpl 账单生成任务仓储实现
type BillingGenerationTaskRepositoryImpl struct {
	db *gorm.DB
}

// NewBillingGenerationTaskRepository 创建账单生成任务仓储实例
func NewBillingGenerationTaskRepository(db *gorm.DB) repository.BillingGenerationTaskRepository {
	return &BillingGenerationTaskRepositoryImpl{
		db: db,
	}
}

// SaveTask 保存任务
func (r *BillingGenerationTaskRepositoryImpl) SaveTask(ctx context.Context, task *entity.BillingGenerationTask) error {
	po := &model.BillingGenerationTaskPO{}
	po.FromEntity(task)

	// 检查是否已存在
	var existingTask model.BillingGenerationTaskPO
	result := r.db.WithContext(ctx).Where("task_id = ?", po.TaskID).First(&existingTask)
	
	if result.Error == gorm.ErrRecordNotFound {
		// 新增
		return r.db.WithContext(ctx).Create(po).Error
	} else if result.Error != nil {
		return result.Error
	} else {
		// 更新
		return r.db.WithContext(ctx).Save(po).Error
	}
}

// FindTaskByID 根据任务ID查询任务
func (r *BillingGenerationTaskRepositoryImpl) FindTaskByID(ctx context.Context, taskID string) (*entity.BillingGenerationTask, error) {
	var po model.BillingGenerationTaskPO
	
	if err := r.db.WithContext(ctx).Where("task_id = ?", taskID).First(&po).Error; err != nil {
		return nil, err
	}
	
	return po.ToEntity(), nil
}

// FindTasksByBillingCycleID 根据账期批次ID分页查询任务列表
func (r *BillingGenerationTaskRepositoryImpl) FindTasksByBillingCycleID(ctx context.Context, billingCycleID int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error) {
	var pos []*model.BillingGenerationTaskPO
	var total int64

	// 统计总数
	if err := r.db.WithContext(ctx).Model(&model.BillingGenerationTaskPO{}).
		Where("billing_cycle_id = ?", billingCycleID).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := r.db.WithContext(ctx).
		Where("billing_cycle_id = ?", billingCycleID).
		Order("submit_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&pos).Error; err != nil {
		return nil, 0, err
	}

	// 转换为实体
	tasks := make([]*entity.BillingGenerationTask, len(pos))
	for i, po := range pos {
		tasks[i] = po.ToEntity()
	}

	return tasks, total, nil
}

// FindTasksByUserID 根据用户ID分页查询用户提交的任务列表
func (r *BillingGenerationTaskRepositoryImpl) FindTasksByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error) {
	var pos []*model.BillingGenerationTaskPO
	var total int64

	// 统计总数
	if err := r.db.WithContext(ctx).Model(&model.BillingGenerationTaskPO{}).
		Where("submitted_by_user_id = ?", userID).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := r.db.WithContext(ctx).
		Where("submitted_by_user_id = ?", userID).
		Order("submit_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&pos).Error; err != nil {
		return nil, 0, err
	}

	// 转换为实体
	tasks := make([]*entity.BillingGenerationTask, len(pos))
	for i, po := range pos {
		tasks[i] = po.ToEntity()
	}

	return tasks, total, nil
}

// FindTasksByBillingCycleAndUser 根据账期批次ID和用户ID分页查询任务列表
func (r *BillingGenerationTaskRepositoryImpl) FindTasksByBillingCycleAndUser(ctx context.Context, billingCycleID int64, userID *int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error) {
	var pos []*model.BillingGenerationTaskPO
	var total int64

	query := r.db.WithContext(ctx).Model(&model.BillingGenerationTaskPO{}).
		Where("billing_cycle_id = ?", billingCycleID)

	// 如果指定了用户ID，添加用户过滤条件
	if userID != nil {
		query = query.Where("submitted_by_user_id = ?", *userID)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("submit_time DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&pos).Error; err != nil {
		return nil, 0, err
	}

	// 转换为实体
	tasks := make([]*entity.BillingGenerationTask, len(pos))
	for i, po := range pos {
		tasks[i] = po.ToEntity()
	}

	return tasks, total, nil
}

// UpdateTaskStatus 更新任务状态
func (r *BillingGenerationTaskRepositoryImpl) UpdateTaskStatus(ctx context.Context, taskID string, status entity.TaskStatus) error {
	return r.db.WithContext(ctx).Model(&model.BillingGenerationTaskPO{}).
		Where("task_id = ?", taskID).
		Update("status", string(status)).Error
}

// UpdateTaskProgress 更新任务进度
func (r *BillingGenerationTaskRepositoryImpl) UpdateTaskProgress(ctx context.Context, taskID string, progressPercentage int, itemsProcessedCount int) error {
	updates := map[string]interface{}{
		"progress_percentage":    progressPercentage,
		"items_processed_count": itemsProcessedCount,
	}
	
	return r.db.WithContext(ctx).Model(&model.BillingGenerationTaskPO{}).
		Where("task_id = ?", taskID).
		Updates(updates).Error
}

// DeleteTask 删除任务
func (r *BillingGenerationTaskRepositoryImpl) DeleteTask(ctx context.Context, taskID string) error {
	return r.db.WithContext(ctx).Where("task_id = ?", taskID).Delete(&model.BillingGenerationTaskPO{}).Error
} 