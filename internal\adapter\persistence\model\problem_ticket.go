package model

import (
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/util"
)

// ProblemTicketPO 问题运单工单持久化对象
type ProblemTicketPO struct {
	ID                int64      `gorm:"column:id;primaryKey;autoIncrement"`
	ManifestID        int64      `gorm:"column:manifest_id;not null"`
	TrackingNumber    string     `gorm:"column:tracking_number"`
	CustomerAccountID int64      `gorm:"column:customer_account_id"`
	ProblemTypeCode   string     `gorm:"column:problem_type_code;not null"`
	ProblemDescription string     `gorm:"column:problem_description"`
	Status           string      `gorm:"column:status;not null"`
	Priority         int         `gorm:"column:priority"`
	AssignedToUserID int64      `gorm:"column:assigned_to_user_id"`
	Remarks          string     `gorm:"column:remarks"`
	CreateTime       *time.Time `gorm:"column:create_time;not null"`
	UpdateTime       *time.Time `gorm:"column:update_time;not null"`
	ResolvedTime     *time.Time `gorm:"column:resolved_time"`
}

// TableName 表名
func (ProblemTicketPO) TableName() string {
	return "problem_manifest_tickets"
}

// ToEntity 转换为实体
func (po *ProblemTicketPO) ToEntity() *entity.ProblemTicket {
	return &entity.ProblemTicket{
		ID:                po.ID,
		ManifestID:        po.ManifestID,
		TrackingNumber:    po.TrackingNumber,
		CustomerAccountID: po.CustomerAccountID,
		ProblemTypeCode:   po.ProblemTypeCode,
		ProblemDescription: po.ProblemDescription,
		Status:           po.Status,
		Priority:         po.Priority,
		AssignedToUserID: po.AssignedToUserID,
		Remarks:          po.Remarks,
		CreateTime:       util.NewPointerFormattedTime(po.CreateTime),
		UpdateTime:       util.NewPointerFormattedTime(po.UpdateTime),
		ResolvedTime:     util.NewPointerFormattedTime(po.ResolvedTime),
	}
} 