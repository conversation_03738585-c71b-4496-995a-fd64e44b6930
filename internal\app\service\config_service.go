package service

import (
	"context"
	"errors"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"go.uber.org/zap"
)

// GetUserShippingFeeTemplatesRequest 查询用户运费模板请求
type GetUserShippingFeeTemplatesRequest struct {
	UserID int64 `uri:"userId" binding:"required"` // 用户ID
}

// UserShippingFeeTemplateDTO 用户运费模板DTO
type UserShippingFeeTemplateDTO struct {
	ID                      int64   `json:"id"`                      // 模板ID
	Name                    string  `json:"name"`                    // 模板名称

	FirstWeightPrice        float64 `json:"firstWeightPrice"`        // 首重价格
	FirstWeightRange        float64 `json:"firstWeightRange"`        // 首重范围
	ContinuedWeightPrice    float64 `json:"continuedWeightPrice"`    // 续重价格
	ContinuedWeightInterval float64 `json:"continuedWeightInterval"` // 续重区间大小
	BulkCoefficient         int     `json:"bulkCoefficient"`         // 轻抛系数
	ThreeSidesStart         float64 `json:"threeSidesStart"`         // 三边和超过多少开始计算体积重量
	CreateTime              string  `json:"createTime"`              // 创建时间，格式：yyyy-MM-dd HH:mm:ss
	UpdateTime              string  `json:"updateTime"`              // 更新时间，格式：yyyy-MM-dd HH:mm:ss
}

// GetUserShippingFeeTemplatesResponse 查询用户运费模板响应
type GetUserShippingFeeTemplatesResponse struct {
	UserID    int64                         `json:"userId"`    // 用户ID
	Templates []*UserShippingFeeTemplateDTO `json:"templates"` // 模板列表
}

// ConfigService 配置管理服务接口
type ConfigService interface {
	// GetUserShippingFeeTemplates 根据用户ID查询三种类型的运费模板
	GetUserShippingFeeTemplates(ctx context.Context, req *GetUserShippingFeeTemplatesRequest) (*GetUserShippingFeeTemplatesResponse, int, error)
}

// ConfigServiceImpl 配置管理服务实现
type ConfigServiceImpl struct {
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository
}

// NewConfigService 创建配置管理服务
func NewConfigService(shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository) ConfigService {
	return &ConfigServiceImpl{
		shippingFeeTemplateRepo: shippingFeeTemplateRepo,
	}
}

// GetUserShippingFeeTemplates 根据用户ID查询三种类型的运费模板
func (s *ConfigServiceImpl) GetUserShippingFeeTemplates(ctx context.Context, req *GetUserShippingFeeTemplatesRequest) (*GetUserShippingFeeTemplatesResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.UserID <= 0 {
		if logger != nil {
			logger.Warn("Invalid user ID", zap.Int64("userId", req.UserID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("用户ID必须大于0")
	}

	// 2. 查询用户的所有运费模板
	templates, err := s.shippingFeeTemplateRepo.GetUserAllTemplates(ctx, req.UserID)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to get user shipping fee templates",
				zap.Int64("userId", req.UserID),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询用户运费模板失败")
	}

	// 3. 转换为DTO
	templateDTOs := make([]*UserShippingFeeTemplateDTO, len(templates))
	for i, template := range templates {
		templateDTOs[i] = &UserShippingFeeTemplateDTO{
			ID:                      template.ID,
			Name:                    template.Name,

			FirstWeightPrice:        template.FirstWeightPrice,
			FirstWeightRange:        template.FirstWeightRange,
			ContinuedWeightPrice:    template.ContinuedWeightPrice,
			ContinuedWeightInterval: template.ContinuedWeightInterval,
			BulkCoefficient:         template.BulkCoefficient,
			ThreeSidesStart:         template.ThreeSidesStart,
			CreateTime:              template.CreateTime.Time.Format("2006-01-02 15:04:05"),
			UpdateTime:              template.UpdateTime.Time.Format("2006-01-02 15:04:05"),
		}
	}

	if logger != nil {
		logger.Info("Successfully retrieved user shipping fee templates",
			zap.Int64("userId", req.UserID),
			zap.Int("templateCount", len(templateDTOs)))
	}

	return &GetUserShippingFeeTemplatesResponse{
		UserID:    req.UserID,
		Templates: templateDTOs,
	}, valueobject.SUCCESS, nil
}



