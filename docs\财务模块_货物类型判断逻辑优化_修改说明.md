# 财务模块货物类型判断逻辑优化修改说明

## 修改背景

在生成账单明细时，`determineCargoType` 方法原本只是简单返回普货类型，没有根据实际的运单信息进行判断。现在需要根据运单的 `shippingFeeTemplateType` 字段来正确判断货物类型。

## 问题分析

### 原始实现问题

**文件**: `internal/app/service/billing_service.go`

**原始代码**:

```go
// determineCargoType 根据运单信息判断货物类型
func (s *BillingServiceImpl) determineCargoType(manifest *entity.Manifest) entity.CargoType {
	// 这里应该根据实际业务逻辑判断货物类型
	// 目前暂时返回普货类型，实际实现时需要根据运单的物品信息等判断
	return entity.CargoTypeGeneral
}
```

**问题**:

1. 所有运单都被判断为普货类型（CargoTypeGeneral）
2. 无法正确区分带电货物和投函货物
3. 导致账单明细中的货物类型信息不准确
4. 影响运费模板的正确选择和费用计算

## 修改方案

### 1. 优化货物类型判断逻辑

**修改后的代码**:

```go
// determineCargoType 根据运单信息判断货物类型
func (s *BillingServiceImpl) determineCargoType(manifest *entity.Manifest) entity.CargoType {
	// 根据运单的运费模板类型判断货物类型
	switch manifest.ShippingFeeTemplateType {
	case 1:
		return entity.CargoTypeGeneral   // 普货
	case 2:
		return entity.CargoTypeBattery   // 带电货物
	case 3:
		return entity.CargoTypePostBox   // 投函货物
	default:
		return entity.CargoTypeGeneral   // 默认为普货
	}
}
```

### 2. 货物类型映射关系

| ShippingFeeTemplateType | CargoType        | 中文描述 | 说明                               |
| ----------------------- | ---------------- | -------- | ---------------------------------- |
| 1                       | CargoTypeGeneral | 普货     | 普通货物，使用普通运费模板         |
| 2                       | CargoTypeBattery | 带电货物 | 含电池的货物，使用带电运费模板     |
| 3                       | CargoTypePostBox | 投函货物 | 投递到邮箱的货物，使用投函运费模板 |
| 其他值                  | CargoTypeGeneral | 普货     | 默认处理为普通货物                 |

## 业务流程优化

### 修改前的流程

1. 查询运单数据
2. **所有运单都判断为普货类型** ❌
3. 选择普货模板计算费用
4. 生成账单明细

### 修改后的流程

1. 查询运单数据
2. **根据运单的 `ShippingFeeTemplateType` 正确判断货物类型** ✅
3. 根据货物类型选择对应的运费模板：
   - 普货 → 使用 GeneralTemplate
   - 带电货物 → 使用 BatteryTemplate
   - 投函货物 → 使用 PostBoxTemplate
4. 使用正确的模板计算费用
5. 生成准确的账单明细

## 影响范围

### 1. 账单明细准确性提升

- **货物类型字段**: `cargoType` 现在能正确反映实际的货物类型
- **货物类型名称**: `cargoTypeName` 显示正确的中文描述
- **运费计算**: 使用对应类型的运费模板进行计算

### 2. 费用计算准确性

- 不同类型的货物使用对应的运费模板
- 带电货物和投函货物的特殊费率得到正确应用
- 避免了所有货物都按普货费率计算的问题

### 3. API 响应改进

**账单明细 API 响应示例**:

```json
{
  "id": 1,
  "cargoType": 2,
  "cargoTypeName": "带电货物",
  "baseFreightFee": 25.5,
  "firstWeightFee": 15.0,
  "continuedWeightFee": 10.5,
  "itemTotalAmount": 35.5
}
```

## 数据一致性

### 运单实体字段

**文件**: `internal/domain/entity/manifest.go`

```go
type Manifest struct {
    // ... 其他字段
    ShippingFeeTemplateType int `json:"shippingFeeTemplateType"` // 运费模板类型
    // ... 其他字段
}
```

### 货物类型常量

**文件**: `internal/domain/entity/billing_record.go`

```go
// CargoType 货物类型
type CargoType int

const (
    CargoTypeGeneral   CargoType = 1 // 普货
    CargoTypeBattery   CargoType = 2 // 带电货物
    CargoTypePostBox   CargoType = 3 // 投函货物
)
```

## 验证结果

- ✅ 代码编译成功
- ✅ 货物类型判断逻辑正确实现
- ✅ 与运单的 `ShippingFeeTemplateType` 字段保持一致
- ✅ 支持三种货物类型的正确识别
- ✅ 提供了合理的默认值处理

## 测试建议

1. **单元测试**: 验证不同 `ShippingFeeTemplateType` 值的货物类型判断
2. **集成测试**: 测试生成账单时不同类型货物的费用计算
3. **数据验证**: 检查现有运单数据的 `ShippingFeeTemplateType` 分布情况

## 相关文件

- `internal/app/service/billing_service.go` - 货物类型判断逻辑
- `internal/domain/entity/manifest.go` - 运单实体定义
- `internal/domain/entity/billing_record.go` - 货物类型常量定义
- `internal/adapter/persistence/model/manifest.go` - 运单数据库模型
