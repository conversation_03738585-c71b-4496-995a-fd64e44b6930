package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// TrackingNumberRepository 运单号相关的仓储接口
type TrackingNumberRepository interface {
	// GetChannelByShipmentType 根据货物类型获取渠道
	GetChannelByShipmentType(ctx context.Context, locationID int64, shipmentTypeID int64) (*entity.TrackingNumberChannel, error)
	
	// AllocateTrackingNumber 分配一个可用的运单号
	AllocateTrackingNumber(ctx context.Context, channelID int64) (*entity.TrackingNumberPool, error)
} 