package persistence

import (
	"context"
	"errors"
	"fmt"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// ShippingFeeTemplateRepositoryImpl 运费模板仓库实现
type ShippingFeeTemplateRepositoryImpl struct {
	db *gorm.DB
}

// NewShippingFeeTemplateRepository 创建运费模板仓库
func NewShippingFeeTemplateRepository(db *gorm.DB) repository.ShippingFeeTemplateRepository {
	return &ShippingFeeTemplateRepositoryImpl{
		db: db,
	}
}

// GetTemplateByID 根据ID获取运费模板
func (r *ShippingFeeTemplateRepositoryImpl) GetTemplateByID(ctx context.Context, id int64) (entity.ShippingFeeTemplate, error) {
	var template entity.ShippingFeeTemplate
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&template).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return template, fmt.Errorf("运费模板不存在: ID=%d", id)
		}
		return template, fmt.Errorf("获取运费模板失败: %w", err)
	}
	return template, nil
}

// GetDefaultUserTemplateID 获取用户默认模板ID
func (r *ShippingFeeTemplateRepositoryImpl) GetDefaultUserTemplateID(ctx context.Context, userID int64) (int64, error) {
	// 获取用户的第一个模板作为默认模板
	var templateUser entity.ShippingFeeTemplateUser
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&templateUser).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, fmt.Errorf("用户未配置任何运费模板: userID=%d", userID)
		}
		return 0, fmt.Errorf("获取用户默认运费模板失败: %w", err)
	}
	return templateUser.TemplateID, nil
}

// GetUserAllTemplates 根据用户ID获取所有类型的运费模板
func (r *ShippingFeeTemplateRepositoryImpl) GetUserAllTemplates(ctx context.Context, userID int64) ([]*entity.ShippingFeeTemplate, error) {
	// 查询用户配置的所有模板关系
	var templateUsers []entity.ShippingFeeTemplateUser
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&templateUsers).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户运费模板关系失败: %w", err)
	}

	if len(templateUsers) == 0 {
		return []*entity.ShippingFeeTemplate{}, nil // 返回空切片而不是nil
	}

	// 提取模板ID列表
	templateIDs := make([]int64, len(templateUsers))
	for i, templateUser := range templateUsers {
		templateIDs[i] = templateUser.TemplateID
	}

	// 查询所有模板详情
	var templatePOs []entity.ShippingFeeTemplate
	err = r.db.WithContext(ctx).Where("id IN ?", templateIDs).Find(&templatePOs).Error
	if err != nil {
		return nil, fmt.Errorf("查询运费模板详情失败: %w", err)
	}

	// 转换为指针切片
	templates := make([]*entity.ShippingFeeTemplate, len(templatePOs))
	for i := range templatePOs {
		templates[i] = &templatePOs[i]
	}

	return templates, nil
}

// GetTemplatesWithSearch 查询运费模板，支持模板名模糊搜索
func (r *ShippingFeeTemplateRepositoryImpl) GetTemplatesWithSearch(ctx context.Context, name string) ([]*entity.ShippingFeeTemplate, error) {
	var templates []entity.ShippingFeeTemplate

	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&entity.ShippingFeeTemplate{})

	// 如果提供了模板名，进行模糊搜索
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// 查询所有符合条件的模板
	err := query.Order("id ASC").Find(&templates).Error
	if err != nil {
		return nil, fmt.Errorf("查询运费模板失败: %w", err)
	}

	// 转换为指针切片
	result := make([]*entity.ShippingFeeTemplate, len(templates))
	for i := range templates {
		result[i] = &templates[i]
	}

	return result, nil
}

// CreateTemplate 创建运费模板
func (r *ShippingFeeTemplateRepositoryImpl) CreateTemplate(ctx context.Context, template *entity.ShippingFeeTemplate) error {
	err := r.db.WithContext(ctx).Create(template).Error
	if err != nil {
		return fmt.Errorf("创建运费模板失败: %w", err)
	}
	return nil
}

// UpdateTemplate 更新运费模板
func (r *ShippingFeeTemplateRepositoryImpl) UpdateTemplate(ctx context.Context, template *entity.ShippingFeeTemplate) error {
	err := r.db.WithContext(ctx).
		Where("id = ?", template.ID).
		Updates(template).Error
	if err != nil {
		return fmt.Errorf("更新运费模板失败: %w", err)
	}
	return nil
}

// DeleteTemplate 删除运费模板
func (r *ShippingFeeTemplateRepositoryImpl) DeleteTemplate(ctx context.Context, id int64) error {
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Delete(&entity.ShippingFeeTemplate{}).Error
	if err != nil {
		return fmt.Errorf("删除运费模板失败: %w", err)
	}
	return nil
}

// CheckTemplateExists 检查模板是否存在
func (r *ShippingFeeTemplateRepositoryImpl) CheckTemplateExists(ctx context.Context, id int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ShippingFeeTemplate{}).
		Where("id = ?", id).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查模板是否存在失败: %w", err)
	}
	return count > 0, nil
}

// CheckTemplateNameExists 检查模板名称是否已存在
func (r *ShippingFeeTemplateRepositoryImpl) CheckTemplateNameExists(ctx context.Context, name string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ShippingFeeTemplate{}).
		Where("name = ?", name).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查模板名称是否存在失败: %w", err)
	}
	return count > 0, nil
}

// CheckTemplateNameExistsExcludeID 检查模板名称是否已存在（排除指定ID的模板）
func (r *ShippingFeeTemplateRepositoryImpl) CheckTemplateNameExistsExcludeID(ctx context.Context, name string, excludeID int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ShippingFeeTemplate{}).
		Where("name = ? AND id != ?", name, excludeID).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查模板名称是否存在失败: %w", err)
	}
	return count > 0, nil
}

// CheckTemplateInUse 检查模板是否被用户使用
func (r *ShippingFeeTemplateRepositoryImpl) CheckTemplateInUse(ctx context.Context, templateID int64) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&entity.ShippingFeeTemplateUser{}).
		Where("template_id = ?", templateID).
		Count(&count).Error
	if err != nil {
		return false, fmt.Errorf("检查模板使用情况失败: %w", err)
	}
	return count > 0, nil
}

// GetUserConfiguredTemplates 根据用户ID获取其已配置的运费模板（包含type信息）
func (r *ShippingFeeTemplateRepositoryImpl) GetUserConfiguredTemplates(ctx context.Context, userID int64) ([]*repository.UserConfiguredTemplate, error) {
	// 先查询用户配置的模板关系
	var templateUsers []entity.ShippingFeeTemplateUser
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Find(&templateUsers).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户运费模板关系失败: %w", err)
	}
	
	if len(templateUsers) == 0 {
		return []*repository.UserConfiguredTemplate{}, nil
	}
	
	// 提取唯一的模板ID列表
	templateIDSet := make(map[int64]bool)
	for _, templateUser := range templateUsers {
		templateIDSet[templateUser.TemplateID] = true
	}
	
	uniqueTemplateIDs := make([]int64, 0, len(templateIDSet))
	for templateID := range templateIDSet {
		uniqueTemplateIDs = append(uniqueTemplateIDs, templateID)
	}
	
	// 查询唯一模板的详情
	var templates []entity.ShippingFeeTemplate
	err = r.db.WithContext(ctx).
		Where("id IN ?", uniqueTemplateIDs).
		Find(&templates).Error
	if err != nil {
		return nil, fmt.Errorf("查询运费模板详情失败: %w", err)
	}
	
	// 创建模板ID到模板对象的映射
	templateMap := make(map[int64]*entity.ShippingFeeTemplate)
	for i := range templates {
		templateMap[templates[i].ID] = &templates[i]
	}
	
	// 按照用户配置的顺序构建结果，确保每个配置都有对应的返回记录
	result := make([]*repository.UserConfiguredTemplate, len(templateUsers))
	for i, templateUser := range templateUsers {
		template, exists := templateMap[templateUser.TemplateID]
		if !exists {
			return nil, fmt.Errorf("模板不存在: ID=%d", templateUser.TemplateID)
		}
		
		result[i] = &repository.UserConfiguredTemplate{
			Template: template,
			Type:     templateUser.Type,
		}
	}
	
	return result, nil
}

// UpdateUserTemplateConfigurations 更新用户的模板配置
func (r *ShippingFeeTemplateRepositoryImpl) UpdateUserTemplateConfigurations(ctx context.Context, userID int64, templateConfigs []*entity.ShippingFeeTemplateUser) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 删除该用户现有的所有模板配置
		err := tx.Where("user_id = ?", userID).Delete(&entity.ShippingFeeTemplateUser{}).Error
		if err != nil {
			return fmt.Errorf("删除用户现有模板配置失败: %w", err)
		}

		// 2. 如果有新配置，则批量插入
		if len(templateConfigs) > 0 {
			// 确保所有配置的用户ID都是正确的
			for _, config := range templateConfigs {
				config.UserID = userID
			}

			err = tx.Create(templateConfigs).Error
			if err != nil {
				return fmt.Errorf("插入新的模板配置失败: %w", err)
			}
		}

		return nil
	})
}
