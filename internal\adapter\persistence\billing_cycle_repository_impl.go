package persistence

import (
	"context"
	"fmt"
	"time"
	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// BillingCycleRepositoryImpl 账期批次仓储实现
type BillingCycleRepositoryImpl struct {
	db *gorm.DB
}

// NewBillingCycleRepository 创建账期批次仓储
func NewBillingCycleRepository(db *gorm.DB) repository.BillingCycleRepository {
	return &BillingCycleRepositoryImpl{
		db: db,
	}
}

// SaveBillingCycle 保存账期批次记录
func (r *BillingCycleRepositoryImpl) SaveBillingCycle(ctx context.Context, cycle *entity.BillingCycle) error {
	po := &model.BillingCyclePO{
		ID:                        cycle.ID,
		CycleYear:                 cycle.CycleYear,
		CycleMonth:                cycle.CycleMonth,
		CycleName:                 cycle.CycleName,
		Status:                    string(cycle.Status),
		TotalCustomersBilled:      cycle.TotalCustomersBilled,
		TotalBillsGenerated:       cycle.TotalBillsGenerated,
		TotalBilledAmount:         cycle.TotalBilledAmount,
		TotalAmountPaidInCycle:    cycle.TotalAmountPaidInCycle,
		TotalBalanceDueInCycle:    cycle.TotalBalanceDueInCycle,
		GeneratedByUserID:         cycle.GeneratedByUserID,
		GenerationStartTime:       cycle.GenerationStartTime,
		GenerationEndTime:         cycle.GenerationEndTime,
		Notes:                     cycle.Notes,
		CreateTime:                cycle.CreateTime,
		UpdateTime:                cycle.UpdateTime,
	}

	if cycle.ID == 0 {
		// 新增
		if err := r.db.WithContext(ctx).Create(po).Error; err != nil {
			return err
		}
		cycle.ID = po.ID
	} else {
		// 更新
		if err := r.db.WithContext(ctx).Save(po).Error; err != nil {
			return err
		}
	}

	return nil
}

// FindBillingCycleByID 根据ID查询账期批次
func (r *BillingCycleRepositoryImpl) FindBillingCycleByID(ctx context.Context, id int64) (*entity.BillingCycle, error) {
	var po model.BillingCyclePO

	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&po).Error; err != nil {
		return nil, err
	}

	return r.poToEntity(&po), nil
}

// FindBillingCycleByYearMonth 根据年月查询账期批次
func (r *BillingCycleRepositoryImpl) FindBillingCycleByYearMonth(ctx context.Context, year, month int) (*entity.BillingCycle, error) {
	var po model.BillingCyclePO

	if err := r.db.WithContext(ctx).Where("cycle_year = ? AND cycle_month = ?", year, month).First(&po).Error; err != nil {
		return nil, err
	}

	return r.poToEntity(&po), nil
}

// CountBillingCycles 统计账期批次总数
func (r *BillingCycleRepositoryImpl) CountBillingCycles(ctx context.Context, filters interface{}) (int64, error) {
	// 将filters转换为具体类型
	filter, ok := filters.(*service.BillingCycleFilters)
	if !ok {
		return 0, fmt.Errorf("invalid filter type")
	}

	query := r.db.WithContext(ctx).Model(&model.BillingCyclePO{})
	query = r.applyBillingCycleFilters(query, filter)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// FindBillingCycles 分页查询账期批次列表
func (r *BillingCycleRepositoryImpl) FindBillingCycles(ctx context.Context, filters interface{}, page, pageSize int) ([]*entity.BillingCycle, error) {
	// 将filters转换为具体类型
	filter, ok := filters.(*service.BillingCycleFilters)
	if !ok {
		return nil, fmt.Errorf("invalid filter type")
	}

	var pos []*model.BillingCyclePO

	query := r.db.WithContext(ctx).Model(&model.BillingCyclePO{})
	query = r.applyBillingCycleFilters(query, filter)

	// 分页查询，按年月倒序
	offset := (page - 1) * pageSize
	if err := query.Order("cycle_year DESC, cycle_month DESC").Offset(offset).Limit(pageSize).Find(&pos).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	cycles := make([]*entity.BillingCycle, len(pos))
	for i, po := range pos {
		cycles[i] = r.poToEntity(po)
	}

	return cycles, nil
}

// CheckCycleExists 检查指定年月的账期批次是否已存在
func (r *BillingCycleRepositoryImpl) CheckCycleExists(ctx context.Context, year, month int) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.BillingCyclePO{}).
		Where("cycle_year = ? AND cycle_month = ?", year, month).
		Count(&count).Error; err != nil {
		return false, err
	}

	return count > 0, nil
}

// poToEntity 将PO转换为实体
func (r *BillingCycleRepositoryImpl) poToEntity(po *model.BillingCyclePO) *entity.BillingCycle {
	return &entity.BillingCycle{
		ID:                        po.ID,
		CycleYear:                 po.CycleYear,
		CycleMonth:                po.CycleMonth,
		CycleName:                 po.CycleName,
		Status:                    entity.BillingCycleStatus(po.Status),
		TotalCustomersBilled:      po.TotalCustomersBilled,
		TotalBillsGenerated:       po.TotalBillsGenerated,
		TotalBilledAmount:         po.TotalBilledAmount,
		TotalAmountPaidInCycle:    po.TotalAmountPaidInCycle,
		TotalBalanceDueInCycle:    po.TotalBalanceDueInCycle,
		GeneratedByUserID:         po.GeneratedByUserID,
		GenerationStartTime:       po.GenerationStartTime,
		GenerationEndTime:         po.GenerationEndTime,
		Notes:                     po.Notes,
		CreateTime:                po.CreateTime,
		UpdateTime:                po.UpdateTime,
	}
}

// applyBillingCycleFilters 应用查询过滤器
func (r *BillingCycleRepositoryImpl) applyBillingCycleFilters(query *gorm.DB, filter *service.BillingCycleFilters) *gorm.DB {
	// 年份过滤
	if filter.CycleYear != nil {
		query = query.Where("cycle_year = ?", *filter.CycleYear)
	}

	// 月份过滤
	if filter.CycleMonth != nil {
		query = query.Where("cycle_month = ?", *filter.CycleMonth)
	}

	// 状态过滤
	if filter.Status != nil && *filter.Status != "" {
		query = query.Where("status = ?", *filter.Status)
	}

	// 账期名称模糊查询
	if filter.CycleName != nil && *filter.CycleName != "" {
		query = query.Where("cycle_name LIKE ?", "%"+*filter.CycleName+"%")
	}

	// 生成操作员过滤
	if filter.GeneratedByUserID != nil {
		query = query.Where("generated_by_user_id = ?", *filter.GeneratedByUserID)
	}

	return query
}

// UpdateBillingCycleStatistics 更新账期批次统计信息
func (r *BillingCycleRepositoryImpl) UpdateBillingCycleStatistics(ctx context.Context, cycleID int64, customerID int64, billCount int, billAmount float64) error {
	// 使用事务确保数据一致性
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 查询当前账期批次
		var cyclePO model.BillingCyclePO
		if err := tx.Where("id = ?", cycleID).First(&cyclePO).Error; err != nil {
			return fmt.Errorf("查询账期批次失败: %w", err)
		}

		// 2. 查询该账期内的账单统计信息
		var stats struct {
			TotalCustomers int     `gorm:"column:total_customers"`
			TotalBills     int     `gorm:"column:total_bills"`
			TotalAmount    float64 `gorm:"column:total_amount"`
			TotalPaid      float64 `gorm:"column:total_paid"`
		}

		query := `
			SELECT 
				COUNT(DISTINCT customer_account_id) as total_customers,
				COUNT(*) as total_bills,
				COALESCE(SUM(total_amount), 0) as total_amount,
				COALESCE(SUM(amount_paid), 0) as total_paid
			FROM billing_records 
			WHERE billing_cycle_id = ?
		`
		
		if err := tx.Raw(query, cycleID).Scan(&stats).Error; err != nil {
			return fmt.Errorf("查询账单统计信息失败: %w", err)
		}

		// 3. 更新账期批次统计信息
		balanceDue := stats.TotalAmount - stats.TotalPaid
		updates := map[string]interface{}{
			"total_customers_billed":      stats.TotalCustomers,
			"total_bills_generated":       stats.TotalBills,
			"total_billed_amount":         stats.TotalAmount,
			"total_amount_paid_in_cycle":  stats.TotalPaid,
			"total_balance_due_in_cycle":  balanceDue,
			"update_time":                 time.Now(),
		}

		if err := tx.Model(&model.BillingCyclePO{}).Where("id = ?", cycleID).Updates(updates).Error; err != nil {
			return fmt.Errorf("更新账期批次统计信息失败: %w", err)
		}

		return nil
	})
}

// CalculateBillingCycleStatistics 重新计算账期批次的所有统计信息
func (r *BillingCycleRepositoryImpl) CalculateBillingCycleStatistics(ctx context.Context, cycleID int64) error {
	return r.UpdateBillingCycleStatistics(ctx, cycleID, 0, 0, 0)
} 