# Docker 部署指南（简化版）

## 概述

本项目提供简化的 Docker 部署方案，包含 API 服务器和账单消费者两个核心服务。

## 文件结构

```
├── Dockerfile              # API服务器构建文件
├── Dockerfile.consumer     # 账单消费者构建文件
├── docker-compose.yml      # 服务编排文件
└── .dockerignore          # 构建忽略文件
```

## 快速开始

### 1. 准备环境

- Docker Engine >= 20.10
- Docker Compose >= 2.0

### 2. 准备配置文件

确保以下文件存在：

- `configs/config.yaml` - 应用配置
- `assets/fonts/ipaexg.ttf` - PDF 字体文件

### 3. 部署服务

```bash
# 克隆代码
git clone <repository-url>
cd zebra-hub-system

# 启动服务
docker-compose up -d --build

# 查看状态
docker-compose ps
```

## 服务架构

### API 服务器

- **容器名**: zebra-api-server
- **端口**: 8080
- **功能**: REST API 服务

### 账单消费者

- **容器名**: zebra-billing-consumer
- **功能**: 异步处理账单任务

## 配置说明

### 环境变量

- `TZ=Asia/Shanghai` - 时区设置

### 数据卷挂载

```yaml
volumes:
  - ./configs:/app/configs # 配置目录
  - ./data:/app/data # 数据目录
  - ./assets:/app/assets # 资源目录
```

## 常用操作

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 重新构建
docker-compose build --no-cache

# 进入容器
docker exec -it zebra-api-server sh
```

## 健康检查

```bash
# 检查API服务
curl http://localhost:8080/api/v1/upload/info

# 检查容器状态
docker ps
docker-compose ps
```

## 故障排除

### 常见问题

1. **端口冲突**

   ```bash
   # 修改端口映射
   ports:
     - "9080:8080"
   ```

2. **配置文件错误**

   ```bash
   # 检查配置
   docker-compose config
   ```

3. **字体文件缺失**
   ```bash
   # 检查字体文件
   docker exec -it zebra-api-server ls /app/assets/fonts/
   ```

### 日志查看

```bash
# API服务器日志
docker-compose logs api-server

# 账单消费者日志
docker-compose logs billing-consumer

# 实时日志
docker-compose logs -f --tail=100
```

## 生产部署

### 外部依赖

生产环境建议使用外部服务：

- MySQL 数据库
- RabbitMQ 消息队列
- Redis 缓存

### 安全建议

1. 使用非 root 用户运行
2. 设置资源限制
3. 启用健康检查
4. 配置日志收集

### 性能优化

```yaml
# 添加资源限制
deploy:
  resources:
    limits:
      memory: 1G
      cpus: "1.0"
    reservations:
      memory: 512M
      cpus: "0.5"
```

## 升级部署

```bash
# 拉取最新代码
git pull

# 重新构建并部署
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 验证部署
docker-compose ps
```

## 备份与恢复

```bash
# 备份数据
docker run --rm -v zebra-hub-system_data:/data -v $(pwd):/backup alpine tar czf /backup/data-backup.tar.gz -C /data .

# 恢复数据
docker run --rm -v zebra-hub-system_data:/data -v $(pwd):/backup alpine tar xzf /backup/data-backup.tar.gz -C /data
```
