# 查询用户运费模板 API 文档

## 接口概述

该接口用于根据用户 ID 查询该用户配置的所有类型的运费模板，包括普通模板(1)、带电模板(2)、投函模板(3)。

## 接口信息

- **请求方法**: GET
- **请求路径**: `/api/v1/finance/shipping-fee-templates/user/{userId}`
- **Content-Type**: `application/json`
- **需要认证**: 是

## 请求参数

### 路径参数 (Path Parameters)

| 参数名 | 类型  | 必填 | 说明    |
| ------ | ----- | ---- | ------- |
| userId | int64 | 是   | 用户 ID |

## 业务逻辑说明

### 查询逻辑

1. **用户模板关系查询**:

   - 从 `tb_shipping_fee_template_user` 表查询该用户配置的所有模板关系
   - 支持一个用户配置多种类型的模板（普通、带电、投函）

2. **模板详情查询**:

   - 根据模板 ID 批量查询 `tb_shipping_fee_template` 表获取详细信息
   - 包含价格、重量范围、轻抛系数等运费计算参数

3. **结果排序**:
   - 按模板类型排序：普通模板(1) → 带电模板(2) → 投函模板(3)
   - 确保返回结果的一致性

### 模板类型说明

| 类型值 | 类型名称 | 说明                     |
| ------ | -------- | ------------------------ |
| 1      | 普通模板 | 适用于普通货物的运费计算 |
| 2      | 带电模板 | 适用于带电货物的运费计算 |
| 3      | 投函模板 | 适用于投函货物的运费计算 |

## 响应结果

### 成功响应

**HTTP 状态码**: 200

**响应体结构**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:35:00Z",
  "data": {
    "userId": 1001,
    "templates": [
      {
        "id": 1,
        "name": "标准普通货物模板",
        "type": 1,
        "typeName": "普通模板",
        "firstWeightPrice": 8.0,
        "firstWeightRange": 0.5,
        "continuedWeightPrice": 3.5,
        "continuedWeightInterval": 0.5,
        "bulkCoefficient": 5000,
        "threeSidesStart": 60.0,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      },
      {
        "id": 2,
        "name": "带电货物专用模板",
        "type": 2,
        "typeName": "带电模板",
        "firstWeightPrice": 12.0,
        "firstWeightRange": 0.5,
        "continuedWeightPrice": 5.0,
        "continuedWeightInterval": 0.5,
        "bulkCoefficient": 5000,
        "threeSidesStart": 60.0,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      },
      {
        "id": 3,
        "name": "投函服务模板",
        "type": 3,
        "typeName": "投函模板",
        "firstWeightPrice": 6.0,
        "firstWeightRange": 0.5,
        "continuedWeightPrice": 2.5,
        "continuedWeightInterval": 0.5,
        "bulkCoefficient": 5000,
        "threeSidesStart": 60.0,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

### 用户未配置模板的响应

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:35:00Z",
  "data": {
    "userId": 1001,
    "templates": []
  }
}
```

### 错误响应

**HTTP 状态码**: 400/500

**响应体结构**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "路径参数绑定错误: 用户ID必须大于0",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:30:00Z",
  "data": null
}
```

## 使用示例

### 示例 1: 查询用户 ID 为 1001 的运费模板

```bash
curl -X GET "http://localhost:8080/api/v1/finance/shipping-fee-templates/user/1001" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 2: 查询用户 ID 为 1002 的运费模板

```bash
curl -X GET "http://localhost:8080/api/v1/finance/shipping-fee-templates/user/1002" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## 响应字段说明

### GetUserShippingFeeTemplatesResponse 字段说明

| 字段名    | 类型  | 说明         |
| --------- | ----- | ------------ |
| userId    | int64 | 用户 ID      |
| templates | array | 运费模板列表 |

### UserShippingFeeTemplateDTO 字段说明

| 字段名                  | 类型    | 说明                                   |
| ----------------------- | ------- | -------------------------------------- |
| id                      | int64   | 模板 ID                                |
| name                    | string  | 模板名称                               |
| type                    | int     | 模板类型：1-普通；2-带电；3-投函       |
| typeName                | string  | 模板类型名称                           |
| firstWeightPrice        | float64 | 首重价格（元）                         |
| firstWeightRange        | float64 | 首重范围（公斤）                       |
| continuedWeightPrice    | float64 | 续重价格（元）                         |
| continuedWeightInterval | float64 | 续重区间大小（公斤）                   |
| bulkCoefficient         | int     | 轻抛系数（用于计算体积重量）           |
| threeSidesStart         | float64 | 三边和超过多少开始计算体积重量（厘米） |
| createTime              | string  | 创建时间，格式：yyyy-MM-dd HH:mm:ss    |
| updateTime              | string  | 更新时间，格式：yyyy-MM-dd HH:mm:ss    |

## 业务场景

### 适用场景

1. **账单生成**: 在生成用户账单时，需要获取用户配置的运费模板
2. **费用预览**: 前端需要显示用户的运费模板配置
3. **配置管理**: 系统管理员查看用户的运费模板配置情况
4. **运费计算**: 在计算运费时，根据货物类型选择对应的模板

### 使用建议

1. **缓存策略**: 运费模板变更不频繁，可考虑缓存模板数据
2. **类型验证**: 前端应根据货物类型自动选择对应的模板
3. **异常处理**: 如果用户未配置某种类型的模板，应提供默认模板或提示用户配置
4. **权限控制**: 确保用户只能查看自己的模板配置

## 错误码说明

| 错误码 | 说明              |
| ------ | ----------------- |
| 100000 | 操作成功          |
| 100002 | 无效参数          |
| 100004 | 未授权/未登录     |
| 100006 | 请求的资源未找到  |
| 100001 | 未知错误/系统繁忙 |

## 注意事项

1. **参数验证**: 用户 ID 必须大于 0
2. **认证要求**: 需要在请求头中包含有效的 Bearer Token
3. **数据完整性**: 返回的模板按类型排序，确保前端展示的一致性
4. **空结果处理**: 如果用户未配置任何模板，返回空数组而不是错误
5. **类型安全**: 模板类型字段包含数值和文本两种形式，便于前端使用

## 数据库查询说明

### 查询用户模板关系

```sql
SELECT user_id, type, template_id
FROM tb_shipping_fee_template_user
WHERE user_id = ?
```

### 查询模板详情

```sql
SELECT id, name, first_weight_price, first_weight_range,
       continued_weight_price, continued_weight_interval,
       bulk_coefficient, three_sides_start, type,
       create_time, update_time
FROM tb_shipping_fee_template
WHERE id IN (?, ?, ?)
```

## 版本历史

| 版本 | 日期       | 变更说明                           |
| ---- | ---------- | ---------------------------------- |
| v1.0 | 2024-12-01 | 初始版本，支持查询用户运费模板配置 |

## 相关接口

- [生成账单接口](/docs/财务模块_生成账单_API文档.md)
- [查询可生成账单用户列表接口](/docs/财务模块_查询可生成账单用户列表_API文档.md)
- [导出运费账单报表接口](/docs/财务模块_导出运费账单报表_API文档.md)
