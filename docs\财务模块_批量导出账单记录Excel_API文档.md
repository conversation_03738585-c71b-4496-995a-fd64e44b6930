# 财务模块 - 批量导出账单记录 Excel API 文档

## 概述

本接口用于根据多个账单记录 ID 批量生成 Excel 文件，并将所有文件打包成 zip 文件供下载。与单个导出接口相比，批量导出接口能同时处理多个账单记录，提高操作效率。

## 接口信息

- **接口路径**: `POST /api/v1/finance/billing-record/batch-export`
- **请求方式**: POST
- **Content-Type**: application/json
- **响应类型**: application/zip (zip 压缩文件)
- **认证要求**: 需要 JWT 认证

## 请求参数

### 请求体 (JSON)

```json
{
  "billingRecordIds": [123, 124, 125, 126]
}
```

| 参数名           | 类型    | 必填 | 说明                                     | 示例                 |
| ---------------- | ------- | ---- | ---------------------------------------- | -------------------- |
| billingRecordIds | []int64 | 是   | 账单记录 ID 列表，最少 1 个，最多 100 个 | [123, 124, 125, 126] |

### 参数约束

- **数量限制**: 最多支持 100 个账单记录 ID
- **ID 验证**: 所有 ID 必须大于 0
- **去重**: 系统不会自动去重，如有重复 ID 会生成重复文件

## 响应说明

### 成功响应 (HTTP 200)

- **Content-Type**: `application/zip`
- **Content-Disposition**: `attachment; filename=批量账单导出_20240115_143052_3个文件.zip`
- **响应体**: zip 压缩文件的二进制数据

#### zip 文件内容结构

```
批量账单导出_20240115_143052_3个文件.zip
├── 001_客户A_账单_BILL202401001_2024-01-15.xlsx
├── 002_客户B_账单_BILL202401002_2024-01-15.xlsx
├── 003_客户C_账单_BILL202401003_2024-01-15.xlsx
└── 导出错误报告.txt (如果有导出失败的文件)
```

### 错误响应

#### 参数错误 (HTTP 400)

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "参数错误: billingRecordIds必须包含至少1个账单记录ID",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": null
}
```

#### 数量超限 (HTTP 400)

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "批量导出数量不能超过100个",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": null
}
```

#### 服务器内部错误 (HTTP 500)

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "批量生成账单Excel失败: 系统内部错误",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": null
}
```

## 功能特性

### 1. 批量处理

- **并发安全**: 每个文件独立生成，失败不影响其他文件
- **错误容忍**: 部分文件生成失败时，继续处理其他文件
- **进度反馈**: 通过日志记录处理进度

### 2. 文件管理

- **唯一命名**: 每个文件添加序号前缀（001*, 002*, 003\_...）
- **原始格式**: 保持与单个导出相同的 Excel 格式和样式
- **压缩打包**: 自动打包成 zip 文件，便于下载和管理

### 3. 错误处理

- **详细报告**: 生成错误报告文件，记录失败的账单记录 ID 和原因
- **部分成功**: 即使部分文件失败，仍返回成功生成的文件
- **全部失败**: 如果所有文件都失败，返回错误响应

### 4. 性能优化

- **内存管理**: 使用流式处理，避免内存占用过大
- **文件大小**: 单个 Excel 文件大小通常在 50KB-2MB 之间
- **总体限制**: zip 文件总大小一般不超过 200MB

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8080/api/v1/finance/billing-record/batch-export" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "billingRecordIds": [123, 124, 125, 126, 127]
  }' \
  --output "批量账单导出.zip"
```

### JavaScript 示例

```javascript
const batchExportBillingRecords = async (billingRecordIds) => {
  try {
    const response = await fetch(
      "/api/v1/finance/billing-record/batch-export",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          billingRecordIds: billingRecordIds,
        }),
      }
    );

    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;

      // 从响应头获取文件名
      const contentDisposition = response.headers.get("Content-Disposition");
      const fileName = contentDisposition
        ? contentDisposition.split("filename=")[1]
        : `批量账单导出_${new Date().getTime()}.zip`;

      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      console.log(`成功导出 ${billingRecordIds.length} 个账单记录`);
    } else {
      const error = await response.json();
      console.error("批量导出失败:", error.errorMessage);
    }
  } catch (error) {
    console.error("请求失败:", error);
  }
};

// 使用示例
batchExportBillingRecords([123, 124, 125, 126, 127]);
```

### Python 示例

```python
import requests
import json

def batch_export_billing_records(billing_record_ids, token):
    url = "http://localhost:8080/api/v1/finance/billing-record/batch-export"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    data = {
        "billingRecordIds": billing_record_ids
    }

    response = requests.post(url, headers=headers, json=data)

    if response.status_code == 200:
        # 获取文件名
        content_disposition = response.headers.get('Content-Disposition', '')
        filename = content_disposition.split('filename=')[1] if 'filename=' in content_disposition else 'batch_export.zip'

        # 保存文件
        with open(filename, 'wb') as f:
            f.write(response.content)
        print(f"成功导出到文件: {filename}")
    else:
        error = response.json()
        print(f"导出失败: {error['errorMessage']}")

# 使用示例
batch_export_billing_records([123, 124, 125, 126], "your_jwt_token")
```

## 业务逻辑说明

### 1. 处理流程

1. **参数验证**: 检查账单记录 ID 列表的有效性
2. **创建 zip 包**: 初始化 zip 文件写入器
3. **逐个处理**: 调用单个导出接口生成每个 Excel 文件
4. **文件命名**: 为每个文件添加序号前缀确保唯一性
5. **错误收集**: 记录处理失败的文件信息
6. **生成报告**: 如有错误则生成错误报告文件
7. **返回结果**: 返回 zip 文件或错误信息

### 2. 错误处理策略

- **继续处理**: 单个文件失败不影响其他文件的生成
- **错误记录**: 详细记录每个失败文件的错误原因
- **最低保证**: 至少有一个文件成功才返回 zip 包
- **完全失败**: 所有文件都失败时返回错误响应

### 3. 文件命名规则

- **序号前缀**: 001*, 002*, 003\_... (三位数字确保排序正确)
- **原始名称**: 保持单个导出时的文件名格式
- **zip 文件名**: `批量账单导出_{时间戳}_{成功数量}个文件.zip`

## 性能指标

- **处理速度**: 单个文件平均生成时间 100-500ms
- **内存占用**: 峰值内存使用约为单个文件大小的 2-3 倍
- **并发能力**: 支持多用户同时进行批量导出
- **超时设置**: 单个请求总超时时间 5 分钟

## 注意事项

1. **权限控制**: 需要有效的 JWT token 和相应的账单查看权限
2. **数据量限制**: 建议单次批量导出不超过 50 个账单记录
3. **网络传输**: 大批量导出时注意网络超时设置
4. **存储空间**: 确保服务器有足够的临时存储空间
5. **日志监控**: 关注批量导出的成功率和错误情况

## 相关接口

- [单个账单记录导出](./财务模块_根据账单记录ID导出Excel_API文档.md)
- [分页查询账单记录](./财务模块_分页查询账单记录_API文档.md)

## 版本信息

- **添加日期**: 2024-01-15
- **版本**: v1.0.0
- **接口类型**: 新增功能
- **依赖**: 基于现有的单个导出接口实现
