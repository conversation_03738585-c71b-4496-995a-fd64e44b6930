package service

import (
	"testing"
	"zebra-hub-system/internal/domain/entity"

	"github.com/stretchr/testify/assert"
)

func TestBillingServiceImpl_analyzeRequiredTemplateTypes(t *testing.T) {
	service := &BillingServiceImpl{}

	tests := []struct {
		name      string
		manifests []*entity.Manifest
		expected  []int
	}{
		{
			name: "只有普通货物",
			manifests: []*entity.Manifest{
				{ShippingFeeTemplateType: 1},
				{ShippingFeeTemplateType: 1},
			},
			expected: []int{1},
		},
		{
			name: "普通货物和带电货物",
			manifests: []*entity.Manifest{
				{ShippingFeeTemplateType: 1},
				{ShippingFeeTemplateType: 2},
				{ShippingFeeTemplateType: 1},
			},
			expected: []int{1, 2},
		},
		{
			name: "全部类型货物",
			manifests: []*entity.Manifest{
				{ShippingFeeTemplateType: 1},
				{ShippingFeeTemplateType: 2},
				{ShippingFeeTemplateType: 3},
			},
			expected: []int{1, 2, 3},
		},
		{
			name: "默认模板类型（0）应转为普通货物",
			manifests: []*entity.Manifest{
				{ShippingFeeTemplateType: 0},
				{ShippingFeeTemplateType: 2},
			},
			expected: []int{1, 2},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.analyzeRequiredTemplateTypes(tt.manifests)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestBillingServiceImpl_checkMissingTemplates(t *testing.T) {
	service := &BillingServiceImpl{}

	tests := []struct {
		name          string
		templates     *entity.AppliedFreightTemplate
		requiredTypes []int
		expected      []string
	}{
		{
			name: "有普通模板，只需要普通模板",
			templates: &entity.AppliedFreightTemplate{
				GeneralTemplate: &entity.ShippingFeeTemplate{ID: 1},
			},
			requiredTypes: []int{1},
			expected:      []string{},
		},
		{
			name: "有普通模板，但需要带电模板",
			templates: &entity.AppliedFreightTemplate{
				GeneralTemplate: &entity.ShippingFeeTemplate{ID: 1},
			},
			requiredTypes: []int{1, 2},
			expected:      []string{"带电模板"},
		},
		{
			name: "没有任何模板，需要普通和带电模板",
			templates: &entity.AppliedFreightTemplate{},
			requiredTypes: []int{1, 2},
			expected:      []string{"普货模板", "带电模板"},
		},
		{
			name: "有全部模板，需要全部类型",
			templates: &entity.AppliedFreightTemplate{
				GeneralTemplate: &entity.ShippingFeeTemplate{ID: 1},
				BatteryTemplate: &entity.ShippingFeeTemplate{ID: 2},
				PostBoxTemplate: &entity.ShippingFeeTemplate{ID: 3},
			},
			requiredTypes: []int{1, 2, 3},
			expected:      []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.checkMissingTemplates(tt.templates, tt.requiredTypes)
			assert.Equal(t, tt.expected, result)
		})
	}
} 