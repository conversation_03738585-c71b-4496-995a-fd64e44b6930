package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/dto"
	domainService "zebra-hub-system/internal/domain/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// FinanceHandler 财务模块处理器
type FinanceHandler struct {
	financeService domainService.FinanceService
	billingService service.BillingService
}

// NewFinanceHandler 创建财务处理器
func NewFinanceHandler(financeService domainService.FinanceService, billingService service.BillingService) *FinanceHandler {
	return &FinanceHandler{
		financeService: financeService,
		billingService: billingService,
	}
}

// ExportShippingBill 导出运费账单报表
// @Summary 导出运费账单报表
// @Description 根据用户ID和指定时间范围生成费用账单报表，支持按发货时间或预报时间筛选，提供下载EXCEL
// @Tags 财务管理
// @Accept json
// @Produce octet-stream
// @Param request body dto.ShippingBillExportRequest true "导出请求参数，timeFilterType=1为按发货时间(默认)，timeFilterType=2为按预报时间"
// @Success 200 {file} file "返回Excel文件"
// @Failure 400 {object} util.Response "参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/finance/shipping-bill/export [post]
func (h *FinanceHandler) ExportShippingBill(c *gin.Context) {
	var req dto.ShippingBillExportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, 100002, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 处理时区问题 - 如果时间带有Z后缀(UTC时间)，则转换为UTC时区
	if !req.StartTime.IsZero() && req.StartTime.Location() != time.UTC {
		// 确保使用UTC时区
		req.StartTime = req.StartTime.UTC()
	}
	if !req.EndTime.IsZero() && req.EndTime.Location() != time.UTC {
		// 确保使用UTC时区
		req.EndTime = req.EndTime.UTC()
	}
	
	// 校验时间参数
	if req.StartTime.IsZero() || req.EndTime.IsZero() || req.EndTime.Before(req.StartTime) {
		util.ResponseError(c, 100002, "时间参数错误：开始时间和结束时间必须有效，且结束时间必须晚于开始时间", http.StatusBadRequest)
		return
	}
	
	// 校验并设置TimeFilterType默认值
	if req.TimeFilterType != 1 && req.TimeFilterType != 2 {
		// 如果不是有效值，则默认设为1(按发货时间筛选)
		req.TimeFilterType = 1
	}
	
	// 调用服务导出Excel文件
	excelData, fileName, err := h.financeService.GenerateShippingBillExcel(c, req)
	if err != nil {
		util.ResponseError(c, 100001, "生成账单报表失败: "+err.Error(), http.StatusInternalServerError)
		return
	}
	
	// 对文件名进行URL编码，解决中文文件名乱码问题
	encodedFileName := util.URLEncode(fileName)
	
	// 设置HTTP响应头信息
	c.Header("Content-Disposition", "attachment; filename="+encodedFileName)
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Length", strconv.Itoa(len(excelData)))
	
	// 返回文件内容
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelData)
}

// ExportBillingRecordExcel 根据账单记录ID导出Excel
// @Summary 根据账单记录ID导出Excel
// @Description 根据账单记录ID查询对应的账单明细和财务调整快照，生成Excel文件下载
// @Tags 财务管理
// @Accept json
// @Produce octet-stream
// @Param request body dto.BillingRecordExportRequest true "导出请求参数"
// @Success 200 {file} file "返回Excel文件"
// @Failure 400 {object} util.Response "参数错误"
// @Failure 404 {object} util.Response "账单记录不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/finance/billing-record/export [post]
func (h *FinanceHandler) ExportBillingRecordExcel(c *gin.Context) {
	var req dto.BillingRecordExportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 参数验证
	if req.BillingRecordID <= 0 {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "账单记录ID必须大于0", http.StatusBadRequest)
		return
	}
	
	// 调用服务导出Excel文件
	excelData, fileName, err := h.financeService.GenerateBillingRecordExcel(c.Request.Context(), req)
	if err != nil {
		// 根据错误类型设置不同的HTTP状态码
		httpStatus := http.StatusInternalServerError
		errorCode := valueobject.ERROR_UNKNOWN
		
		if strings.Contains(err.Error(), "账单记录不存在") || strings.Contains(err.Error(), "not found") {
			httpStatus = http.StatusNotFound
			errorCode = valueobject.ERROR_RESOURCE_NOT_FOUND
		} else if strings.Contains(err.Error(), "参数") {
			httpStatus = http.StatusBadRequest
			errorCode = valueobject.ERROR_INVALID_PARAMETER
		}
		
		util.ResponseError(c, errorCode, "生成账单Excel失败: "+err.Error(), httpStatus)
		return
	}
	
	// 对文件名进行URL编码，解决中文文件名乱码问题
	encodedFileName := util.URLEncode(fileName)
	
	// 设置HTTP响应头信息
	c.Header("Content-Disposition", "attachment; filename="+encodedFileName)
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Length", strconv.Itoa(len(excelData)))
	
	// 返回文件内容
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", excelData)
}

// BatchExportBillingRecordExcel 批量导出账单记录Excel
// @Summary 批量导出账单记录Excel
// @Description 根据多个账单记录ID批量生成Excel文件，并打包成zip文件下载
// @Tags 财务管理
// @Accept json
// @Produce octet-stream
// @Param request body dto.BatchBillingRecordExportRequest true "批量导出请求参数"
// @Success 200 {file} file "返回zip文件"
// @Failure 400 {object} util.Response "参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/finance/billing-record/batch-export [post]
func (h *FinanceHandler) BatchExportBillingRecordExcel(c *gin.Context) {
	var req dto.BatchBillingRecordExportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 参数验证
	if len(req.BillingRecordIDs) == 0 {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "账单记录ID列表不能为空", http.StatusBadRequest)
		return
	}
	
	if len(req.BillingRecordIDs) > 100 {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "批量导出数量不能超过100个", http.StatusBadRequest)
		return
	}
	
	// 验证所有ID都大于0
	for i, id := range req.BillingRecordIDs {
		if id <= 0 {
			util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, 
				fmt.Sprintf("账单记录ID[%d]必须大于0", i), http.StatusBadRequest)
			return
		}
	}
	
	// 调用服务批量导出Excel文件
	zipData, fileName, err := h.financeService.BatchGenerateBillingRecordExcel(c.Request.Context(), req)
	if err != nil {
		// 根据错误类型设置不同的HTTP状态码
		httpStatus := http.StatusInternalServerError
		errorCode := valueobject.ERROR_UNKNOWN
		
		if strings.Contains(err.Error(), "参数") {
			httpStatus = http.StatusBadRequest
			errorCode = valueobject.ERROR_INVALID_PARAMETER
		}
		
		util.ResponseError(c, errorCode, "批量生成账单Excel失败: "+err.Error(), httpStatus)
		return
	}
	
	// 对文件名进行URL编码，解决中文文件名乱码问题
	encodedFileName := util.URLEncode(fileName)
	
	// 设置HTTP响应头信息为zip文件
	c.Header("Content-Disposition", "attachment; filename="+encodedFileName)
	c.Header("Content-Type", "application/zip")
	c.Header("Content-Length", strconv.Itoa(len(zipData)))
	
	// 返回zip文件内容
	c.Data(http.StatusOK, "application/zip", zipData)
}

// GenerateBilling 生成账单
// @Summary 生成账单
// @Description 根据时间范围（固定使用发货时间）、用户ID和运费模板信息生成账单记录及明细。模板信息直接传入完整的ShippingFeeTemplate对象，而不是ID
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param request body service.GenerateBillingRequest true "生成账单请求参数，包含完整的运费模板对象"
// @Success 200 {object} util.Response{data=service.GenerateBillingResponse} "成功生成账单"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/generate [post]
// @Security ApiKeyAuth
func (h *FinanceHandler) GenerateBilling(c *gin.Context) {
	var req service.GenerateBillingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从上下文中获取当前用户ID并设置到请求中
	claimsData, exists := c.Get(util.ClaimsContextKey)
	if exists {
		userID := claimsData.(*util.Claims).UserID
		req.GeneratedByUserID = &userID // 设置账单生成操作员ID
	}

	// 调用服务层生成账单
	resp, code, err := h.billingService.GenerateBilling(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetUsersForBilling 查询可生成账单的用户列表
// @Summary 查询可生成账单的用户列表
// @Description 根据时间范围（固定使用发货时间）查询在指定时间段内有可生成账单的运单或财务调整记录的用户列表，包含统计信息
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param startTime query string true "开始时间，格式：yyyy-MM-dd HH:mm:ss"
// @Param endTime query string true "结束时间，格式：yyyy-MM-dd HH:mm:ss"
// @Success 200 {object} util.Response{data=service.GetUsersForBillingResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/users [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) GetUsersForBilling(c *gin.Context) {
	var req service.GetUsersForBillingRequest
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "查询参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层查询可生成账单的用户列表
	resp, code, err := h.billingService.GetUsersForBilling(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// ListBillingRecords 分页查询账单记录
// @Summary 分页查询账单记录
// @Description 根据条件分页查询账单记录，支持多种过滤条件，如客户ID、账单状态、时间范围、金额范围等
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认1" minimum(1) default(1)
// @Param pageSize query int false "每页数量，默认10，最大100" minimum(1) maximum(100) default(10)
// @Param customerAccountId query int false "客户账户ID"
// @Param status query string false "账单状态：UNPAID（未付款）、PAID（已付款）、PARTIAL_PAID（部分付款）、OVERDUE（逾期）、CANCELLED（已取消）"
// @Param billNumber query string false "账单编号（模糊查询）"
// @Param billDateStart query string false "账单日期开始，格式：yyyy-MM-dd"
// @Param billDateEnd query string false "账单日期结束，格式：yyyy-MM-dd"
// @Param billingPeriodStart query string false "账期开始日期，格式：yyyy-MM-dd"
// @Param billingPeriodEnd query string false "账期结束日期，格式：yyyy-MM-dd"
// @Param minAmount query number false "最小金额"
// @Param maxAmount query number false "最大金额"
// @Param currency query string false "货币单位，如CNY、USD等"
// @Success 200 {object} util.Response{data=service.ListBillingRecordsResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/records [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) ListBillingRecords(c *gin.Context) {
	var req service.ListBillingRecordsRequest
	
	// 绑定查询参数，并设置默认值
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "查询参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 设置默认的分页参数并验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	
	// 调用服务层查询账单记录
	resp, code, err := h.billingService.ListBillingRecords(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}


// ListBillingRecordItems 根据账单记录ID查询账单明细列表
// @Summary 根据账单记录ID查询账单明细列表
// @Description 分页查询指定账单记录的明细列表，包含运单信息、费用明细、物品信息等
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param billingRecordId path int true "账单记录ID"
// @Param page query int false "页码，默认1" minimum(1) default(1)
// @Param pageSize query int false "每页数量，默认20，最大100" minimum(1) maximum(100) default(20)
// @Success 200 {object} util.Response{data=service.ListBillingRecordItemsResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "账单记录不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/records/{billingRecordId}/items [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) ListBillingRecordItems(c *gin.Context) {
	var req service.ListBillingRecordItemsRequest
	
	// 绑定路径参数
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "路径参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "查询参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 设置默认的分页参数并验证
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}
	
	// 调用服务层查询账单明细
	resp, code, err := h.billingService.ListBillingRecordItems(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetBillingRecordDetail 根据账单记录ID获取账单记录详情
// @Summary 根据账单记录ID获取账单记录详情
// @Description 获取指定账单记录的详细信息，包含客户信息、运费模板信息、支付状态等完整数据
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param billingRecordId path int true "账单记录ID"
// @Success 200 {object} util.Response{data=service.GetBillingRecordDetailResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "账单记录不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/records/{billingRecordId} [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) GetBillingRecordDetail(c *gin.Context) {
	var req service.GetBillingRecordDetailRequest
	
	// 绑定路径参数
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "路径参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层获取账单记录详情
	resp, code, err := h.billingService.GetBillingRecordDetail(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// ListBillingAdjustmentSnapshots 根据账单记录ID分页查询调整明细列表
// @Summary 根据账单记录ID分页查询调整明细列表
// @Description 根据账单记录ID分页查询财务调整快照明细列表，包含完整的运单信息快照和调整信息
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param billingRecordId path int true "账单记录ID"
// @Param page query int false "页码，默认1" minimum(1) default(1)
// @Param pageSize query int false "每页数量，默认20，最大100" minimum(1) maximum(100) default(20)
// @Success 200 {object} util.Response{data=service.ListBillingAdjustmentSnapshotsResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "账单记录不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/records/{billingRecordId}/adjustment-snapshots [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) ListBillingAdjustmentSnapshots(c *gin.Context) {
	var req service.ListBillingAdjustmentSnapshotsRequest
	
	// 绑定路径参数
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "路径参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 绑定查询参数
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "查询参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层查询调整明细列表
	resp, code, err := h.billingService.ListBillingAdjustmentSnapshots(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// ListBillingGenerationTasks 查询账单生成任务列表
// @Summary 查询账单生成任务列表  
// @Description 根据账期批次ID和当前登录用户分页查询账单生成任务列表，包含任务状态、进度等信息
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param billingCycleId query int64 true "账期批次ID"
// @Param page query int false "页码，默认1" default(1)
// @Param pageSize query int false "每页数量，默认20，最大100" default(20)
// @Success 200 {object} util.Response{data=service.ListBillingGenerationTasksResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/generation-tasks [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) ListBillingGenerationTasks(c *gin.Context) {
	var req service.ListBillingGenerationTasksRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 获取当前登录用户ID
	var currentUserID int64
	if claimsData, exists := c.Get(util.ClaimsContextKey); exists {
		currentUserID = claimsData.(*util.Claims).UserID
	}

	// 调用服务层查询任务列表，传入当前用户ID
	resp, code, err := h.billingService.ListBillingGenerationTasksByUser(c.Request.Context(), &req, currentUserID)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// AsyncGenerateBilling 异步生成账单
// @Summary 异步生成账单
// @Description 提交异步账单生成任务，支持多个客户和用户特定模板配置，任务将在后台处理
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param request body service.AsyncGenerateBillingRequest true "异步生成账单请求参数"
// @Success 200 {object} util.Response{data=service.AsyncGenerateBillingResponse} "任务创建成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/async-generate [post]
// @Security ApiKeyAuth
func (h *FinanceHandler) AsyncGenerateBilling(c *gin.Context) {
	var req service.AsyncGenerateBillingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从上下文中获取当前用户ID
	var submittedByUserID int64
	if claimsData, exists := c.Get(util.ClaimsContextKey); exists {
		submittedByUserID = claimsData.(*util.Claims).UserID
	}

	// 调用服务层生成异步账单任务
	resp, code, err := h.billingService.AsyncGenerateBilling(c.Request.Context(), &req, submittedByUserID)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetBillingGenerationTaskDetail 根据任务ID获取任务详情
// @Summary 根据任务ID获取任务详情
// @Description 根据任务ID获取账单生成任务的详细信息，包含任务状态、进度、客户列表、提交者信息等
// @Tags 财务管理
// @Accept json
// @Produce json
// @Param taskId path string true "任务ID"
// @Success 200 {object} util.Response{data=service.GetBillingGenerationTaskDetailResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "任务不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /finance/billing/generation-tasks/{taskId} [get]
// @Security ApiKeyAuth
func (h *FinanceHandler) GetBillingGenerationTaskDetail(c *gin.Context) {
	var req service.GetBillingGenerationTaskDetailRequest
	
	// 绑定路径参数
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "路径参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层获取任务详情
	resp, code, err := h.billingService.GetBillingGenerationTaskDetail(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
} 