package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// UserRepository 用户仓储接口
type UserRepository interface {
	// FindByUsername 根据用户名查找用户
	FindByUsername(ctx context.Context, username string) (*entity.User, error)
	
	// FindByPhone 根据手机号查找用户
	FindByPhone(ctx context.Context, phone string) (*entity.User, error)
	
	// FindByID 根据用户ID查找用户
	FindByID(ctx context.Context, id int64) (*entity.User, error)
	
	// UpdateLoginInfo 更新用户登录信息
	UpdateLoginInfo(ctx context.Context, id int64, loginIP string) error

	// FindByIDs 根据用户ID批量查询用户
	FindByIDs(ctx context.Context, ids []int64) ([]*entity.User, error)
	
	// ListUsers 查询用户列表，用于下拉框
	// @param ctx - 上下文
	// @param keyword - 可选的关键字过滤(用户名或昵称)
	// @param page - 页码
	// @param pageSize - 每页数量
	// @return 用户列表、总数量和可能的错误
	ListUsers(ctx context.Context, keyword string, page, pageSize int) ([]*entity.User, int64, error)
} 