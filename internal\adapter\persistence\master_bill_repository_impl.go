package persistence

import (
	"context"
	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// MasterBillRepositoryImpl 主提单仓储实现
type MasterBillRepositoryImpl struct {
	db *gorm.DB
}

// NewMasterBillRepository 创建主提单仓储
func NewMasterBillRepository(db *gorm.DB) repository.MasterBillRepository {
	return &MasterBillRepositoryImpl{
		db: db,
	}
}

// ListForOptions 获取主提单列表，用于前端下拉框
func (r *MasterBillRepositoryImpl) ListForOptions(ctx context.Context, keyword string, page, pageSize int) ([]*entity.MasterBill, int64, error) {
	var masterBillPOs []*model.MasterBillPO
	var total int64
	
	offset := (page - 1) * pageSize
	
	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&model.MasterBillPO{})
	
	// 只查询未删除的记录
	query = query.Where("is_deleted = ?", 0)
	
	// 添加关键字过滤
	if keyword != "" {
		keyword = "%" + keyword + "%"
		query = query.Where("master_bill_number LIKE ?", keyword)
	}
	
	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询数据
	if err := query.Order("id DESC").Offset(offset).Limit(pageSize).Find(&masterBillPOs).Error; err != nil {
		return nil, 0, err
	}
	
	// 转换为实体
	masterBills := make([]*entity.MasterBill, 0, len(masterBillPOs))
	for _, po := range masterBillPOs {
		masterBills = append(masterBills, po.ToEntity())
	}
	
	return masterBills, total, nil
}

// FindByID 根据ID查询主提单
func (r *MasterBillRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.MasterBill, error) {
	var masterBillPO model.MasterBillPO
	
	if err := r.db.WithContext(ctx).Where("id = ? AND is_deleted = ?", id, 0).First(&masterBillPO).Error; err != nil {
		return nil, err
	}
	
	return masterBillPO.ToEntity(), nil
} 