package service

import (
	"context"
	"errors"
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ProblemTicketService 问题工单服务
type ProblemTicketService struct {
	problemTicketRepo repository.ProblemTicketRepository
	userRepo          repository.UserRepository
	manifestRepo      repository.ManifestRepository
}

// NewProblemTicketService 创建问题工单服务实例
func NewProblemTicketService(
	problemTicketRepo repository.ProblemTicketRepository,
	userRepo repository.UserRepository,
	manifestRepo repository.ManifestRepository,
) *ProblemTicketService {
	return &ProblemTicketService{
		problemTicketRepo: problemTicketRepo,
		userRepo:          userRepo,
		manifestRepo:      manifestRepo,
	}
}

// ListProblemTicketsRequest 查询问题工单请求
type ListProblemTicketsRequest struct {
	Page            int    `json:"page" form:"page"`                       // 页码
	PageSize        int    `json:"pageSize" form:"pageSize"`               // 每页大小
	Status          string `json:"status" form:"status"`                   // 工单状态
	ProblemTypeCode string `json:"problemTypeCode" form:"problemTypeCode"` // 问题类型
	TrackingNumber  string `json:"trackingNumber" form:"trackingNumber"`   // 物流单号
	CustomerID      int64  `json:"customerId" form:"customerId"`           // 客户ID
	AssignedUserID  int64  `json:"assignedUserId" form:"assignedUserId"`   // 处理人ID
	StartTime       string `json:"startTime" form:"startTime"`             // 创建时间范围开始，格式：yyyy-MM-dd HH:mm:ss
	EndTime         string `json:"endTime" form:"endTime"`                 // 创建时间范围结束，格式：yyyy-MM-dd HH:mm:ss
	ManifestID      int64  `json:"manifestId" form:"manifestId"`           // 运单ID
	Priority        int    `json:"priority" form:"priority"`               // 优先级
}

// ProblemTicketDTO 带有用户昵称信息的问题工单DTO
type ProblemTicketDTO struct {
	*entity.ProblemTicket
	CustomerNickname     string `json:"customerNickname"`     // 客户昵称
	AssignedUserNickname string `json:"assignedUserNickname"` // 处理人昵称
}

// ListProblemTicketsResponse 问题工单查询响应
type ListProblemTicketsResponse struct {
	Total    int64               `json:"total"`    // 总记录数
	Page     int                 `json:"page"`     // 当前页码
	PageSize int                 `json:"pageSize"` // 每页大小
	Data     []*ProblemTicketDTO `json:"data"`     // 查询结果
}

// ProblemTicketDetailResponse 工单详情响应
type ProblemTicketDetailResponse struct {
	*ProblemTicketDTO
}

// ResolveProblemTicketRequest 处理问题工单请求
type ResolveProblemTicketRequest struct {
	ID      int64  `json:"id" uri:"id" binding:"required"` // 工单ID
	Remarks string `json:"remarks"`                         // 处理备注
}

// ResolveProblemTicketResponse 处理问题工单响应 (简化)
type ResolveProblemTicketResponse struct {
	Success bool `json:"success"` // 是否成功
}

// AddProblemTicketRemarkRequest 添加问题工单备注请求
type AddProblemTicketRemarkRequest struct {
	ID      int64  `json:"id" uri:"id" binding:"required"` // 工单ID
	Remarks string `json:"remarks" binding:"required"`      // 处理备注 (必填)
}

// AddProblemTicketRemarkResponse 添加问题工单备注响应
type AddProblemTicketRemarkResponse struct {
	Success bool `json:"success"` // 是否成功
}

// MarkAllProblemTicketsAsResolvedRequest 批量处理所有问题工单请求
type MarkAllProblemTicketsAsResolvedRequest struct {
	Remarks string `json:"remarks"` // 处理备注 (可选)
}

// MarkAllProblemTicketsAsResolvedResponse 批量处理所有问题工单响应
type MarkAllProblemTicketsAsResolvedResponse struct {
	ProcessedCount int64 `json:"processedCount"` // 已处理的工单数量 
}

// MarkSpecificProblemTicketsAsResolvedRequest 批量处理指定问题工单请求
type MarkSpecificProblemTicketsAsResolvedRequest struct {
	TicketIDs []int64 `json:"ticketIds" binding:"required"` // 需要处理的工单ID列表
	Remarks   string  `json:"remarks"`                     // 处理备注 (可选)
}

// MarkSpecificProblemTicketsAsResolvedResponse 批量处理指定问题工单响应
type MarkSpecificProblemTicketsAsResolvedResponse struct {
	ProcessedCount int64 `json:"processedCount"` // 成功处理的工单数量
}

// MarkProblemTicketAsPendingRequest 将问题工单标记为待处理请求
type MarkProblemTicketAsPendingRequest struct {
	ID      int64  `json:"id" uri:"id" binding:"required"` // 工单ID
	Remarks string `json:"remarks"`                         // 处理备注
}

// MarkProblemTicketAsPendingResponse 将问题工单标记为待处理响应
type MarkProblemTicketAsPendingResponse struct {
	Success bool `json:"success"` // 是否成功
}

// MarkSpecificProblemTicketsAsPendingRequest 批量将指定问题工单标记为待处理请求
type MarkSpecificProblemTicketsAsPendingRequest struct {
	TicketIDs []int64 `json:"ticketIds" binding:"required"` // 需要处理的工单ID列表
	Remarks   string  `json:"remarks"`                     // 处理备注 (可选)
}

// MarkSpecificProblemTicketsAsPendingResponse 批量将指定问题工单标记为待处理响应
type MarkSpecificProblemTicketsAsPendingResponse struct {
	ProcessedCount int64 `json:"processedCount"` // 成功处理的工单数量
}

// ListProblemTickets 分页查询问题工单
func (s *ProblemTicketService) ListProblemTickets(ctx context.Context, req *ListProblemTicketsRequest) (*ListProblemTicketsResponse, int, error) {
	// 设置默认分页参数
	page := req.Page
	if page < 1 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// 转换查询参数
	repoQueryParams := &repository.ProblemTicketQueryParams{
		Status:          req.Status,
		ProblemTypeCode: req.ProblemTypeCode,
		TrackingNumber:  req.TrackingNumber,
		CustomerID:      req.CustomerID,
		AssignedUserID:  req.AssignedUserID,
		StartTime:       req.StartTime,
		EndTime:         req.EndTime,
		ManifestID:      req.ManifestID,
		Priority:        req.Priority,
	}

	// 调用仓储层查询
	tickets, total, err := s.problemTicketRepo.FindPaginated(ctx, repoQueryParams, page, pageSize)
	if err != nil {
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 提取所有需要查询的用户ID
	userIDs := make(map[int64]bool)
	for _, ticket := range tickets {
		if ticket.CustomerAccountID > 0 {
			userIDs[ticket.CustomerAccountID] = true
		}
		if ticket.AssignedToUserID > 0 {
			userIDs[ticket.AssignedToUserID] = true
		}
	}

	// 批量查询用户信息
	uniqueUserIDs := make([]int64, 0, len(userIDs))
	for id := range userIDs {
		uniqueUserIDs = append(uniqueUserIDs, id)
	}

	var users []*entity.User
	var userMap = make(map[int64]*entity.User)

	if len(uniqueUserIDs) > 0 {
		users, err = s.userRepo.FindByIDs(ctx, uniqueUserIDs)
		if err != nil {
			// 不阻塞主流程，仅记录错误
			// logger.Error("Failed to find users by IDs", zap.Error(err))
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 填充昵称信息
	ticketDTOs := make([]*ProblemTicketDTO, 0, len(tickets))
	for _, ticket := range tickets {
		dto := &ProblemTicketDTO{
			ProblemTicket: ticket,
		}

		// 设置客户昵称
		if customer, ok := userMap[ticket.CustomerAccountID]; ok {
			dto.CustomerNickname = customer.Nickname
		}

		// 设置处理人昵称
		if assignedUser, ok := userMap[ticket.AssignedToUserID]; ok {
			dto.AssignedUserNickname = assignedUser.Nickname
		}

		ticketDTOs = append(ticketDTOs, dto)
	}

	return &ListProblemTicketsResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Data:     ticketDTOs,
	}, valueobject.SUCCESS, nil
}

// GetProblemTicketByID 根据ID获取问题工单
func (s *ProblemTicketService) GetProblemTicketByID(ctx context.Context, id int64) (*ProblemTicketDetailResponse, int, error) {
	ticket, err := s.problemTicketRepo.GetByID(ctx, id)
	if err != nil {
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, err
	}

	// 创建DTO并填充用户昵称
	dto := &ProblemTicketDTO{
		ProblemTicket: ticket,
	}

	// 查询并设置客户和处理人昵称
	userIDs := make([]int64, 0, 2)
	if ticket.CustomerAccountID > 0 {
		userIDs = append(userIDs, ticket.CustomerAccountID)
	}
	if ticket.AssignedToUserID > 0 {
		userIDs = append(userIDs, ticket.AssignedToUserID)
	}

	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			// 不阻塞主流程，仅记录错误
			// logger.Error("Failed to find users by IDs for detail", zap.Error(err))
		} else {
			userMap := make(map[int64]*entity.User)
			for _, user := range users {
				userMap[user.ID] = user
			}

			// 设置客户昵称
			if customer, ok := userMap[ticket.CustomerAccountID]; ok {
				dto.CustomerNickname = customer.Nickname
			}

			// 设置处理人昵称
			if assignedUser, ok := userMap[ticket.AssignedToUserID]; ok {
				dto.AssignedUserNickname = assignedUser.Nickname
			}
		}
	}

	return &ProblemTicketDetailResponse{
		ProblemTicketDTO: dto,
	}, valueobject.SUCCESS, nil
}

// ResolveProblemTicket 处理问题工单
func (s *ProblemTicketService) ResolveProblemTicket(ginCtx *gin.Context, req *ResolveProblemTicketRequest) (*ResolveProblemTicketResponse, int, error) {
	ctx := ginCtx.Request.Context() // 从 gin.Context 获取标准 context.Context
	ticket, err := s.problemTicketRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("问题工单不存在")
	}

	if ticket.Status != valueobject.ProblemTicketStatusPending {
		return nil, valueobject.ERROR_PROBLEM_TICKET_STATUS_INVALID_OPERATION, errors.New("工单状态不是待处理，无法执行此操作")
	}

	// 获取当前登录用户ID
	claimsData, exists := ginCtx.Get(util.ClaimsContextKey) // 使用 util.ClaimsContextKey
	if !exists {
		return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("无法获取用户信息")
	}
	userID := claimsData.(*util.Claims).UserID // 使用 util.Claims 替代 util.CustomClaims

	ticket.Status = valueobject.ProblemTicketStatusResolved
	ticket.Remarks = req.Remarks
	ticket.AssignedToUserID = userID // 记录处理人ID
	resolvedTime := time.Now()
	ticket.ResolvedTime = util.NewPointerFormattedTime(&resolvedTime)

	if err := s.problemTicketRepo.Update(ctx, ticket); err != nil {
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	return &ResolveProblemTicketResponse{
		Success: true,
	}, valueobject.SUCCESS, nil
}

// AddRemarkToProblemTicket 为问题工单添加备注
func (s *ProblemTicketService) AddRemarkToProblemTicket(ginCtx *gin.Context, req *AddProblemTicketRemarkRequest) (*AddProblemTicketRemarkResponse, int, error) {
	ctx := ginCtx.Request.Context()
	ticket, err := s.problemTicketRepo.GetByID(ctx, req.ID)
	if err != nil {
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("问题工单不存在")
	}

	// 获取当前登录用户ID，如果需要记录是谁添加的备注（可选）
	// claimsData, exists := ginCtx.Get(util.ClaimsContextKey)
	// if !exists {
	// 	return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("无法获取用户信息")
	// }
	// userID := claimsData.(*util.Claims).UserID

	// 更新备注，可以考虑是追加还是覆盖，这里简单覆盖
	ticket.Remarks = req.Remarks
	// ticket.AssignedToUserID = userID // 如果需要记录最后修改备注的人

	if err := s.problemTicketRepo.Update(ctx, ticket); err != nil {
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	return &AddProblemTicketRemarkResponse{
		Success: true,
	}, valueobject.SUCCESS, nil
}

// MarkAllProblemTicketsAsResolved 批量将所有待处理问题工单标记为已处理
func (s *ProblemTicketService) MarkAllProblemTicketsAsResolved(ginCtx *gin.Context, req *MarkAllProblemTicketsAsResolvedRequest) (*MarkAllProblemTicketsAsResolvedResponse, int, error) {
	ctx := ginCtx.Request.Context()

	// 获取当前登录用户ID
	claimsData, exists := ginCtx.Get(util.ClaimsContextKey)
	if !exists {
		return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("无法获取用户信息")
	}
	userID := claimsData.(*util.Claims).UserID

	// 使用默认备注（如果未提供）
	remark := req.Remarks
	if remark == "" {
		remark = "批量处理完成"
	}

	// 调用仓储层方法批量更新问题工单
	processedCount, err := s.problemTicketRepo.MarkAllAsResolved(ctx, userID, remark)
	if err != nil {
		return nil, valueobject.ERROR_UNKNOWN, errors.New("批量处理问题工单失败: " + err.Error())
	}

	// 返回处理结果
	return &MarkAllProblemTicketsAsResolvedResponse{
		ProcessedCount: processedCount,
	}, valueobject.SUCCESS, nil
}

// MarkSpecificProblemTicketsAsResolved 批量将指定问题工单标记为已处理
func (s *ProblemTicketService) MarkSpecificProblemTicketsAsResolved(ginCtx *gin.Context, req *MarkSpecificProblemTicketsAsResolvedRequest) (*MarkSpecificProblemTicketsAsResolvedResponse, int, error) {
	ctx := ginCtx.Request.Context()

	// 验证参数
	if len(req.TicketIDs) == 0 {
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("工单ID列表不能为空")
	}

	// 获取当前登录用户ID
	claimsData, exists := ginCtx.Get(util.ClaimsContextKey)
	if !exists {
		return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("无法获取用户信息")
	}
	userID := claimsData.(*util.Claims).UserID

	// 使用默认备注（如果未提供）
	remark := req.Remarks
	if remark == "" {
		remark = "批量处理完成"
	}

	// 调用仓储层方法批量更新问题工单
	processedCount, err := s.problemTicketRepo.MarkMultipleAsResolved(ctx, req.TicketIDs, userID, remark)
	if err != nil {
		return nil, valueobject.ERROR_UNKNOWN, errors.New("批量处理指定问题工单失败: " + err.Error())
	}

	// 返回处理结果
	return &MarkSpecificProblemTicketsAsResolvedResponse{
		ProcessedCount: processedCount,
	}, valueobject.SUCCESS, nil
}

// MarkProblemTicketAsPending 将问题工单标记为待处理
func (s *ProblemTicketService) MarkProblemTicketAsPending(ginCtx *gin.Context, req *MarkProblemTicketAsPendingRequest) (*MarkProblemTicketAsPendingResponse, int, error) {
	ctx := ginCtx.Request.Context() // 从 gin.Context 获取标准 context.Context

	// 获取当前登录用户ID
	claimsData, exists := ginCtx.Get(util.ClaimsContextKey)
	if !exists {
		return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("无法获取用户信息")
	}
	userID := claimsData.(*util.Claims).UserID

	// 调用仓储层方法更新工单状态
	err := s.problemTicketRepo.MarkAsPending(ctx, req.ID, userID, req.Remarks)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("问题工单不存在或无法更新")
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("更新问题工单状态失败: " + err.Error())
	}

	return &MarkProblemTicketAsPendingResponse{
		Success: true,
	}, valueobject.SUCCESS, nil
}

// MarkSpecificProblemTicketsAsPending 批量将指定问题工单标记为待处理
func (s *ProblemTicketService) MarkSpecificProblemTicketsAsPending(ginCtx *gin.Context, req *MarkSpecificProblemTicketsAsPendingRequest) (*MarkSpecificProblemTicketsAsPendingResponse, int, error) {
	ctx := ginCtx.Request.Context()

	// 验证参数
	if len(req.TicketIDs) == 0 {
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("工单ID列表不能为空")
	}

	// 获取当前登录用户ID
	claimsData, exists := ginCtx.Get(util.ClaimsContextKey)
	if !exists {
		return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("无法获取用户信息")
	}
	userID := claimsData.(*util.Claims).UserID

	// 使用默认备注（如果未提供）
	remark := req.Remarks
	if remark == "" {
		remark = "批量标记为待处理"
	}

	// 调用仓储层方法批量更新问题工单
	processedCount, err := s.problemTicketRepo.MarkMultipleAsPending(ctx, req.TicketIDs, userID, remark)
	if err != nil {
		return nil, valueobject.ERROR_UNKNOWN, errors.New("批量标记问题工单为待处理失败: " + err.Error())
	}

	// 返回处理结果
	return &MarkSpecificProblemTicketsAsPendingResponse{
		ProcessedCount: processedCount,
	}, valueobject.SUCCESS, nil
}

// GetUsersWithProblemManifests 获取拥有问题运单的用户列表
func (s *ProblemTicketService) GetUsersWithProblemManifests(ginCtx *gin.Context, req *UsersWithProblemManifestsRequest) (*UsersWithProblemManifestsResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Getting users with problem manifests", zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))

	users, total, err := s.manifestRepo.FindUsersWithProblemManifests(ctx, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to find users with problem manifests from repository", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	userDTOs := make([]*UserWithProblemManifestsDTO, 0, len(users))
	for _, user := range users {
		userDTOs = append(userDTOs, &UserWithProblemManifestsDTO{
			UserID:       user.ID,
			Nickname:     user.Nickname,
			PendingCount: user.PendingCount,
		})
	}

	logger.Debug("Successfully got users with problem manifests", zap.Int64("totalCount", total), zap.Int("returnedCount", len(userDTOs)))
	return &UsersWithProblemManifestsResponse{
		Total: total,
		List:  userDTOs,
	}, valueobject.SUCCESS, nil
}

// GetProblemManifestCount 获取问题运单数量
func (s *ProblemTicketService) GetProblemManifestCount(ctx context.Context) (int64, error) {
	// 调用仓储层查询所有待处理的问题运单数量
	count, err := s.problemTicketRepo.CountPendingProblemTickets(ctx)
	if err != nil {
		return 0, err
	}
	
	return count, nil
}
