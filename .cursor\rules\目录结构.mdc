---
description: 
globs: 
alwaysApply: true
---
# 目录结构

```md
[project-root]/
├── cmd/                     # 主要应用程序的入口 (main packages)
│   └── [app-name]/          # 例如: api-server, worker
│       └── main.go
├── internal/                # 内部代码，不希望其他项目导入
│   ├── adapter/             # 适配器层 (DDD): 外部依赖的实现
│   │   ├── persistence/     # 数据库仓储实现 (PostgreSQL, MySQL)
│   │   ├── message_queue/   # 消息队列生产者/消费者实现
│   │   └── external_service/ # 外部服务客户端
│   ├── app/                 # 应用层 (DDD): 编排业务逻辑，用例
│   │   └── service/         # 应用服务
│   ├── domain/              # 领域层 (DDD): 核心业务逻辑与模型
│   │   ├── entity/          # 实体
│   │   ├── valueobject/     # 值对象
│   │   ├── repository/      # 仓储接口定义
│   │   ├── aggregate/       # 聚合 (可选，如果严格 DDD)
│   │   └── domain_service/  # 领域服务 (如果业务逻辑不适合放在实体中)
│   ├── config/              # 配置加载与管理
│   ├── handler/             # API 处理器/控制器 (例如 Gin/Echo handlers)
│   ├── middleware/          # HTTP 中间件
│   ├── router/              # API 路由定义
│   └── util/                # 通用工具函数
├── pkg/                     # 可被外部项目引用的库代码 (如果你的项目也作为库提供)
│   └── [library-name]/
├── api/                     # API 定义文件 (例如 Swagger/OpenAPI .yaml or .json)
├── configs/                 # 配置文件 (例如 .yaml, .toml, .env)
├── scripts/                 # 脚本 (例如构建、部署、数据库迁移)
├── test/                    # 集成测试、端到端测试 (单元测试在各包内)
├── web/                     # 前端静态资源 (如果前后端一体)
├── Dockerfile
├── Makefile                 # (可选) 常用构建命令
├── go.mod
├── go.sum
└── README.md

```
## 各目录职责说明
cmd/: 包含项目的可执行文件入口。每个子目录是一个独立的应用程序。main.go 负责初始化依赖、配置，并启动应用。
internal/: 项目的核心私有代码。这是应用的主要逻辑所在。
adapter/: 实现领域层定义的接口，与外部世界（数据库、消息队列、第三方服务）交互。
app/: 包含应用服务，它们协调领域对象来完成特定的业务用例。不包含业务规则。
domain/: 包含所有业务核心概念、规则和逻辑。这是项目中最独立的部分，不依赖其他层。
config/: 负责加载和管理应用程序的配置。
handler/ (或 interfaces/http): 处理 HTTP 请求，解析参数，调用应用服务，并返回响应。
middleware/: HTTP 中间件，如认证、日志、CORS 等。
router/: 定义 API 路由，将 URL 路径映射到对应的 Handler。
util/: 项目内部共享的、与业务无关的通用工具函数。
pkg/: 如果项目的部分代码可以作为库被其他项目复用，则放在这里。如果只是一个应用程序，可以省略此目录。
api/: 存放 API 契约文件，如 OpenAPI/Swagger 定义。
configs/: 存放应用程序的配置文件模板或默认配置。
scripts/: 存放辅助脚本，如构建、部署、数据库迁移等。


test/: 用于存放集成测试和端到端测试代码。单元测试应与被测试的代码放在同一个包内。