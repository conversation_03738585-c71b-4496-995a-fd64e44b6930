package service

import (
	"context"
)

// BillingService 账单服务接口
type BillingService interface {
	// 同步账单生成相关方法
	GenerateBilling(ctx context.Context, req *GenerateBillingRequest) (*GenerateBillingResponse, int, error)
	ListBillingRecords(ctx context.Context, req *ListBillingRecordsRequest) (*ListBillingRecordsResponse, int, error)
	GetBillingRecordDetail(ctx context.Context, req *GetBillingRecordDetailRequest) (*GetBillingRecordDetailResponse, int, error)
	ListBillingRecordItems(ctx context.Context, req *ListBillingRecordItemsRequest) (*ListBillingRecordItemsResponse, int, error)
	ListBillingAdjustmentSnapshots(ctx context.Context, req *ListBillingAdjustmentSnapshotsRequest) (*ListBillingAdjustmentSnapshotsResponse, int, error)
	GetUsersForBilling(ctx context.Context, req *GetUsersForBillingRequest) (*GetUsersForBillingResponse, int, error)
	
	// 异步账单生成相关方法
	AsyncGenerateBilling(ctx context.Context, req *AsyncGenerateBillingRequest, submittedByUserID int64) (*AsyncGenerateBillingResponse, int, error)
	
	// 账单生成任务相关方法
	ListBillingGenerationTasks(ctx context.Context, req *ListBillingGenerationTasksRequest) (*ListBillingGenerationTasksResponse, int, error)
	ListBillingGenerationTasksByUser(ctx context.Context, req *ListBillingGenerationTasksRequest, userID int64) (*ListBillingGenerationTasksResponse, int, error)
	GetBillingGenerationTaskDetail(ctx context.Context, req *GetBillingGenerationTaskDetailRequest) (*GetBillingGenerationTaskDetailResponse, int, error)
} 