# 财务模块 - 删除 billing_financial_adjustment_snapshots 字段修改说明

## 修改概述

根据数据库表结构变更，从 `billing_financial_adjustment_snapshots` 表和相关代码中删除以下字段：

- `manifest_base_freight_fee` - 基础运费
- `manifest_first_weight_fee` - 首重费用
- `manifest_continued_weight_fee` - 续重费用
- `manifest_over_length_surcharge` - 超长费
- `manifest_remote_area_surcharge` - 偏远费
- `manifest_overweight_surcharge` - 超重费
- `manifest_original_total_fee` - 原始总费用
- `original_creator_id` - 原始记录创建者 ID
- `original_create_time` - 原始记录创建时间

## 修改内容

### 1. 实体层修改

**文件**: `internal/domain/entity/billing_record.go`

**修改内容**:

- 从 `BillingFinancialAdjustmentSnapshot` 结构体中删除以下字段：
  - `ManifestBaseFreightFee`
  - `ManifestFirstWeightFee`
  - `ManifestContinuedWeightFee`
  - `ManifestOverLengthSurcharge`
  - `ManifestRemoteAreaSurcharge`
  - `ManifestOverweightSurcharge`
  - `ManifestOriginalTotalFee`
  - `OriginalCreatorID`
  - `OriginalCreatorName`
  - `OriginalCreateTime`

**修改前**:

```go
type BillingFinancialAdjustmentSnapshot struct {
    // ... 其他字段

    // 运单费用构成快照
    ManifestBaseFreightFee      *float64 `json:"manifestBaseFreightFee,omitempty"`
    ManifestFirstWeightFee      *float64 `json:"manifestFirstWeightFee,omitempty"`
    ManifestContinuedWeightFee  *float64 `json:"manifestContinuedWeightFee,omitempty"`
    ManifestOverLengthSurcharge *float64 `json:"manifestOverLengthSurcharge,omitempty"`
    ManifestRemoteAreaSurcharge *float64 `json:"manifestRemoteAreaSurcharge,omitempty"`
    ManifestOverweightSurcharge *float64 `json:"manifestOverweightSurcharge,omitempty"`
    ManifestOriginalTotalFee    *float64 `json:"manifestOriginalTotalFee,omitempty"`

    // 原始记录信息
    OriginalCreatorID    *int64    `json:"originalCreatorId,omitempty"`
    OriginalCreatorName  *string   `json:"originalCreatorName,omitempty"`
    OriginalCreateTime   time.Time `json:"originalCreateTime"`
    SnapshotTime         time.Time `json:"snapshotTime"`
}
```

**修改后**:

```go
type BillingFinancialAdjustmentSnapshot struct {
    // ... 其他字段

    // 快照创建时间
    SnapshotTime         time.Time `json:"snapshotTime"`
}
```

### 2. 数据库模型修改

**文件**: `internal/adapter/persistence/model/billing_model.go`

**修改内容**:

- 从 `BillingFinancialAdjustmentSnapshotPO` 结构体中删除对应的数据库字段映射

**修改前**:

```go
type BillingFinancialAdjustmentSnapshotPO struct {
    // ... 其他字段

    // 运单费用构成快照
    ManifestBaseFreightFee      *float64 `gorm:"column:manifest_base_freight_fee;type:decimal(19,2)"`
    ManifestFirstWeightFee      *float64 `gorm:"column:manifest_first_weight_fee;type:decimal(19,2)"`
    ManifestContinuedWeightFee  *float64 `gorm:"column:manifest_continued_weight_fee;type:decimal(19,2)"`
    ManifestOverLengthSurcharge *float64 `gorm:"column:manifest_over_length_surcharge;type:decimal(19,2)"`
    ManifestRemoteAreaSurcharge *float64 `gorm:"column:manifest_remote_area_surcharge;type:decimal(19,2)"`
    ManifestOverweightSurcharge *float64 `gorm:"column:manifest_overweight_surcharge;type:decimal(19,2)"`
    ManifestOriginalTotalFee    *float64 `gorm:"column:manifest_original_total_fee;type:decimal(19,2)"`

    // 原始记录信息
    OriginalCreatorID    *int64    `gorm:"column:original_creator_id"`
    OriginalCreateTime   time.Time `gorm:"column:original_create_time;not null"`
    SnapshotTime         time.Time `gorm:"column:snapshot_time;autoCreateTime"`
}
```

**修改后**:

```go
type BillingFinancialAdjustmentSnapshotPO struct {
    // ... 其他字段

    // 快照创建时间
    SnapshotTime         time.Time `gorm:"column:snapshot_time;autoCreateTime"`
}
```

### 3. 服务层 DTO 修改

**文件**: `internal/app/service/billing_service.go`

**修改内容**:

- 从 `BillingAdjustmentSnapshotDTO` 结构体中删除对应字段
- 简化 `ListBillingAdjustmentSnapshots` 方法中的 DTO 转换逻辑

**修改前**:

```go
type BillingAdjustmentSnapshotDTO struct {
    // ... 其他字段

    // 运单费用构成快照
    ManifestBaseFreightFee      *float64 `json:"manifestBaseFreightFee,omitempty"`
    ManifestFirstWeightFee      *float64 `json:"manifestFirstWeightFee,omitempty"`
    ManifestContinuedWeightFee  *float64 `json:"manifestContinuedWeightFee,omitempty"`
    ManifestOverLengthSurcharge *float64 `json:"manifestOverLengthSurcharge,omitempty"`
    ManifestRemoteAreaSurcharge *float64 `json:"manifestRemoteAreaSurcharge,omitempty"`
    ManifestOverweightSurcharge *float64 `json:"manifestOverweightSurcharge,omitempty"`
    ManifestOriginalTotalFee    *float64 `json:"manifestOriginalTotalFee,omitempty"`

    // 原始记录信息
    OriginalCreatorID    *int64 `json:"originalCreatorId,omitempty"`
    OriginalCreatorName  *string `json:"originalCreatorName,omitempty"`
    OriginalCreateTime   string `json:"originalCreateTime"`
    SnapshotTime         string `json:"snapshotTime"`
}
```

**修改后**:

```go
type BillingAdjustmentSnapshotDTO struct {
    // ... 其他字段

    // 快照创建时间
    SnapshotTime         string `json:"snapshotTime"`
}
```

### 4. 服务层逻辑简化

**文件**: `internal/app/service/billing_service.go`

**修改内容**:

- 简化 `generateFinancialAdjustmentSnapshots` 方法，删除费用构成字段的计算和赋值逻辑
- 删除运费模板查询和费用重新计算的复杂逻辑

**修改前**:

```go
// 尝试获取用户的默认运费模板来重新计算费用构成
var template *entity.ShippingFeeTemplate
// ... 复杂的模板查询和费用计算逻辑

// 设置费用信息
snapshot.ManifestBaseFreightFee = &fees.BaseFreightFee
snapshot.ManifestFirstWeightFee = &fees.FirstWeightFee
snapshot.ManifestContinuedWeightFee = &fees.ContinuedWeightFee
snapshot.ManifestOverLengthSurcharge = &manifest.OverLengthSurcharge
snapshot.ManifestRemoteAreaSurcharge = &manifest.RemoteAreaSurcharge
snapshot.ManifestOverweightSurcharge = &manifest.OtherCost
snapshot.ManifestOriginalTotalFee = &originalTotalFee

// 设置原始记录信息
snapshot.OriginalCreatorID = adjustment.CreatorID
snapshot.OriginalCreateTime = adjustment.CreateTime
```

**修改后**:

```go
// 简化的快照生成，只保留基本信息
snapshot := &entity.BillingFinancialAdjustmentSnapshot{
    BillingRecordID:      billingRecordID,
    OriginalAdjustmentID: adjustment.ID,
    AdjustmentType:       adjustment.AdjustmentType,
    Description:          &adjustment.Description,
    AdditionalDetails:    additionalDetails,
    Amount:               adjustment.Amount,
    Currency:             adjustment.Currency,
    EffectiveDate:        adjustment.EffectiveDate,
    SnapshotTime:         time.Now(),
}
```

### 5. 仓储层修改

**文件**: `internal/adapter/persistence/billing_repository_impl.go`

**修改内容**:

- 在 `SaveBillingFinancialAdjustmentSnapshots` 方法中删除已删除字段的赋值
- 在 `FindBillingFinancialAdjustmentSnapshotsByBillingRecordID` 方法中删除已删除字段的转换
- 删除创建者信息的查询逻辑

**修改前**:

```go
pos[j] = &model.BillingFinancialAdjustmentSnapshotPO{
    // ... 其他字段

    // 运单费用构成快照
    ManifestBaseFreightFee:      snapshot.ManifestBaseFreightFee,
    ManifestFirstWeightFee:      snapshot.ManifestFirstWeightFee,
    ManifestContinuedWeightFee:  snapshot.ManifestContinuedWeightFee,
    ManifestOverLengthSurcharge: snapshot.ManifestOverLengthSurcharge,
    ManifestRemoteAreaSurcharge: snapshot.ManifestRemoteAreaSurcharge,
    ManifestOverweightSurcharge: snapshot.ManifestOverweightSurcharge,
    ManifestOriginalTotalFee:    snapshot.ManifestOriginalTotalFee,

    // 原始记录信息
    OriginalCreatorID:    snapshot.OriginalCreatorID,
    OriginalCreateTime:   snapshot.OriginalCreateTime,
    SnapshotTime:         snapshot.SnapshotTime,
}
```

**修改后**:

```go
pos[j] = &model.BillingFinancialAdjustmentSnapshotPO{
    // ... 其他字段

    // 快照创建时间
    SnapshotTime:         snapshot.SnapshotTime,
}
```

### 6. 财务服务修改

**文件**: `internal/domain/service/impl/finance_service_impl.go`

**修改内容**:

- 在 `convertAdjustmentSnapshotsToAdjustmentItems` 方法中，使用 `SnapshotTime` 替代 `OriginalCreateTime`

**修改前**:

```go
adjustmentItems[i] = dto.BillingAdjustmentItem{
    // ... 其他字段
    OriginalCreateTime:    snapshot.OriginalCreateTime,
}
```

**修改后**:

```go
adjustmentItems[i] = dto.BillingAdjustmentItem{
    // ... 其他字段
    OriginalCreateTime:    snapshot.SnapshotTime, // 使用快照时间替代原始创建时间
}
```

## 影响分析

### 1. 数据简化

- 财务调整快照不再保存运单的费用构成详情
- 不再保存原始记录的创建者信息
- 快照数据结构更加简洁，专注于调整记录本身

### 2. 性能优化

- 删除了复杂的运费模板查询和费用重新计算逻辑
- 减少了数据库存储空间
- 简化了快照生成过程，提高了性能

### 3. 功能保持

- 核心的财务调整信息仍然完整保存
- 运单基本信息快照功能保持不变
- Excel 导出功能正常工作

### 4. 向后兼容性

- API 接口保持兼容，只是返回的字段减少
- 现有的查询和展示逻辑仍然有效

## 验证结果

- ✅ 编译成功：所有代码修改通过编译验证
- ✅ 结构一致：实体、模型、DTO 结构保持一致
- ✅ 逻辑简化：删除了不必要的复杂计算逻辑
- ✅ 功能完整：核心财务调整功能保持完整

## 总结

本次修改成功删除了 `billing_financial_adjustment_snapshots` 表中的 9 个字段，并相应地简化了代码逻辑。修改后的系统更加简洁高效，同时保持了核心功能的完整性。删除的字段主要是运单费用构成的详细信息和原始记录创建者信息，这些信息在财务调整快照中的重要性相对较低，删除后不会影响主要业务功能。
