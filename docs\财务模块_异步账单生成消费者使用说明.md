# 财务模块 - 异步账单生成消费者使用说明

## 概述

账单生成消费者是处理异步账单生成任务的后台服务。它从 RabbitMQ 队列中接收任务消息，执行实际的账单生成逻辑，并更新任务状态。

## 系统架构

```
API接口 -> RabbitMQ队列 -> 消费者程序 -> 数据库
    ↓                        ↓             ↓
  任务创建              任务处理        状态更新
```

## 功能特性

### 1. 高可用性

- **多工作者支持**：支持并发处理多个任务
- **优雅关闭**：接收停止信号后完整处理当前任务
- **故障恢复**：程序重启后自动恢复队列连接
- **消息持久化**：确保任务不丢失

### 2. 容错机制

- **自动重试**：失败任务自动重试，最多 3 次
- **指数退避**：重试间隔逐渐增长（1s, 4s, 9s...）
- **超时控制**：单个任务最大处理时间 30 分钟
- **错误隔离**：单个客户失败不影响其他客户

### 3. 监控能力

- **实时进度**：处理过程中实时更新进度百分比
- **详细日志**：完整记录处理过程和错误信息
- **状态追踪**：PENDING -> PROCESSING -> COMPLETED/FAILED

## 安装和部署

### 1. 环境要求

- Go 1.19+
- RabbitMQ 3.8+
- MySQL 8.0+
- Windows 10/11 或 Linux

### 2. 构建程序

#### 使用 Makefile（推荐）

```bash
# 构建消费者程序
make build-consumer

# 或构建所有程序
make build
```

#### 手动构建

```bash
# Windows
go build -o bin/billing-consumer.exe cmd/billing-consumer/main.go

# Linux
go build -o bin/billing-consumer cmd/billing-consumer/main.go
```

### 3. 配置文件

确保 `configs/config.yaml` 中的 RabbitMQ 配置正确：

```yaml
rabbitmq:
  host: *************
  port: 5673
  virtual-host: /
  username: zebra
  password: banma1346797
```

## 启动和管理

### 1. 使用管理脚本（推荐）

#### Windows

```batch
# 启动消费者
scripts\start-billing-consumer.bat start

# 查看状态
scripts\start-billing-consumer.bat status

# 查看日志
scripts\start-billing-consumer.bat logs

# 实时查看日志
scripts\start-billing-consumer.bat logs -f

# 停止消费者
scripts\start-billing-consumer.bat stop

# 重启消费者
scripts\start-billing-consumer.bat restart
```

#### Linux

```bash
# 启动消费者
scripts/start-billing-consumer.sh start

# 查看状态
scripts/start-billing-consumer.sh status

# 查看日志
scripts/start-billing-consumer.sh logs

# 实时查看日志
scripts/start-billing-consumer.sh logs -f

# 停止消费者
scripts/start-billing-consumer.sh stop

# 重启消费者
scripts/start-billing-consumer.sh restart
```

### 2. 使用 Makefile

```bash
# 启动消费者（后台运行）
make start-consumer

# 查看状态
make status-consumer

# 查看日志
make logs-consumer

# 实时查看日志
make logs-consumer-follow

# 停止消费者
make stop-consumer

# 重启消费者
make restart-consumer
```

### 3. 直接运行

#### 开发模式

```bash
# 直接运行（控制台输出）
make dev-consumer

# 或
go run cmd/billing-consumer/main.go
```

#### 生产模式

```bash
# 先构建
make build-consumer

# 然后运行
bin/billing-consumer.exe
```

## 配置参数

### 1. 消费者配置

在 `internal/adapter/message_queue/billing_consumer.go` 中可以调整：

```go
// 默认配置
maxRetries:    3    // 最大重试次数
prefetchCount: 1    // 预获取消息数量
workers:       2    // 并发工作者数量
```

### 2. 运行时配置

可以通过代码修改消费者配置：

```go
consumer := message_queue.NewBillingConsumer(conn, processor, logger)
consumer.SetWorkers(4)         // 设置4个工作者
consumer.SetMaxRetries(5)      // 设置最大重试5次
consumer.SetPrefetchCount(2)   // 设置预获取2条消息
```

### 3. 超时设置

在 `billing_consumer.go` 中的 `handleMessage` 方法：

```go
// 创建处理上下文，设置超时
taskCtx, cancel := context.WithTimeout(ctx, 30*time.Minute) // 30分钟超时
```

## 监控和运维

### 1. 日志监控

#### 日志级别

- **INFO**：正常处理信息
- **WARN**：警告信息（如重试）
- **ERROR**：错误信息（需要关注）

#### 关键日志

```
启动消费者工作者 - 工作者启动
开始处理账单生成任务 - 任务开始
客户账单生成成功 - 单个客户完成
账单生成任务完成 - 整个任务完成
任务处理失败 - 任务失败（需要关注）
```

#### 查看日志

```bash
# 查看最新日志
tail -f logs/billing-consumer.log

# 搜索错误日志
grep "ERROR" logs/billing-consumer.log

# 搜索特定任务日志
grep "taskId=xxx" logs/billing-consumer.log
```

### 2. 系统监控

#### 进程监控

```bash
# 检查进程是否运行
ps aux | grep billing-consumer

# 检查内存使用
top -p `pgrep billing-consumer`
```

#### 队列监控

建议监控 RabbitMQ 管理界面中的：

- 队列长度（`billing.generation`）
- 消息处理速度
- 错误率

### 3. 数据库监控

监控 `billing_generation_tasks` 表：

- 任务状态分布
- 处理时间统计
- 失败任务分析

```sql
-- 查看任务状态统计
SELECT status, COUNT(*) as count
FROM billing_generation_tasks
GROUP BY status;

-- 查看最近失败的任务
SELECT task_id, error_message, submit_time
FROM billing_generation_tasks
WHERE status = 'FAILED'
ORDER BY submit_time DESC
LIMIT 10;

-- 查看处理时间统计
SELECT
  AVG(TIMESTAMPDIFF(SECOND, start_time, end_time)) as avg_duration,
  MAX(TIMESTAMPDIFF(SECOND, start_time, end_time)) as max_duration
FROM billing_generation_tasks
WHERE status = 'COMPLETED'
AND start_time IS NOT NULL
AND end_time IS NOT NULL;
```

## 性能优化

### 1. 并发优化

根据服务器性能调整工作者数量：

```go
// CPU密集型：工作者数 = CPU核心数
consumer.SetWorkers(runtime.NumCPU())

// IO密集型：工作者数 = CPU核心数 × 2
consumer.SetWorkers(runtime.NumCPU() * 2)
```

### 2. 内存优化

- 控制预获取消息数量，避免内存溢出
- 批量处理大量账单时考虑分批保存

### 3. 数据库优化

- 确保相关表有适当的索引
- 考虑使用数据库连接池
- 监控慢查询

## 故障排查

### 1. 常见问题

#### 消费者无法启动

```bash
# 检查配置文件
cat configs/config.yaml | grep -A 10 rabbitmq

# 检查RabbitMQ连接
telnet ************* 5673

# 检查数据库连接
mysql -h ************* -P 3336 -u root -p zebra_express_hub
```

#### 任务处理失败

```bash
# 查看错误日志
grep "ERROR" logs/billing-consumer.log | tail -20

# 检查数据库连接
grep "Failed to connect database" logs/billing-consumer.log

# 检查队列连接
grep "Failed to initialize RabbitMQ" logs/billing-consumer.log
```

#### 消息堆积

```bash
# 查看队列长度（RabbitMQ管理界面）
# 检查消费者是否正常运行
make status-consumer

# 考虑增加工作者数量
# 检查是否有死锁或长时间运行的任务
```

### 2. 调试模式

开发环境下可以启用详细日志：

```go
// 在main.go中设置
util.InitLogger("debug")  // 启用debug级别日志
```

### 3. 紧急处理

#### 停止所有处理

```bash
# 立即停止消费者
make stop-consumer

# 或直接杀死进程
pkill billing-consumer
```

#### 清空队列（慎用）

```bash
# 连接到RabbitMQ管理界面
# 手动清空 billing.generation 队列
```

## 最佳实践

### 1. 部署建议

- **生产环境**：使用系统服务方式部署
- **高可用**：部署多个消费者实例
- **负载均衡**：RabbitMQ 自动分发消息
- **监控告警**：设置进程监控和队列监控

### 2. 运维建议

- **定期检查**：每日检查消费者状态和日志
- **性能监控**：监控处理速度和资源使用
- **容量规划**：根据业务量调整消费者配置
- **备份恢复**：确保队列和数据库的备份策略

### 3. 安全建议

- **权限控制**：消费者使用专用数据库用户
- **网络安全**：限制 RabbitMQ 访问来源
- **日志安全**：避免在日志中记录敏感信息

## 版本升级

### 1. 兼容性检查

升级前检查：

- 消息格式是否兼容
- 数据库结构是否需要升级
- 配置文件是否需要更新

### 2. 升级步骤

```bash
# 1. 停止当前消费者
make stop-consumer

# 2. 备份当前版本
cp -r bin bin.backup

# 3. 构建新版本
make build-consumer

# 4. 启动新版本
make start-consumer

# 5. 检查状态
make status-consumer

# 6. 查看日志确认正常
make logs-consumer
```

### 3. 回滚方案

如果新版本有问题：

```bash
# 1. 停止新版本
make stop-consumer

# 2. 恢复旧版本
mv bin.backup bin

# 3. 启动旧版本
make start-consumer
```

## 技术支持

如果遇到问题，请提供以下信息：

1. **系统环境**：操作系统版本、Go 版本
2. **错误信息**：完整的错误日志
3. **配置信息**：相关配置文件内容
4. **复现步骤**：问题的详细复现步骤
5. **系统状态**：消费者状态、队列状态、数据库状态
