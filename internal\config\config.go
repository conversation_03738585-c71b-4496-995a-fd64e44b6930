package config

import (
	"fmt"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Server     Server     `yaml:"server" mapstructure:"server"`
	Database   Database   `yaml:"database" mapstructure:"database"`
	GoogleAPIs GoogleAPIs `yaml:"google_apis" mapstructure:"google_apis"`
	Redis      Redis      `yaml:"redis" mapstructure:"redis"`
	RabbitMQ   RabbitMQConfig `yaml:"rabbitmq" mapstructure:"rabbitmq"`
	OSS        OSSConfig  `yaml:"oss" mapstructure:"oss"`
}

// Server 服务配置
type Server struct {
	Port int    `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

// GoogleAPIs Google API相关配置
type GoogleAPIs struct {
	Translate GoogleTranslate `mapstructure:"translate"`
}

// GoogleTranslate Google翻译API配置
type GoogleTranslate struct {
	APIKey       string `mapstructure:"api_key"`        // API Key方式认证（可选）
	Project      string `mapstructure:"project"`        // Google Cloud项目ID
	CredentialFile string `mapstructure:"credential_file"` // JSON密钥文件路径（推荐）
	Enabled      bool   `mapstructure:"enabled"`        // 是否启用翻译服务
}

// Redis Redis配置
type Redis struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	Host        string `yaml:"host" mapstructure:"host"`               // RabbitMQ主机地址
	Port        int    `yaml:"port" mapstructure:"port"`               // RabbitMQ端口
	VirtualHost string `yaml:"virtual-host" mapstructure:"virtual-host"` // 虚拟主机
	Username    string `yaml:"username" mapstructure:"username"`       // 用户名
	Password    string `yaml:"password" mapstructure:"password"`       // 密码
}

// LoadConfig 加载配置
func LoadConfig(configPath string) (*Config, error) {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("read config error: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("unmarshal config error: %w", err)
	}

	return &config, nil
} 