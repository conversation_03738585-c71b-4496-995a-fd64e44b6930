package service

import (
	"context"
	"errors"
	"fmt"
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"go.uber.org/zap"
)

// CreateBillingCycleRequest 创建账期批次请求
type CreateBillingCycleRequest struct {
	CycleYear         int     `json:"cycleYear" binding:"required,min=2020,max=2100"`  // 账期年份
	CycleMonth        int     `json:"cycleMonth" binding:"required,min=1,max=12"`      // 账期月份 (1-12)
	CycleName         *string `json:"cycleName,omitempty"`                             // 账期名称（可选）
	GeneratedByUserID *int64  `json:"generatedByUserId,omitempty"`                     // 批次生成操作员ID
	Notes             *string `json:"notes,omitempty"`                                 // 批次备注
}

// CreateBillingCycleResponse 创建账期批次响应
type CreateBillingCycleResponse struct {
	BillingCycleID int64  `json:"billingCycleId"` // 生成的账期批次ID
	CycleName      string `json:"cycleName"`      // 账期名称
	Message        string `json:"message"`        // 响应消息
}

// ListBillingCyclesRequest 分页查询账期批次请求
type ListBillingCyclesRequest struct {
	Page                int     `form:"page" json:"page" default:"1"`                            // 页码
	PageSize            int     `form:"pageSize" json:"pageSize" binding:"max=100" default:"10"` // 每页数量
	CycleYear           *int    `form:"cycleYear" json:"cycleYear,omitempty"`                    // 账期年份（可选）
	CycleMonth          *int    `form:"cycleMonth" json:"cycleMonth,omitempty"`                  // 账期月份（可选）
	Status              *string `form:"status" json:"status,omitempty"`                          // 账期批次状态（可选）
	CycleName           *string `form:"cycleName" json:"cycleName,omitempty"`                    // 账期名称（可选，模糊查询）
	GeneratedByUserID   *int64  `form:"generatedByUserId" json:"generatedByUserId,omitempty"`    // 生成操作员ID（可选）
}

// BillingCycleDTO 账期批次DTO
type BillingCycleDTO struct {
	ID                        int64   `json:"id"`                                  // 账期批次ID
	CycleYear                 int     `json:"cycleYear"`                           // 账期年份
	CycleMonth                int     `json:"cycleMonth"`                          // 账期月份
	CycleName                 string  `json:"cycleName"`                           // 账期名称
	Status                    string  `json:"status"`                              // 账期批次状态
	StatusName                string  `json:"statusName"`                          // 账期批次状态名称
	TotalCustomersBilled      *int    `json:"totalCustomersBilled,omitempty"`      // 本周期内出账客户数
	TotalBillsGenerated       *int    `json:"totalBillsGenerated,omitempty"`       // 本周期内生成的账单总数
	TotalBilledAmount         *float64 `json:"totalBilledAmount,omitempty"`        // 本周期内账单总金额合计
	TotalAmountPaidInCycle    *float64 `json:"totalAmountPaidInCycle,omitempty"`   // 本周期内已付金额合计
	TotalBalanceDueInCycle    *float64 `json:"totalBalanceDueInCycle,omitempty"`   // 本周期内待支付金额合计
	GeneratedByUserID         *int64  `json:"generatedByUserId,omitempty"`         // 批次生成操作员ID
	GeneratedByNickname       *string `json:"generatedByNickname,omitempty"`       // 批次生成操作员昵称
	GenerationStartTime       *string `json:"generationStartTime,omitempty"`       // 批次账单开始生成时间
	GenerationEndTime         *string `json:"generationEndTime,omitempty"`         // 批次账单完成生成时间
	Notes                     *string `json:"notes,omitempty"`                     // 批次备注
	CreateTime                string  `json:"createTime"`                          // 记录创建时间
	UpdateTime                string  `json:"updateTime"`                          // 记录更新时间
}

// ListBillingCyclesResponse 分页查询账期批次响应
type ListBillingCyclesResponse struct {
	Total int64              `json:"total"` // 总数
	List  []*BillingCycleDTO `json:"list"`  // 账期批次列表
}

// GetBillingCycleDetailRequest 获取账期批次详情请求
type GetBillingCycleDetailRequest struct {
	BillingCycleID int64 `uri:"billingCycleId" binding:"required"` // 账期批次ID
}

// GetBillingCycleDetailResponse 获取账期批次详情响应
type GetBillingCycleDetailResponse struct {
	BillingCycle *BillingCycleDTO `json:"billingCycle"` // 账期批次详情
}

// BillingCycleFilters 账期批次查询过滤器
type BillingCycleFilters struct {
	CycleYear           *int    // 账期年份
	CycleMonth          *int    // 账期月份
	Status              *string // 账期批次状态
	CycleName           *string // 账期名称（模糊查询）
	GeneratedByUserID   *int64  // 生成操作员ID
}

// BillingCycleService 账期批次服务接口
type BillingCycleService interface {
	// CreateBillingCycle 创建账期批次
	CreateBillingCycle(ctx context.Context, req *CreateBillingCycleRequest) (*CreateBillingCycleResponse, int, error)

	// ListBillingCycles 分页查询账期批次列表
	ListBillingCycles(ctx context.Context, req *ListBillingCyclesRequest) (*ListBillingCyclesResponse, int, error)

	// GetBillingCycleDetail 根据账期批次ID获取账期批次详情
	GetBillingCycleDetail(ctx context.Context, req *GetBillingCycleDetailRequest) (*GetBillingCycleDetailResponse, int, error)
}

// BillingCycleServiceImpl 账期批次服务实现
type BillingCycleServiceImpl struct {
	billingCycleRepo repository.BillingCycleRepository
	userRepo         repository.UserRepository
}

// NewBillingCycleService 创建账期批次服务
func NewBillingCycleService(
	billingCycleRepo repository.BillingCycleRepository,
	userRepo repository.UserRepository,
) BillingCycleService {
	return &BillingCycleServiceImpl{
		billingCycleRepo: billingCycleRepo,
		userRepo:         userRepo,
	}
}

// CreateBillingCycle 创建账期批次
func (s *BillingCycleServiceImpl) CreateBillingCycle(ctx context.Context, req *CreateBillingCycleRequest) (*CreateBillingCycleResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if err := s.validateCreateBillingCycleRequest(req); err != nil {
		if logger != nil {
			logger.Warn("Invalid create billing cycle request parameters", zap.Error(err))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 检查指定年月的账期批次是否已存在
	exists, err := s.billingCycleRepo.CheckCycleExists(ctx, req.CycleYear, req.CycleMonth)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to check cycle exists", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("检查账期批次是否存在失败")
	}

	if exists {
		return nil, valueobject.ERROR_INVALID_PARAMETER, fmt.Errorf("%d年%d月的账期批次已存在", req.CycleYear, req.CycleMonth)
	}

	// 3. 生成账期名称（如果未提供）
	cycleName := s.generateCycleName(req.CycleYear, req.CycleMonth, req.CycleName)

	// 4. 创建账期批次实体
	now := time.Now()
	billingCycle := &entity.BillingCycle{
		CycleYear:         req.CycleYear,
		CycleMonth:        req.CycleMonth,
		CycleName:         &cycleName,
		Status:            entity.BillingCycleStatusPending,
		GeneratedByUserID: req.GeneratedByUserID,
		Notes:             req.Notes,
		CreateTime:        now,
		UpdateTime:        now,
	}

	// 5. 保存账期批次
	if err := s.billingCycleRepo.SaveBillingCycle(ctx, billingCycle); err != nil {
		if logger != nil {
			logger.Error("Failed to save billing cycle", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("保存账期批次失败")
	}

	if logger != nil {
		logger.Info("Successfully created billing cycle",
			zap.Int64("billingCycleId", billingCycle.ID),
			zap.Int("cycleYear", req.CycleYear),
			zap.Int("cycleMonth", req.CycleMonth),
			zap.String("cycleName", cycleName))
	}

	return &CreateBillingCycleResponse{
		BillingCycleID: billingCycle.ID,
		CycleName:      cycleName,
		Message:        fmt.Sprintf("成功创建%s账期批次", cycleName),
	}, valueobject.SUCCESS, nil
}

// ListBillingCycles 分页查询账期批次列表
func (s *BillingCycleServiceImpl) ListBillingCycles(ctx context.Context, req *ListBillingCyclesRequest) (*ListBillingCyclesResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if err := s.validateListBillingCyclesRequest(req); err != nil {
		if logger != nil {
			logger.Warn("Invalid list billing cycles request parameters", zap.Error(err))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 构建查询过滤器
	filters := &BillingCycleFilters{
		CycleYear:           req.CycleYear,
		CycleMonth:          req.CycleMonth,
		Status:              req.Status,
		CycleName:           req.CycleName,
		GeneratedByUserID:   req.GeneratedByUserID,
	}

	// 3. 查询账期批次总数
	total, err := s.billingCycleRepo.CountBillingCycles(ctx, filters)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to count billing cycles", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账期批次总数失败")
	}

	// 4. 分页查询账期批次
	cycles, err := s.billingCycleRepo.FindBillingCycles(ctx, filters, req.Page, req.PageSize)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find billing cycles", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账期批次失败")
	}

	// 5. 收集用户ID并批量查询用户信息
	userIDs := make([]int64, 0)
	userIDSet := make(map[int64]bool)

	for _, cycle := range cycles {
		if cycle.GeneratedByUserID != nil && !userIDSet[*cycle.GeneratedByUserID] {
			userIDs = append(userIDs, *cycle.GeneratedByUserID)
			userIDSet[*cycle.GeneratedByUserID] = true
		}
	}

	// 6. 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find users by IDs", zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 7. 构建响应DTO
	dtos := make([]*BillingCycleDTO, len(cycles))
	for i, cycle := range cycles {
		dto := &BillingCycleDTO{
			ID:                        cycle.ID,
			CycleYear:                 cycle.CycleYear,
			CycleMonth:                cycle.CycleMonth,
			CycleName:                 s.getCycleName(cycle),
			Status:                    string(cycle.Status),
			StatusName:                s.getBillingCycleStatusName(cycle.Status),
			TotalCustomersBilled:      cycle.TotalCustomersBilled,
			TotalBillsGenerated:       cycle.TotalBillsGenerated,
			TotalBilledAmount:         cycle.TotalBilledAmount,
			TotalAmountPaidInCycle:    cycle.TotalAmountPaidInCycle,
			TotalBalanceDueInCycle:    cycle.TotalBalanceDueInCycle,
			GeneratedByUserID:         cycle.GeneratedByUserID,
			Notes:                     cycle.Notes,
			CreateTime:                cycle.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime:                cycle.UpdateTime.Format("2006-01-02 15:04:05"),
		}

		// 设置时间字段
		if cycle.GenerationStartTime != nil {
			startTimeStr := cycle.GenerationStartTime.Format("2006-01-02 15:04:05")
			dto.GenerationStartTime = &startTimeStr
		}
		if cycle.GenerationEndTime != nil {
			endTimeStr := cycle.GenerationEndTime.Format("2006-01-02 15:04:05")
			dto.GenerationEndTime = &endTimeStr
		}

		// 设置生成操作员昵称
		if cycle.GeneratedByUserID != nil {
			if operator, exists := userMap[*cycle.GeneratedByUserID]; exists {
				dto.GeneratedByNickname = &operator.Nickname
			}
		}

		dtos[i] = dto
	}

	if logger != nil {
		logger.Info("Successfully listed billing cycles",
			zap.Int("page", req.Page),
			zap.Int("pageSize", req.PageSize),
			zap.Int64("total", total),
			zap.Int("count", len(dtos)))
	}

	return &ListBillingCyclesResponse{
		Total: total,
		List:  dtos,
	}, valueobject.SUCCESS, nil
}

// GetBillingCycleDetail 根据账期批次ID获取账期批次详情
func (s *BillingCycleServiceImpl) GetBillingCycleDetail(ctx context.Context, req *GetBillingCycleDetailRequest) (*GetBillingCycleDetailResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.BillingCycleID <= 0 {
		if logger != nil {
			logger.Warn("Invalid billing cycle ID", zap.Int64("billingCycleId", req.BillingCycleID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("账期批次ID必须大于0")
	}

	// 2. 查询账期批次
	cycle, err := s.billingCycleRepo.FindBillingCycleByID(ctx, req.BillingCycleID)
	if err != nil {
		if logger != nil {
			logger.Warn("Billing cycle not found",
				zap.Int64("billingCycleId", req.BillingCycleID),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("账期批次不存在")
	}

	// 3. 查询相关用户信息
	userMap := make(map[int64]*entity.User)
	if cycle.GeneratedByUserID != nil {
		users, err := s.userRepo.FindByIDs(ctx, []int64{*cycle.GeneratedByUserID})
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find users by IDs", zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 4. 构建详情DTO
	dto := &BillingCycleDTO{
		ID:                        cycle.ID,
		CycleYear:                 cycle.CycleYear,
		CycleMonth:                cycle.CycleMonth,
		CycleName:                 s.getCycleName(cycle),
		Status:                    string(cycle.Status),
		StatusName:                s.getBillingCycleStatusName(cycle.Status),
		TotalCustomersBilled:      cycle.TotalCustomersBilled,
		TotalBillsGenerated:       cycle.TotalBillsGenerated,
		TotalBilledAmount:         cycle.TotalBilledAmount,
		TotalAmountPaidInCycle:    cycle.TotalAmountPaidInCycle,
		TotalBalanceDueInCycle:    cycle.TotalBalanceDueInCycle,
		GeneratedByUserID:         cycle.GeneratedByUserID,
		Notes:                     cycle.Notes,
		CreateTime:                cycle.CreateTime.Format("2006-01-02 15:04:05"),
		UpdateTime:                cycle.UpdateTime.Format("2006-01-02 15:04:05"),
	}

	// 设置时间字段
	if cycle.GenerationStartTime != nil {
		startTimeStr := cycle.GenerationStartTime.Format("2006-01-02 15:04:05")
		dto.GenerationStartTime = &startTimeStr
	}
	if cycle.GenerationEndTime != nil {
		endTimeStr := cycle.GenerationEndTime.Format("2006-01-02 15:04:05")
		dto.GenerationEndTime = &endTimeStr
	}

	// 设置生成操作员信息
	if cycle.GeneratedByUserID != nil {
		if operator, exists := userMap[*cycle.GeneratedByUserID]; exists {
			dto.GeneratedByNickname = &operator.Nickname
		}
	}

	if logger != nil {
		logger.Info("Successfully retrieved billing cycle detail",
			zap.Int64("billingCycleId", req.BillingCycleID),
			zap.String("cycleName", dto.CycleName))
	}

	return &GetBillingCycleDetailResponse{
		BillingCycle: dto,
	}, valueobject.SUCCESS, nil
}

// validateCreateBillingCycleRequest 验证创建账期批次请求参数
func (s *BillingCycleServiceImpl) validateCreateBillingCycleRequest(req *CreateBillingCycleRequest) error {
	// 验证年份范围
	if req.CycleYear < 2020 || req.CycleYear > 2100 {
		return errors.New("账期年份必须在2020-2100之间")
	}

	// 验证月份范围
	if req.CycleMonth < 1 || req.CycleMonth > 12 {
		return errors.New("账期月份必须在1-12之间")
	}

	// 验证账期名称长度
	if req.CycleName != nil && len(*req.CycleName) > 100 {
		return errors.New("账期名称长度不能超过100个字符")
	}

	// 验证备注长度
	if req.Notes != nil && len(*req.Notes) > 1000 {
		return errors.New("备注长度不能超过1000个字符")
	}

	return nil
}

// validateListBillingCyclesRequest 验证分页查询账期批次请求参数
func (s *BillingCycleServiceImpl) validateListBillingCyclesRequest(req *ListBillingCyclesRequest) error {
	// 验证分页参数
	if req.Page <= 0 {
		return errors.New("页码必须大于0")
	}

	if req.PageSize <= 0 || req.PageSize > 100 {
		return errors.New("每页数量必须在1-100之间")
	}

	// 验证年份范围
	if req.CycleYear != nil && (*req.CycleYear < 2020 || *req.CycleYear > 2100) {
		return errors.New("账期年份必须在2020-2100之间")
	}

	// 验证月份范围
	if req.CycleMonth != nil && (*req.CycleMonth < 1 || *req.CycleMonth > 12) {
		return errors.New("账期月份必须在1-12之间")
	}

	return nil
}

// generateCycleName 生成账期名称
func (s *BillingCycleServiceImpl) generateCycleName(year, month int, customName *string) string {
	if customName != nil && *customName != "" {
		return *customName
	}

	return fmt.Sprintf("%d年%d月账期", year, month)
}

// getCycleName 获取账期名称
func (s *BillingCycleServiceImpl) getCycleName(cycle *entity.BillingCycle) string {
	if cycle.CycleName != nil && *cycle.CycleName != "" {
		return *cycle.CycleName
	}

	return fmt.Sprintf("%d年%d月账期", cycle.CycleYear, cycle.CycleMonth)
}

// getBillingCycleStatusName 获取账期批次状态名称
func (s *BillingCycleServiceImpl) getBillingCycleStatusName(status entity.BillingCycleStatus) string {
	switch status {
	case entity.BillingCycleStatusPending:
		return "待处理"
	case entity.BillingCycleStatusProcessing:
		return "处理中"
	case entity.BillingCycleStatusCompleted:
		return "已完成"
	case entity.BillingCycleStatusFailed:
		return "失败"
	case entity.BillingCycleStatusCancelled:
		return "已取消"
	default:
		return fmt.Sprintf("未知状态(%s)", string(status))
	}
} 