package handler

import (
	"net/http"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// ConfigHandler 配置管理处理器
type ConfigHandler struct {
	configService service.ConfigService
}

// NewConfigHandler 创建配置管理处理器
func NewConfigHandler(configService service.ConfigService) *ConfigHandler {
	return &ConfigHandler{
		configService: configService,
	}
}

// GetUserShippingFeeTemplates 根据用户ID查询三种类型的运费模板
// @Summary 根据用户ID查询三种类型的运费模板
// @Description 查询指定用户配置的所有类型的运费模板，包括普通模板(1)、带电模板(2)、投函模板(3)
// @Tags 配置管理
// @Accept json
// @Produce json
// @Param userId path int true "用户ID"
// @Success 200 {object} util.Response{data=service.GetUserShippingFeeTemplatesResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "用户不存在或未配置模板"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /config/shipping-fee-templates/user/{userId} [get]
// @Security ApiKeyAuth
func (h *ConfigHandler) GetUserShippingFeeTemplates(c *gin.Context) {
	var req service.GetUserShippingFeeTemplatesRequest
	
	// 绑定路径参数
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "路径参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层查询用户运费模板
	resp, code, err := h.configService.GetUserShippingFeeTemplates(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
} 