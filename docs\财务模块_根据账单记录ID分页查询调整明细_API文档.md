# 根据账单记录 ID 分页查询调整明细 API 文档

## 接口概述

该接口用于根据指定的账单记录 ID，分页查询该账单下的所有财务调整快照明细记录。每个快照记录包含完整的财务调整信息和关联的运单信息快照，确保财务调整的上下文信息完整保存。

## 接口信息

- **请求方法**: GET
- **请求路径**: `/api/v1/finance/billing/records/{billingRecordId}/adjustment-snapshots`
- **需要认证**: 是

## 请求参数

### 路径参数 (Path Parameters)

| 参数名          | 类型  | 必填 | 说明        |
| --------------- | ----- | ---- | ----------- |
| billingRecordId | int64 | 是   | 账单记录 ID |

### 查询参数 (Query Parameters)

| 参数名   | 类型 | 必填 | 默认值 | 说明                     |
| -------- | ---- | ---- | ------ | ------------------------ |
| page     | int  | 否   | 1      | 页码，最小值为 1         |
| pageSize | int  | 否   | 20     | 每页数量，取值范围 1-100 |

## 业务逻辑说明

### 查询流程

1. **参数验证**:

   - 验证账单记录 ID 是否为正整数
   - 验证分页参数的合法性

2. **权限校验**:

   - 验证账单记录是否存在
   - 确保当前用户有查看该账单的权限

3. **快照查询**:

   - 根据账单记录 ID 查询所有相关的财务调整快照
   - 按快照 ID 升序排列
   - 支持分页查询

4. **数据组装**:
   - 转换为前端友好的 DTO 格式
   - 补充调整类型名称、创建者昵称等扩展信息
   - 格式化时间字段

### 快照数据内容

每条财务调整快照记录包含以下信息：

#### 财务调整信息

- **调整类型**: 调整类型代码和名称（如：增加、减少）
- **调整金额**: 调整的具体金额
- **调整描述**: 调整的原因或说明
- **生效日期**: 费用实际发生或确认的日期
- **附加详情**: 特定调整类型的额外信息

#### 关联运单信息快照

- **运单基础信息**: 快递单号、订单号、转单号、客户订单号
- **时间信息**: 运单创建时间、发货时间
- **收件人信息**: 收件人姓名
- **物品信息**: 物品描述、货物类型

#### 运单尺寸重量信息快照

- **重量信息**: 实际重量、体积重、计费重量
- **尺寸信息**: 长、宽、高、三边和

#### 运单费用构成快照

- **基础费用**: 基础运费、首重费用、续重费用
- **附加费用**: 超长费、偏远费
- **原始总费用**: 调整前的运单总费用

#### 原始记录信息

- **创建者信息**: 原始调整记录的创建者 ID 和昵称
- **时间信息**: 原始记录创建时间、快照创建时间

## 响应结果

### 成功响应

**HTTP 状态码**: 200

**响应体结构**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "total": 15,
    "list": [
      {
        "id": 1,
        "billingRecordId": 123,
        "originalAdjustmentId": 456,
        "adjustmentType": "COMPENSATION",
        "adjustmentTypeName": "赔偿",
        "description": "运费补差",
        "additionalDetails": {
          "reason": "重量重新测量",
          "originalWeight": 1.2,
          "newWeight": 1.8
        },
        "amount": 15.5,
        "currency": "CNY",
        "effectiveDate": "2024-12-01",
        "manifestId": 789,
        "manifestExpressNumber": "SF1234567890",
        "manifestOrderNo": "ORD20241201001",
        "manifestTransferredTrackingNumber": "TRK20241201001",
        "manifestCustomerOrderNumber": "CUST20241201001",
        "manifestCreateTime": "2024-11-28 10:30:00",
        "manifestShipmentTime": "2024-11-29 14:20:00",
        "manifestReceiverName": "张三",
        "manifestItemDescription": "电子产品, 手机配件",
        "manifestCargoType": "普通货物",
        "manifestWeight": 1.8,
        "manifestLength": 25.0,
        "manifestWidth": 15.0,
        "manifestHeight": 8.0,
        "manifestSumOfSides": 48.0,
        "manifestDimensionalWeight": 0.5,
        "manifestChargeableWeight": 1.8,
        "manifestBaseFreightFee": 12.0,
        "manifestFirstWeightFee": 8.0,
        "manifestContinuedWeightFee": 4.0,
        "manifestOverLengthSurcharge": 0.0,
        "manifestRemoteAreaSurcharge": 0.0,
        "manifestOriginalTotalFee": 12.0,
        "originalCreatorId": 1001,
        "originalCreatorName": "李四",
        "originalCreateTime": "2024-12-01 09:15:00",
        "snapshotTime": "2024-12-01 16:30:00"
      }
    ]
  }
}
```

### 错误响应

#### 参数错误 (400)

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "账单记录ID必须大于0",
  "data": null
}
```

#### 账单记录不存在 (404)

```json
{
  "success": false,
  "errorCode": 100006,
  "errorMessage": "账单记录不存在",
  "data": null
}
```

#### 服务器内部错误 (500)

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "查询账单调整明细失败",
  "data": null
}
```

## 使用示例

### 示例 1: 查询第一页调整明细

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records/123/adjustment-snapshots?page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 2: 查询指定页码的调整明细

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records/123/adjustment-snapshots?page=2&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### JavaScript 调用示例

```javascript
async function getBillingAdjustmentSnapshots(
  billingRecordId,
  page = 1,
  pageSize = 20
) {
  try {
    const response = await fetch(
      `/api/v1/finance/billing/records/${billingRecordId}/adjustment-snapshots?page=${page}&pageSize=${pageSize}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    const result = await response.json();

    if (result.success) {
      console.log("调整明细列表:", result.data.list);
      console.log("总数:", result.data.total);
      return result.data;
    } else {
      console.error("获取调整明细失败:", result.errorMessage);
      throw new Error(result.errorMessage);
    }
  } catch (error) {
    console.error("请求失败:", error);
    throw error;
  }
}

// 使用示例
getBillingAdjustmentSnapshots(123, 1, 20)
  .then((data) => {
    console.log(`共找到 ${data.total} 条调整明细`);
    data.list.forEach((snapshot, index) => {
      console.log(
        `${index + 1}. 调整类型: ${snapshot.adjustmentTypeName}, 金额: ${
          snapshot.amount
        }, 运单号: ${snapshot.manifestExpressNumber}`
      );
    });
  })
  .catch((error) => {
    console.error("获取调整明细失败:", error);
  });
```

## 数据字段说明

### 调整类型映射

| 代码         | 名称 | 说明                           |
| ------------ | ---- | ------------------------------ |
| COMPENSATION | 赔偿 | 赔偿类调整                     |
| REASSIGNMENT | 改派 | 改派类调整                     |
| DESTRUCTION  | 销毁 | 销毁类调整                     |
| RETURN       | 退回 | 退回类调整                     |
| FEE          | 费用 | 费用类调整（如操作费、附加费） |
| REBATE       | 返款 | 返款、折扣类调整               |
| OTHER        | 其他 | 其他类型的调整                 |

### 货物类型说明

- **普通货物**: 一般商品，无特殊运输要求
- **带电货物**: 含有电池或电子设备的货物
- **投函货物**: 可通过邮政投函方式配送的小件货物

### 时间字段格式

- **日期字段**: `yyyy-MM-dd` 格式（如：2024-12-01）
- **时间字段**: `yyyy-MM-dd HH:mm:ss` 格式（如：2024-12-01 16:30:00）

## 相关接口

### 关联接口

- `GET /finance/billing/records` - 分页查询账单记录列表
- `GET /finance/billing/records/{billingRecordId}` - 获取账单记录详情
- `GET /finance/billing/records/{billingRecordId}/items` - 查询账单明细列表
- `POST /finance/billing/generate` - 生成账单

### 典型使用流程

1. 通过账单列表接口获取账单概要信息
2. 使用账单详情接口获取特定账单的详细信息
3. 使用账单明细接口查看运单明细
4. 使用本接口查看财务调整明细

## 技术实现

### 代码结构

```
internal/
├── app/service/billing_service.go                    # 服务层实现
├── handler/finance_handler.go                        # 处理器层实现
├── router/router.go                                  # 路由配置
├── adapter/persistence/billing_repository_impl.go   # 数据访问层
└── domain/
    ├── entity/billing_record.go                     # 领域实体
    └── repository/billing_repository.go             # 仓储接口
```

### 关键方法

- **服务层**: `BillingService.ListBillingAdjustmentSnapshots()`
- **处理器层**: `FinanceHandler.ListBillingAdjustmentSnapshots()`
- **仓储层**: `BillingRepository.FindBillingFinancialAdjustmentSnapshotsByBillingRecordID()`

### 数据流程

```
HTTP请求 → 路由 → 处理器 → 服务层 → 仓储层 → 数据库
                ↓
HTTP响应 ← JSON序列化 ← DTO转换 ← 实体对象 ← 查询结果
```

## 注意事项

### 1. 性能考虑

- 接口支持分页查询，避免一次性加载大量数据
- 批量查询用户信息，减少数据库访问次数
- 如果用户信息查询失败，不影响主要功能

### 2. 数据完整性

- 快照记录保存了调整发生时的完整运单信息
- 即使原始运单或调整记录被修改或删除，快照信息保持不变
- 确保财务调整的历史可追溯性

### 3. 错误处理

- 参数验证失败返回 400 错误
- 账单记录不存在返回 404 错误
- 系统异常返回 500 错误
- 用户信息查询失败不影响主要功能

### 4. 安全考虑

- 需要 JWT 认证
- 建议添加权限控制（如只能查看自己的账单调整明细）
- 敏感信息需要权限控制

## 扩展建议

### 1. 权限控制

可以在处理器中添加权限验证：

```go
// 检查用户是否有权限查看该账单的调整明细
if !hasPermission(userID, billingRecordID) {
    util.ResponseError(c, valueobject.ERROR_FORBIDDEN_ACCESS, "无权限访问该账单调整明细", http.StatusForbidden)
    return
}
```

### 2. 缓存优化

对于频繁查询的调整明细，可以考虑添加缓存：

```go
// 先从缓存查询
cacheKey := fmt.Sprintf("billing_adjustments:%d:%d:%d", billingRecordID, page, pageSize)
if cachedData := cache.Get(cacheKey); cachedData != nil {
    return cachedData, nil
}
```

### 3. 导出功能

可以添加调整明细的导出功能：

```go
// 导出账单调整明细为Excel
func (h *FinanceHandler) ExportBillingAdjustmentSnapshots(c *gin.Context) {
    // 实现导出逻辑
}
```

### 4. 审计日志

可以添加查询日志记录：

```go
logger.Info("User viewed billing adjustment snapshots",
    zap.Int64("userId", userID),
    zap.Int64("billingRecordId", billingRecordID),
    zap.Int("page", page),
    zap.Int("pageSize", pageSize))
```

## 总结

该接口提供了完整的账单财务调整明细查询功能，支持分页查询，包含丰富的快照信息，确保财务调整的历史可追溯性。通过合理的数据结构设计和性能优化，为财务管理提供了强有力的支持。
