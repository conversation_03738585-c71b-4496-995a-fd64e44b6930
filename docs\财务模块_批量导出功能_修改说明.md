# 财务模块 - 批量导出功能修改说明

## 修改概述

根据用户需求，在现有的单个账单记录导出功能基础上，新增**批量导出账单记录 Excel** 功能。批量导出功能能够同时导出多个账单记录的 Excel 文件，并自动打包成 zip 文件供下载。

## 新增功能特点

- **批量处理**: 支持一次性导出最多 100 个账单记录
- **自动打包**: 将所有 Excel 文件打包成 zip 文件
- **错误容忍**: 部分文件失败不影响其他文件的生成
- **文件管理**: 自动添加序号前缀，确保文件名唯一
- **错误报告**: 如有失败文件，自动生成错误报告

## 实现架构

### 1. 新增 DTO 结构

**文件**: `internal/domain/dto/finance_dto.go`

```go
// BatchBillingRecordExportRequest 批量导出账单记录Excel请求参数
type BatchBillingRecordExportRequest struct {
	BillingRecordIDs []int64 `json:"billingRecordIds" binding:"required,min=1,max=100" example:"[123,124,125]"` // 账单记录ID列表（最多100个）
}
```

### 2. 扩展服务接口

**文件**: `internal/domain/service/finance_service.go`

```go
// BatchGenerateBillingRecordExcel 批量生成账单记录Excel并打包成zip文件
BatchGenerateBillingRecordExcel(ctx context.Context, req dto.BatchBillingRecordExportRequest) ([]byte, string, error)
```

### 3. 服务实现

**文件**: `internal/domain/service/impl/finance_service_impl.go`

- 新增批量导出方法实现
- 使用 `archive/zip` 包进行文件打包
- 实现错误容忍机制
- 生成详细的日志记录

### 4. 新增 HTTP 处理器

**文件**: `internal/handler/finance_handler.go`

```go
// BatchExportBillingRecordExcel 批量导出账单记录Excel
func (h *FinanceHandler) BatchExportBillingRecordExcel(c *gin.Context)
```

### 5. 添加路由

**文件**: `internal/router/router.go`

```go
financeRoutes.POST("/billing-record/batch-export", financeHandler.BatchExportBillingRecordExcel)
```

## 核心处理逻辑

### 1. 参数验证

- 检查账单记录 ID 列表不为空
- 验证数量不超过 100 个
- 确保所有 ID 都大于 0

### 2. 批量生成流程

```go
for each billingRecordID:
    1. 调用单个导出接口生成 Excel
    2. 添加序号前缀确保文件名唯一
    3. 将文件添加到 zip 包中
    4. 记录成功/失败状态
```

### 3. 错误处理策略

- **继续处理**: 单个文件失败时继续处理其他文件
- **错误收集**: 收集所有失败文件的错误信息
- **错误报告**: 生成 `导出错误报告.txt` 文件包含在 zip 中
- **最低保证**: 至少一个文件成功才返回 zip，否则返回错误

### 4. 文件命名规则

- **Excel 文件**: `001_客户A_账单_BILL202401001_2024-01-15.xlsx`
- **zip 文件**: `批量账单导出_20240115_143052_3个文件.zip`
- **错误报告**: `导出错误报告.txt`

## 技术实现细节

### 1. ZIP 文件生成

```go
var zipBuffer bytes.Buffer
zipWriter := zip.NewWriter(&zipBuffer)
defer zipWriter.Close()

// 为每个 Excel 文件创建 zip 条目
fileWriter, err := zipWriter.Create(uniqueFileName)
fileWriter.Write(excelData)
```

### 2. 内存管理

- 使用 `bytes.Buffer` 在内存中构建 zip 文件
- 避免临时文件写入磁盘
- 及时释放单个 Excel 文件的内存

### 3. 错误容忍机制

```go
for _, billingRecordID := range req.BillingRecordIDs {
    excelData, fileName, err := s.GenerateBillingRecordExcel(ctx, singleReq)
    if err != nil {
        // 记录错误但继续处理
        errorMessages = append(errorMessages, errorMessage)
        continue
    }
    // 处理成功的文件
    successCount++
}
```

## API 接口信息

- **路径**: `POST /api/v1/finance/billing-record/batch-export`
- **请求体**: `{"billingRecordIds": [123, 124, 125]}`
- **响应类型**: `application/zip`
- **最大限制**: 100 个账单记录 ID

## 依赖关系

本功能完全基于现有的单个导出接口实现：

```
BatchGenerateBillingRecordExcel
    └── 循环调用 GenerateBillingRecordExcel
        └── 复用所有现有的 Excel 生成逻辑
```

## 性能考虑

### 1. 处理时间

- 单个文件: 100-500ms
- 批量处理: 约为单个文件时间 × 文件数量
- 建议限制: 单次导出不超过 50 个文件

### 2. 内存使用

- 峰值内存约为: 单个 Excel 文件大小 × 2-3 倍
- zip 压缩比: 通常能压缩 10-30%

### 3. 网络传输

- 单个 Excel: 50KB-2MB
- 批量 zip: 通常不超过 200MB

## 日志记录

系统会记录详细的处理日志：

```go
zap.L().Info("成功添加文件到zip包",
    zap.Int64("billingRecordID", billingRecordID),
    zap.String("fileName", uniqueFileName))

zap.L().Info("批量导出完成",
    zap.Int("totalRequested", len(req.BillingRecordIDs)),
    zap.Int("successCount", successCount),
    zap.Int("errorCount", len(errorMessages)))
```

## 版本信息

- **添加日期**: 2024-01-15
- **功能版本**: v1.0.0
- **修改类型**: 新增功能
- **影响范围**: 财务模块导出功能扩展

## 测试建议

1. **正常流程**: 测试 2-5 个有效的账单记录 ID
2. **边界条件**: 测试 1 个和 100 个账单记录 ID
3. **错误处理**: 测试包含无效 ID 的混合场景
4. **性能测试**: 测试 50+ 个账单记录的处理时间
5. **并发测试**: 测试多用户同时进行批量导出

## 相关文档

- [批量导出 API 文档](./财务模块_批量导出账单记录Excel_API文档.md)
- [单个导出 API 文档](./财务模块_根据账单记录ID导出Excel_API文档.md)
