package persistence

import (
	"context"
	"time"

	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// ProblemTicketRepositoryImpl 问题工单仓储实现
type ProblemTicketRepositoryImpl struct {
	db *gorm.DB
}

// NewProblemTicketRepository 创建问题工单仓储
func NewProblemTicketRepository(db *gorm.DB) repository.ProblemTicketRepository {
	return &ProblemTicketRepositoryImpl{db: db}
}

// FindPaginated 分页查询问题工单
func (r *ProblemTicketRepositoryImpl) FindPaginated(ctx context.Context, queryParams *repository.ProblemTicketQueryParams, page, pageSize int) ([]*entity.ProblemTicket, int64, error) {
	var ticketPOs []*model.ProblemTicketPO
	var total int64
	
	offset := (page - 1) * pageSize
	
	// 构建查询条件
	query := r.db.Model(&model.ProblemTicketPO{})
	
	// 应用过滤条件
	if queryParams != nil {
		if queryParams.Status != "" {
			query = query.Where("status = ?", queryParams.Status)
		}
		
		if queryParams.ProblemTypeCode != "" {
			query = query.Where("problem_type_code = ?", queryParams.ProblemTypeCode)
		}
		
		if queryParams.TrackingNumber != "" {
			query = query.Where("tracking_number LIKE ?", "%"+queryParams.TrackingNumber+"%")
		}
		
		if queryParams.CustomerID > 0 {
			query = query.Where("customer_account_id = ?", queryParams.CustomerID)
		}
		
		if queryParams.AssignedUserID > 0 {
			query = query.Where("assigned_to_user_id = ?", queryParams.AssignedUserID)
		}
		
		if queryParams.ManifestID > 0 {
			query = query.Where("manifest_id = ?", queryParams.ManifestID)
		}
		
		if queryParams.Priority > 0 {
			query = query.Where("priority = ?", queryParams.Priority)
		}
		
		// 时间范围筛选
		if queryParams.StartTime != "" {
			query = query.Where("create_time >= ?", queryParams.StartTime)
		}
		
		if queryParams.EndTime != "" {
			query = query.Where("create_time <= ?", queryParams.EndTime)
		}
	}
	
	// 查询总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	err = query.Order("create_time DESC").Offset(offset).Limit(pageSize).Find(&ticketPOs).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 转换为实体
	tickets := make([]*entity.ProblemTicket, 0, len(ticketPOs))
	for _, po := range ticketPOs {
		tickets = append(tickets, po.ToEntity())
	}
	
	return tickets, total, nil
}

// GetByID 根据ID获取问题工单
func (r *ProblemTicketRepositoryImpl) GetByID(ctx context.Context, id int64) (*entity.ProblemTicket, error) {
	var ticketPO model.ProblemTicketPO
	
	if err := r.db.First(&ticketPO, id).Error; err != nil {
		return nil, err
	}
	
	return ticketPO.ToEntity(), nil
}

// Save 保存问题工单
func (r *ProblemTicketRepositoryImpl) Save(ctx context.Context, ticket *entity.ProblemTicket) error {
	ticketPO := &model.ProblemTicketPO{
		ManifestID:        ticket.ManifestID,
		TrackingNumber:    ticket.TrackingNumber,
		CustomerAccountID: ticket.CustomerAccountID,
		ProblemTypeCode:   ticket.ProblemTypeCode,
		ProblemDescription: ticket.ProblemDescription,
		Status:           ticket.Status,
		Priority:         ticket.Priority,
		AssignedToUserID: ticket.AssignedToUserID,
		Remarks:          ticket.Remarks,
	}
	
	if err := r.db.Create(ticketPO).Error; err != nil {
		return err
	}
	
	// 设置自增ID
	ticket.ID = ticketPO.ID
	return nil
}

// Update 更新问题工单
func (r *ProblemTicketRepositoryImpl) Update(ctx context.Context, ticket *entity.ProblemTicket) error {
	updateFields := map[string]interface{}{
		"status":            ticket.Status,
		"priority":          ticket.Priority,
		"assigned_to_user_id": ticket.AssignedToUserID,
		"remarks":           ticket.Remarks,
	}
	
	// 如果有解决时间，也更新
	if !ticket.ResolvedTime.IsZero() {
		updateFields["resolved_time"] = ticket.ResolvedTime.Time
	}
	
	return r.db.Model(&model.ProblemTicketPO{}).Where("id = ?", ticket.ID).Updates(updateFields).Error
}

// MarkAllAsResolved 将所有待处理的问题工单标记为已处理
func (r *ProblemTicketRepositoryImpl) MarkAllAsResolved(ctx context.Context, operatorID int64, remark string) (int64, error) {
	nowTime := time.Now()
	
	// 使用事务确保数据一致性
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}
	
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新所有状态为 PENDING 的工单
	result := tx.Model(&model.ProblemTicketPO{}).
		Where("status = ?", "PENDING").
		Updates(map[string]interface{}{
			"status":         "RESOLVED",
			"assigned_to_user_id": operatorID,
			"remarks":        remark,
			"resolved_time":  nowTime,
			"update_time":    nowTime,
		})
	
	if result.Error != nil {
		tx.Rollback()
		return 0, result.Error
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return 0, err
	}
	
	return result.RowsAffected, nil
}

// MarkMultipleAsResolved 将指定ID列表的问题工单标记为已处理
func (r *ProblemTicketRepositoryImpl) MarkMultipleAsResolved(ctx context.Context, ticketIDs []int64, operatorID int64, remark string) (int64, error) {
	// 如果ID列表为空，直接返回0
	if len(ticketIDs) == 0 {
		return 0, nil
	}

	nowTime := time.Now()
	
	// 使用事务确保数据一致性
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}
	
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 只更新状态为 PENDING 的指定ID工单
	result := tx.Model(&model.ProblemTicketPO{}).
		Where("id IN ? AND status = ?", ticketIDs, "PENDING").
		Updates(map[string]interface{}{
			"status":             "RESOLVED",
			"assigned_to_user_id": operatorID,
			"remarks":            remark,
			"resolved_time":      nowTime,
			"update_time":        nowTime,
		})
	
	if result.Error != nil {
		tx.Rollback()
		return 0, result.Error
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return 0, err
	}
	
	return result.RowsAffected, nil
}

// MarkAsPending 将指定ID的问题工单重新标记为待处理
func (r *ProblemTicketRepositoryImpl) MarkAsPending(ctx context.Context, ticketID int64, operatorID int64, remark string) error {
	nowTime := time.Now()

	// 更新工单状态
	result := r.db.WithContext(ctx).Model(&model.ProblemTicketPO{}).
		Where("id = ?", ticketID).
		Updates(map[string]interface{}{
			"status":             "PENDING",
			"assigned_to_user_id": operatorID,
			"remarks":            remark,
			"resolved_time":      nil, // 清除解决时间
			"update_time":        nowTime,
		})
	
	if result.Error != nil {
		return result.Error
	}

	// 如果没有找到记录或者实际没有更新任何记录
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	
	return nil
}

// MarkMultipleAsPending 将指定ID列表的问题工单重新标记为待处理
func (r *ProblemTicketRepositoryImpl) MarkMultipleAsPending(ctx context.Context, ticketIDs []int64, operatorID int64, remark string) (int64, error) {
	// 如果ID列表为空，直接返回0
	if len(ticketIDs) == 0 {
		return 0, nil
	}

	nowTime := time.Now()
	
	// 使用事务确保数据一致性
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return 0, tx.Error
	}
	
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新指定ID工单为待处理状态
	result := tx.Model(&model.ProblemTicketPO{}).
		Where("id IN ?", ticketIDs).
		Updates(map[string]interface{}{
			"status":             "PENDING",
			"assigned_to_user_id": operatorID,
			"remarks":            remark,
			"resolved_time":      nil, // 清除解决时间
			"update_time":        nowTime,
		})
	
	if result.Error != nil {
		tx.Rollback()
		return 0, result.Error
	}
	
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return 0, err
	}
	
	return result.RowsAffected, nil
}

// CountPendingProblemTickets 统计待处理的问题工单数量
func (r *ProblemTicketRepositoryImpl) CountPendingProblemTickets(ctx context.Context) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&model.ProblemTicketPO{}).Where("status = ?", "PENDING")
	
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	
	return count, nil
} 