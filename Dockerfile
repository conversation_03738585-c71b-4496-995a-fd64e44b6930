FROM golang:1.23-alpine AS builder

# 设置 Go 模块代理为国内源
ENV GOPROXY=https://goproxy.cn,direct

# 设置Go编译优化参数
ENV CGO_ENABLED=0 
ENV GOOS=linux
ENV GOARCH=amd64

# 安装必要的工具
RUN apk add --no-cache git

# 设置工作目录
WORKDIR /app

# 拷贝go.mod和go.sum文件
COPY go.mod go.sum ./

# 下载依赖（这一步会被缓存，除非go.mod或go.sum发生变化）
RUN go mod download

# 拷贝源代码
COPY . .

# 接受构建参数来指定要构建的应用
ARG APP_NAME=api-server
ARG BINARY_NAME=zebra-hub-system

# 构建指定的应用，使用编译优化参数
RUN go build -a -installsuffix cgo -ldflags="-w -s" -o ${BINARY_NAME} ./cmd/${APP_NAME}

# 使用更小的基础镜像
FROM alpine:latest

# 安装ca-certificates和tzdata
RUN apk --no-cache add ca-certificates tzdata

# 设置时区为亚洲/上海
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN adduser -D -g '' appuser

# 创建应用目录
RUN mkdir -p /app/configs /app/data /app/assets && chown -R appuser:appuser /app

# 切换到工作目录
WORKDIR /app

# 从构建阶段拷贝可执行文件和配置
ARG BINARY_NAME=zebra-hub-system
COPY --from=builder /app/${BINARY_NAME} .
COPY --from=builder /app/configs/config.yaml ./configs/
COPY --from=builder /app/configs/google-translate-key.json ./configs/
COPY --from=builder /app/data ./data
COPY --from=builder /app/assets ./assets

# 使用非root用户运行应用
USER appuser

# 暴露应用端口（仅对API服务器有意义）
EXPOSE 8080

# 运行应用
CMD ["./zebra-hub-system"] 