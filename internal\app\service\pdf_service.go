package service

import (
	"bytes"
	"fmt"
	"image"
	"image/draw"
	"image/png"
	"strconv"
	"strings"
	"time"

	"zebra-hub-system/internal/domain/entity"

	"github.com/boombuler/barcode"
	"github.com/boombuler/barcode/code128"
	"github.com/jung-kurt/gofpdf/v2"
)

// PDFService 定义PDF生成服务接口
type PDFService interface {
	GenerateLabels(manifests []*entity.Manifest, users map[int64]*entity.User) ([]byte, error)
}

// pdfServiceImpl 实现PDF生成服务
type pdfServiceImpl struct {
	fontPath string
}

// NewPDFService 创建一个新的PDF服务实例
func NewPDFService(fontPath string) PDFService {
	return &pdfServiceImpl{
		fontPath: fontPath,
	}
}

// GenerateLabels 生成PDF面单
func (s *pdfServiceImpl) GenerateLabels(manifests []*entity.Manifest, users map[int64]*entity.User) ([]byte, error) {
	pdf := gofpdf.New("P", "mm", "A4", "")

	// 添加支持中文的字体
	pdf.AddUTF8Font("IPAexGothic", "", s.fontPath)

	for i, manifest := range manifests {
		if i > 0 {
			pdf.AddPage()
		} else {
			// 对于第一页，也需要调用AddPage
			pdf.AddPage()
		}
		s.drawLabel(pdf, manifest, users[manifest.UserID])
	}

	var buf bytes.Buffer
	if err := pdf.Output(&buf); err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

// drawLabel 在PDF上绘制单个面单
func (s *pdfServiceImpl) drawLabel(pdf *gofpdf.Fpdf, manifest *entity.Manifest, user *entity.User) {
	const (
		pageWidth    = 210
		pageHeight   = 297
		topMargin    = 45.0 // 顶部边距
		horizMargin  = 20.0 // 水平边距
		contentWidth = pageWidth - 2*horizMargin
	)

	// 设置字体
	pdf.SetFont("IPAexGothic", "", 12)

	// 1. 顶部横线
	pdf.Line(horizMargin, topMargin, pageWidth-horizMargin, topMargin)

	// 2. 条形码和运单号
	s.drawBarcode(pdf, manifest.ExpressNumber, horizMargin, topMargin+5, contentWidth)

	// 3. 第二条横线
	pdf.Line(horizMargin, topMargin+45, pageWidth-horizMargin, topMargin+45)

	// 4. 收件人等信息
	s.drawAddressInfo(pdf, manifest, horizMargin, topMargin+50)

	// 5. 第三条横线
	pdf.Line(horizMargin, topMargin+125, pageWidth-horizMargin, topMargin+125)

	// 6. 印刷时间和用户名
	s.drawFooterInfo(pdf, user, horizMargin, topMargin+130)

	// 7. 底部横线
	pdf.Line(horizMargin, topMargin+145, pageWidth-horizMargin, topMargin+145)
}

// drawBarcode 绘制条形码
func (s *pdfServiceImpl) drawBarcode(pdf *gofpdf.Fpdf, trackingNumber string, x, y, width float64) {
	// 创建条形码
	bc, err := code128.Encode(trackingNumber)
	if err != nil {
		pdf.SetXY(x, y)
		pdf.Cell(width, 10, "Barcode generation error")
		return
	}

	// 缩放条形码以适应宽度
	scaledBc, err := barcode.Scale(bc, 700, 80) // 增加条码图像的分辨率
	if err != nil {
		pdf.SetXY(x, y)
		pdf.Cell(width, 10, "Barcode scaling error")
		return
	}

	// 将条形码图像转换为RGBA格式以确保兼容性
	bounds := scaledBc.Bounds()
	rgba := image.NewRGBA(bounds)
	draw.Draw(rgba, bounds, scaledBc, bounds.Min, draw.Src)

	// 将条形码保存为图像
	var buf bytes.Buffer
	if err := png.Encode(&buf, rgba); err != nil {
		pdf.SetXY(x, y)
		pdf.Cell(width, 10, "Barcode PNG encoding error")
		return
	}

	// 在PDF中注册并绘制图像
	imgInfo := pdf.RegisterImageReader(trackingNumber, "png", &buf)
	// 让条码图片宽度撑满内容区域，高度自动缩放
	displayedHeight := imgInfo.Height() * width / imgInfo.Width()
	pdf.Image(trackingNumber, x, y, width, 0, false, "", 0, "")

	// 绘制运单号文本
	pdf.SetFont("IPAexGothic", "", 16)   // 增大运单号字体
	textY := y + displayedHeight + 3 // 在条形码下方
	pdf.SetXY(x, textY)
	pdf.CellFormat(width, 10, trackingNumber, "", 0, "C", false, 0, "")
}

// drawAddressInfo 绘制地址信息
func (s *pdfServiceImpl) drawAddressInfo(pdf *gofpdf.Fpdf, manifest *entity.Manifest, x, y float64) {
	pdf.SetFont("IPAexGothic", "", 14) // 增大字体
	lineHeight := 10.0                // 增大行高

	// 姓名
	pdf.SetXY(x, y)
	pdf.Cell(30, lineHeight, "名前:")
	pdf.Cell(100, lineHeight, manifest.ReceiverName)

	// 住所
	y += lineHeight
	pdf.SetXY(x, y)
	pdf.Cell(30, lineHeight, "住所:")
	pdf.MultiCell(140, lineHeight, manifest.ReceiverAddress, "", "L", false)

	// 电话
	y += lineHeight * 2 // 为地址留出两行空间
	pdf.SetXY(x, y)
	pdf.Cell(30, lineHeight, "電話番号:")
	pdf.Cell(100, lineHeight, manifest.ReceiverPhone)

	// 邮编
	y += lineHeight
	pdf.SetXY(x, y)
	pdf.Cell(30, lineHeight, "郵便番号:")
	pdf.Cell(100, lineHeight, manifest.ReceiverZipCode)

	// 品名和数量
	var itemNames []string
	totalQuantity := 0
	for _, item := range manifest.Items {
		itemNames = append(itemNames, item.Name)
		totalQuantity += item.Quantity
	}
	itemNameStr := strings.Join(itemNames, ", ")
	
	y += lineHeight
	pdf.SetXY(x, y)
	pdf.Cell(30, lineHeight, "品名:")
	pdf.Cell(100, lineHeight, itemNameStr)

	y += lineHeight
	pdf.SetXY(x, y)
	pdf.Cell(30, lineHeight, "数量:")
	pdf.Cell(100, lineHeight, strconv.Itoa(totalQuantity))
}

// drawFooterInfo 绘制页脚信息
func (s *pdfServiceImpl) drawFooterInfo(pdf *gofpdf.Fpdf, user *entity.User, x, y float64) {
	pdf.SetFont("IPAexGothic", "", 12) // 增大字体
	
	// 印刷时间
	printTime := time.Now().Format("2006-01-02 15:04:05")
	pdf.SetXY(x, y)
	pdf.Cell(100, 10, fmt.Sprintf("印刷時間: %s", printTime))

	// 用户名
	username := ""
	if user != nil {
		username = user.Username
	}
	pdf.SetXY(150, y) // 靠右
	pdf.CellFormat(30, 10, username, "", 0, "R", false, 0, "")
} 