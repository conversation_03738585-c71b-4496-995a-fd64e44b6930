FROM golang:1.23-alpine AS builder

# 设置 Go 模块代理和构建缓存
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 安装必要工具
RUN apk add --no-cache git ca-certificates tzdata

# 设置工作目录
WORKDIR /app

# 拷贝go.mod和go.sum文件
COPY go.mod go.sum ./

# 使用BuildKit缓存挂载优化依赖下载
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

# 拷贝源代码
COPY . .

# 使用BuildKit缓存挂载进行构建，加入编译优化参数
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go build -v -a -installsuffix cgo \
    -ldflags="-w -s -X main.version=$(date +%Y%m%d%H%M%S)" \
    -o billing-consumer ./cmd/billing-consumer

# 使用更小的基础镜像
FROM alpine:latest

# 安装ca-certificates和tzdata
RUN apk --no-cache add ca-certificates tzdata

# 设置时区为亚洲/上海
ENV TZ=Asia/Shanghai

# 创建非root用户和目录
RUN adduser -D -g '' appuser && \
    mkdir -p /app/configs /app/data /app/assets && \
    chown -R appuser:appuser /app

# 切换到工作目录
WORKDIR /app

# 从构建阶段拷贝可执行文件和配置
COPY --from=builder /app/billing-consumer .
COPY --from=builder /app/configs/ ./configs/
COPY --from=builder /app/data/ ./data/
COPY --from=builder /app/assets/ ./assets/

# 使用非root用户运行应用
USER appuser

# 账单消费者不需要暴露端口（仅消费消息队列）

# 运行账单消费者
CMD ["./billing-consumer"] 