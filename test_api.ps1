# 测试文件上传API的PowerShell脚本

Write-Host "=== 测试文件上传API ===" -ForegroundColor Green

# 测试服务器是否运行
Write-Host "1. 检查服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/upload/info" -Method GET -ErrorAction Stop
    Write-Host "服务器正在运行，但需要认证" -ForegroundColor Red
    Write-Host "响应状态: $($response.StatusCode)" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "服务器正在运行，需要JWT认证 (401 Unauthorized)" -ForegroundColor Yellow
    } elseif ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "路由未找到 (404 Not Found)" -ForegroundColor Red
    } else {
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试登录接口
Write-Host "`n2. 测试登录接口..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "admin"
        password = "123456"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/login" -Method POST -ContentType "application/json" -Body $loginBody -ErrorAction Stop
    Write-Host "登录接口响应状态: $($loginResponse.StatusCode)" -ForegroundColor Green
    
    if ($loginResponse.StatusCode -eq 200) {
        $loginData = $loginResponse.Content | ConvertFrom-Json
        if ($loginData.success) {
            $token = $loginData.data.token
            Write-Host "登录成功，获得Token: $($token.Substring(0, 20))..." -ForegroundColor Green
            
            # 使用Token测试上传配置接口
            Write-Host "`n3. 使用Token测试上传配置接口..." -ForegroundColor Yellow
            $headers = @{
                "Authorization" = "Bearer $token"
            }
            
            $configResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/upload/info" -Method GET -Headers $headers -ErrorAction Stop
            Write-Host "上传配置接口响应状态: $($configResponse.StatusCode)" -ForegroundColor Green
            
            $configData = $configResponse.Content | ConvertFrom-Json
            Write-Host "上传配置信息:" -ForegroundColor Cyan
            Write-Host ($configData | ConvertTo-Json -Depth 3) -ForegroundColor White
        } else {
            Write-Host "登录失败: $($loginData.errorMessage)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "登录测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green 