package handler

import (
	"net/http"
	"time"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// FinancialAdjustmentHandler 财务调整记录处理器
type FinancialAdjustmentHandler struct {
	financialAdjustmentService service.FinancialAdjustmentService
}

// NewFinancialAdjustmentHandler 创建财务调整记录处理器
func NewFinancialAdjustmentHandler(financialAdjustmentService service.FinancialAdjustmentService) *FinancialAdjustmentHandler {
	return &FinancialAdjustmentHandler{
		financialAdjustmentService: financialAdjustmentService,
	}
}

// ListFinancialAdjustments 查询财务调整记录列表
// @Summary 查询财务调整记录列表
// @Description 根据条件查询财务调整记录，支持分页、筛选和排序
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param manifestId query int false "运单ID"
// @Param adjustmentType query string false "调整类型"
// @Param effectiveDateStart query string false "生效日期范围-开始 (格式: 2006-01-02)"
// @Param effectiveDateEnd query string false "生效日期范围-结束 (格式: 2006-01-02)"
// @Param expressNumber query string false "快递单号"
// @Param transferredTrackingNumber query string false "转单号"
// @Param page query int false "页码，默认第1页" default(1)
// @Param pageSize query int false "每页数量，默认10条" default(10)
// @Param orderBy query string false "排序字段 (默认: effectiveDate)" Enums(effectiveDate, createTime, amount)
// @Param isAsc query boolean false "是否升序 (默认: false)" default(false)
// @Success 200 {object} util.Response{data=service.ListFinancialAdjustmentsResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments [get]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) ListFinancialAdjustments(c *gin.Context) {
	var req service.ListFinancialAdjustmentsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 调用服务层（日期解析在服务层处理）
	resp, code, err := h.financialAdjustmentService.ListFinancialAdjustments(c.Request.Context(), &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// VoidFinancialAdjustment 作废财务调整记录
// @Summary 作废财务调整记录
// @Description 将财务调整记录标记为作废状态
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param request body service.VoidFinancialAdjustmentRequest true "作废财务调整记录请求"
// @Success 200 {object} util.Response{data=service.VoidFinancialAdjustmentResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误或记录已作废"
// @Failure 404 {object} util.Response "财务调整记录不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments/void [post]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) VoidFinancialAdjustment(c *gin.Context) {
	var req service.VoidFinancialAdjustmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从上下文中获取当前用户ID
	claimsData, exists := c.Get(util.ClaimsContextKey)
	if !exists {
		util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "未授权的请求", http.StatusUnauthorized)
		return
	}
	req.VoidedBy = claimsData.(*util.Claims).UserID

	resp, code, err := h.financialAdjustmentService.VoidFinancialAdjustment(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		switch code {
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// CreateCompensationAdjustmentRequest 定义创建赔偿类型财务调整记录的请求体
type CreateCompensationAdjustmentRequest struct {
	ManifestID                   int64    `json:"manifestId" binding:"required"`
	IsFreightDeduction           bool     `json:"isFreightDeduction"`
	FreightDeductionPercentage   *float64 `json:"freightDeductionPercentage"`  // Pointer to allow 0 and distinguish from not provided
	TotalFreightDeductionAmount  *float64 `json:"totalFreightDeductionAmount"` // Pointer for same reason
	IsValueCompensation          bool     `json:"isValueCompensation"`
	CargoValue                   *float64 `json:"cargoValue,omitempty"`         // 货值，用于计算赔偿金额
	ValueCompensationPercentage  *float64 `json:"valueCompensationPercentage"`  // Pointer
	TotalValueCompensationAmount *float64 `json:"totalValueCompensationAmount"` // Pointer
	ProofOfValueImageUrls        []string `json:"proofOfValueImageUrls"`
	TotalCompensationAmount      float64  `json:"totalCompensationAmount" binding:"required"` // This will be the main 'amount'
	Description                  string   `json:"description"`
	EffectiveDateStr             string   `json:"effectiveDate" binding:"required"` // Format: YYYY-MM-DD
	Currency                     string   `json:"currency" binding:"required"`
	CustomerAccountID            int64    `json:"customerAccountId" binding:"required"`
}

// CreateCompensationAdjustment 创建赔偿类财务调整记录
// @Summary 创建赔偿类财务调整记录
// @Description 用于记录运费减免或货值赔偿等财务调整
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param request body CreateCompensationAdjustmentRequest true "创建赔偿类财务调整记录请求"
// @Success 201 {object} util.Response{data=service.FinancialAdjustmentItemResponse} "成功创建"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments/compensations [post]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) CreateCompensationAdjustment(c *gin.Context) {
	var req CreateCompensationAdjustmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从上下文中获取当前用户ID
	claimsData, exists := c.Get(util.ClaimsContextKey)
	if !exists {
		util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "未授权的请求", http.StatusUnauthorized)
		return
	}
	creatorID := claimsData.(*util.Claims).UserID

	// 解析生效日期
	effectiveDate, err := time.ParseInLocation("2006-01-02", req.EffectiveDateStr, time.Local)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "生效日期格式错误，请使用 YYYY-MM-DD 格式", http.StatusBadRequest)
		return
	}

	// 构建服务层请求 DTO
	serviceReq := &service.CreateCompensationAdjustmentDTO{
		ManifestID:                   req.ManifestID,
		IsFreightDeduction:           req.IsFreightDeduction,
		FreightDeductionPercentage:   req.FreightDeductionPercentage,
		TotalFreightDeductionAmount:  req.TotalFreightDeductionAmount,
		IsValueCompensation:          req.IsValueCompensation,
		CargoValue:                   req.CargoValue, // 添加货值字段
		ValueCompensationPercentage:  req.ValueCompensationPercentage,
		TotalValueCompensationAmount: req.TotalValueCompensationAmount,
		ProofOfValueImageUrls:        req.ProofOfValueImageUrls,
		TotalCompensationAmount:      req.TotalCompensationAmount,
		Description:                  req.Description,
		EffectiveDate:                effectiveDate,
		Currency:                     req.Currency,
		CustomerAccountID:            req.CustomerAccountID,
		CreatorID:                    creatorID,
	}

	// 调用服务层
	resp, code, err := h.financialAdjustmentService.CreateCompensationAdjustment(c.Request.Context(), serviceReq)
	if err != nil {
		// 默认服务器内部错误
		httpStatus := http.StatusInternalServerError
		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND: // e.g., ManifestID not found
			httpStatus = http.StatusNotFound
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccessWithStatus(c, resp, http.StatusCreated)
}

// CreateReassignmentAdjustmentRequest 定义创建改派类型财务调整记录的请求体
type CreateReassignmentAdjustmentRequest struct {
	ManifestID         int64    `json:"manifestId" binding:"required"`         // 关联的运单ID
	ReassignmentNumber string   `json:"reassignmentNumber" binding:"required"` // 改派单号
	Amount             float64  `json:"amount" binding:"required"`             // 改派费用
	CargoValue         *float64 `json:"cargoValue,omitempty"`                  // 货值，用于计算改派费用
	Description        string   `json:"description"`                           // 改派描述/原因
	EffectiveDateStr   string   `json:"effectiveDate" binding:"required"`      // 生效日期，格式: YYYY-MM-DD
	Currency           string   `json:"currency" binding:"required"`           // 货币代码，如: CNY, USD
	CustomerAccountID  int64    `json:"customerAccountId" binding:"required"`  // 客户账户ID
}

// CreateDestructionAdjustmentRequest 定义创建销毁类型财务调整记录的请求体
type CreateDestructionAdjustmentRequest struct {
	ManifestID        int64   `json:"manifestId" binding:"required"`        // 关联的运单ID
	Amount            float64 `json:"amount" binding:"required"`            // 销毁费用
	Description       string  `json:"description"`                          // 销毁描述/原因
	EffectiveDateStr  string  `json:"effectiveDate" binding:"required"`     // 生效日期，格式: YYYY-MM-DD
	Currency          string  `json:"currency" binding:"required"`          // 货币代码，如: CNY, USD
	CustomerAccountID int64   `json:"customerAccountId" binding:"required"` // 客户账户ID
}

// CreateReturnAdjustmentRequest 定义创建退回类型财务调整记录的请求体
type CreateReturnAdjustmentRequest struct {
	ManifestID        int64   `json:"manifestId" binding:"required"`        // 关联的运单ID
	Amount            float64 `json:"amount" binding:"required"`            // 退回费用
	Description       string  `json:"description"`                          // 退回描述/原因
	EffectiveDateStr  string  `json:"effectiveDate" binding:"required"`     // 生效日期，格式: YYYY-MM-DD
	Currency          string  `json:"currency" binding:"required"`          // 货币代码，如: CNY, USD
	CustomerAccountID int64   `json:"customerAccountId" binding:"required"` // 客户账户ID
}

// BatchCreateAdjustmentItemRequest 批量创建调整项请求
type BatchCreateAdjustmentItemRequest struct {
	AdjustmentType string  `json:"adjustmentType" binding:"required"` // 调整类型：REASSIGNMENT、RETURN、DESTRUCTION等
	Amount         float64 `json:"amount" binding:"required"`         // 该类型的调整金额
}

// BatchCreateFinancialAdjustmentRequest 批量创建财务调整记录请求
type BatchCreateFinancialAdjustmentRequest struct {
	TrackingNumbers   []string                           `json:"trackingNumbers" binding:"required,min=1,max=100"` // 订单号列表，最多100个
	AdjustmentItems   []BatchCreateAdjustmentItemRequest `json:"adjustmentItems" binding:"required,min=1"`         // 调整项列表，包含类型和金额
	EffectiveDateStr  string                             `json:"effectiveDate" binding:"required"`                 // 生效日期，格式: YYYY-MM-DD
	Description       string                             `json:"description"`                                      // 调整描述
	Currency          string                             `json:"currency" binding:"required"`                      // 货币代码
}

// CreateReassignmentAdjustment 创建改派类财务调整记录
// @Summary 创建改派类财务调整记录
// @Description 用于记录改派产生的费用调整
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param request body CreateReassignmentAdjustmentRequest true "创建改派类财务调整记录请求"
// @Success 201 {object} util.Response{data=service.FinancialAdjustmentItemResponse} "成功创建"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments/reassignments [post]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) CreateReassignmentAdjustment(c *gin.Context) {
	// 1. 解析请求参数
	var req CreateReassignmentAdjustmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, err.Error(), http.StatusBadRequest)
		return
	}

	// 2. 获取当前用户ID (今JWT token中)
	claimsData, exists := c.Get("claims")
	if !exists {
		util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "用户未登录", http.StatusUnauthorized)
		return
	}
	creatorID := claimsData.(*util.Claims).UserID

	// 3. 解析生效日期
	effectiveDate, err := time.ParseInLocation("2006-01-02", req.EffectiveDateStr, time.Local)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "生效日期格式错误，请使用 YYYY-MM-DD 格式", http.StatusBadRequest)
		return
	}

	// 4. 构建服务层请求 DTO
	serviceReq := &service.CreateReassignmentAdjustmentDTO{
		ManifestID:         req.ManifestID,
		ReassignmentNumber: req.ReassignmentNumber,
		Amount:             req.Amount,
		CargoValue:         req.CargoValue,
		Description:        req.Description,
		EffectiveDate:      effectiveDate,
		Currency:           req.Currency,
		CustomerAccountID:  req.CustomerAccountID,
		CreatorID:          creatorID,
	}

	// 5. 调用服务层
	resp, code, err := h.financialAdjustmentService.CreateReassignmentAdjustment(c.Request.Context(), serviceReq)
	if err != nil {
		// 默认服务器内部错误
		httpStatus := http.StatusInternalServerError
		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND: // e.g., ManifestID not found
			httpStatus = http.StatusNotFound
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccessWithStatus(c, resp, http.StatusCreated)
}

// CreateDestructionAdjustment 创建销毁类财务调整记录
// @Summary 创建销毁类财务调整记录
// @Description 用于记录销毁产生的费用调整
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param request body CreateDestructionAdjustmentRequest true "创建销毁类财务调整记录请求"
// @Success 201 {object} util.Response{data=service.FinancialAdjustmentItemResponse} "成功创建"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments/destructions [post]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) CreateDestructionAdjustment(c *gin.Context) {
	// 1. 解析请求参数
	var req CreateDestructionAdjustmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, err.Error(), http.StatusBadRequest)
		return
	}

	// 2. 获取当前用户ID (今JWT token中)
	claimsData, exists := c.Get("claims")
	if !exists {
		util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "用户未登录", http.StatusUnauthorized)
		return
	}
	creatorID := claimsData.(*util.Claims).UserID

	// 3. 解析生效日期
	effectiveDate, err := time.ParseInLocation("2006-01-02", req.EffectiveDateStr, time.Local)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "生效日期格式错误，请使用 YYYY-MM-DD 格式", http.StatusBadRequest)
		return
	}

	// 4. 构建服务层请求 DTO
	serviceReq := &service.CreateDestructionAdjustmentDTO{
		ManifestID:        req.ManifestID,
		Amount:            req.Amount,
		Description:       req.Description,
		EffectiveDate:     effectiveDate,
		Currency:          req.Currency,
		CustomerAccountID: req.CustomerAccountID,
		CreatorID:         creatorID,
	}

	// 5. 调用服务层
	resp, code, err := h.financialAdjustmentService.CreateDestructionAdjustment(c.Request.Context(), serviceReq)
	if err != nil {
		// 默认服务器内部错误
		httpStatus := http.StatusInternalServerError
		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND: // e.g., ManifestID not found
			httpStatus = http.StatusNotFound
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccessWithStatus(c, resp, http.StatusCreated)
}

// CreateReturnAdjustment 创建退回类财务调整记录
// @Summary 创建退回类财务调整记录
// @Description 用于记录退回产生的费用调整
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param request body CreateReturnAdjustmentRequest true "创建退回类财务调整记录请求"
// @Success 201 {object} util.Response{data=service.FinancialAdjustmentItemResponse} "成功创建"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments/returns [post]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) CreateReturnAdjustment(c *gin.Context) {
	// 1. 解析请求参数
	var req CreateReturnAdjustmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, err.Error(), http.StatusBadRequest)
		return
	}

	// 2. 获取当前用户ID (今JWT token中)
	claimsData, exists := c.Get("claims")
	if !exists {
		util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "用户未登录", http.StatusUnauthorized)
		return
	}
	creatorID := claimsData.(*util.Claims).UserID

	// 3. 解析生效日期
	effectiveDate, err := time.ParseInLocation("2006-01-02", req.EffectiveDateStr, time.Local)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "生效日期格式错误，请使用 YYYY-MM-DD 格式", http.StatusBadRequest)
		return
	}

	// 4. 构建服务层请求 DTO
	serviceReq := &service.CreateReturnAdjustmentDTO{
		ManifestID:        req.ManifestID,
		Amount:            req.Amount,
		Description:       req.Description,
		EffectiveDate:     effectiveDate,
		Currency:          req.Currency,
		CustomerAccountID: req.CustomerAccountID,
		CreatorID:         creatorID,
	}

	// 5. 调用服务层
	resp, code, err := h.financialAdjustmentService.CreateReturnAdjustment(c.Request.Context(), serviceReq)
	if err != nil {
		// 默认服务器内部错误
		httpStatus := http.StatusInternalServerError
		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND: // e.g., ManifestID not found
			httpStatus = http.StatusNotFound
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// BatchCreateFinancialAdjustments 批量创建财务调整记录
// @Summary 批量创建财务调整记录
// @Description 根据订单号列表和调整类型批量创建财务调整记录，支持改派、退回、销毁三种类型的组合选择
// @Tags FinancialAdjustments
// @Accept json
// @Produce json
// @Param request body BatchCreateFinancialAdjustmentRequest true "批量创建财务调整记录请求"
// @Success 201 {object} util.Response{data=service.BatchCreateFinancialAdjustmentResponse} "成功创建"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /financial-adjustments/batch [post]
// @Security ApiKeyAuth
func (h *FinancialAdjustmentHandler) BatchCreateFinancialAdjustments(c *gin.Context) {
	var req BatchCreateFinancialAdjustmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 验证调整类型
	validTypes := map[string]bool{
		string(valueobject.AdjustmentTypeReassignment): true,
		string(valueobject.AdjustmentTypeReturn):       true,
		string(valueobject.AdjustmentTypeDestruction):  true,
		string(valueobject.AdjustmentTypeCompensation): true,
		string(valueobject.AdjustmentTypeFee):          true,
		string(valueobject.AdjustmentTypeRebate):       true,
		string(valueobject.AdjustmentTypeOther):        true,
	}
	
	for _, adjItem := range req.AdjustmentItems {
		if !validTypes[adjItem.AdjustmentType] {
			util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "无效的调整类型: "+adjItem.AdjustmentType, http.StatusBadRequest)
			return
		}
	}

	// 从上下文中获取当前用户ID
	claimsData, exists := c.Get(util.ClaimsContextKey)
	if !exists {
		util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "未授权的请求", http.StatusUnauthorized)
		return
	}
	creatorID := claimsData.(*util.Claims).UserID

	// 解析生效日期
	effectiveDate, err := time.ParseInLocation("2006-01-02", req.EffectiveDateStr, time.Local)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "生效日期格式错误，请使用 YYYY-MM-DD 格式", http.StatusBadRequest)
		return
	}

	// 转换调整项类型
	adjustmentItems := make([]service.BatchCreateAdjustmentItem, len(req.AdjustmentItems))
	for i, item := range req.AdjustmentItems {
		adjustmentItems[i] = service.BatchCreateAdjustmentItem{
			AdjustmentType: item.AdjustmentType,
			Amount:         item.Amount,
		}
	}

	// 构建服务层请求 DTO
	serviceReq := &service.BatchCreateFinancialAdjustmentDTO{
		TrackingNumbers:   req.TrackingNumbers,
		AdjustmentItems:   adjustmentItems,
		EffectiveDate:     effectiveDate,
		Description:       req.Description,
		Currency:          req.Currency,
		CreatorID:         creatorID,
	}

	// 调用服务层
	resp, code, err := h.financialAdjustmentService.BatchCreateFinancialAdjustments(c.Request.Context(), serviceReq)
	if err != nil {
		// 默认服务器内部错误
		httpStatus := http.StatusInternalServerError
		// 根据业务错误码调整HTTP状态码
		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		}
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}
