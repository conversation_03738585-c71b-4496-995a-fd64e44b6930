# Git相关
.git
.gitignore
.github/

# 文档
*.md
README*
docs/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
tmp/

# 依赖和缓存
node_modules/
vendor/

# 环境变量文件
.env*
!.env.example

# 构建产物
dist/
build/

# Docker相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 脚本（可选，根据需要）
scripts/

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 测试覆盖率报告
coverage.out
*.cover

# 本地数据（如果不需要包含在镜像中）
# data/
# configs/config.local.yaml 