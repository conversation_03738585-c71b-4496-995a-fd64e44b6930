package entity

import "time"

// ShipmentType 货物类型实体
type ShipmentType struct {
	ID         int64     `json:"id"`         // 主键ID
	Code       string    `json:"code"`       // 类型代码 (例如: NORMAL, BATTERY, POSTAL)
	Name       string    `json:"name"`       // 类型名称 (例如: 普通货物, 带电货物, 投函)
	IsActive   bool      `json:"isActive"`   // 是否启用
	CreateTime time.Time `json:"createTime"` // 创建时间
	UpdateTime time.Time `json:"updateTime"` // 更新时间
}

// TableName 指定表名
func (ShipmentType) TableName() string {
	return "shipment_types"
} 