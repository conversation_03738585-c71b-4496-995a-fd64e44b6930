# 分页查询账单记录 API 文档

## 接口概述

该接口用于分页查询账单记录，支持多种过滤条件，如客户 ID、账单状态、时间范围、金额范围等。

## 接口信息

- **请求方法**: GET
- **请求路径**: `/api/v1/finance/billing/records`
- **Content-Type**: `application/json`
- **需要认证**: 是

## 请求参数

### 查询参数 (Query Parameters)

| 参数名             | 类型   | 必填 | 默认值 | 说明                                                                                                       |
| ------------------ | ------ | ---- | ------ | ---------------------------------------------------------------------------------------------------------- |
| page               | int    | 否   | 1      | 页码，最小值为 1                                                                                           |
| pageSize           | int    | 否   | 10     | 每页数量，范围 1-100                                                                                       |
| customerAccountId  | int    | 否   | -      | 客户账户 ID                                                                                                |
| status             | string | 否   | -      | 账单状态：UNPAID（未付款）、PAID（已付款）、PARTIAL_PAID（部分付款）、OVERDUE（逾期）、CANCELLED（已取消） |
| billNumber         | string | 否   | -      | 账单编号（模糊查询）                                                                                       |
| billDateStart      | string | 否   | -      | 账单日期开始，格式：yyyy-MM-dd                                                                             |
| billDateEnd        | string | 否   | -      | 账单日期结束，格式：yyyy-MM-dd                                                                             |
| billingPeriodStart | string | 否   | -      | 账期开始日期，格式：yyyy-MM-dd                                                                             |
| billingPeriodEnd   | string | 否   | -      | 账期结束日期，格式：yyyy-MM-dd                                                                             |
| billPeriodTimeType | string | 否   | -      | 账期时间类型：CREATE_TIME（预报时间）或 SHIPMENT_TIME（发货时间）                                          |
| minAmount          | number | 否   | -      | 最小金额                                                                                                   |
| maxAmount          | number | 否   | -      | 最大金额                                                                                                   |
| currency           | string | 否   | -      | 货币单位，如 CNY、USD 等                                                                                   |

## 响应结果

### 成功响应

**HTTP 状态码**: 200

**响应体结构**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:35:00Z",
  "data": {
    "total": 150,
    "list": [
      {
        "id": 1,
        "billNumber": "BILL-********-143052-123456",
        "customerAccountId": 1001,
        "customerNickname": "张三",
        "billDate": "2024-12-01",
        "dueDate": "2024-12-31",
        "billingPeriodStart": "2024-11-01",
        "billingPeriodEnd": "2024-11-30",
        "billPeriodTimeType": "SHIPMENT_TIME",
        "totalAmount": 1250.5,
        "amountPaid": 0.0,
        "balanceDue": 1250.5,
        "currency": "CNY",
        "status": "UNPAID",
        "paymentMethod": null,
        "paymentTransactionId": null,
        "paymentDate": null,
        "notes": "2024年11月账单",
        "generatedByUserId": 9001,
        "generatedByNickname": "财务专员",
        "createTime": "2024-12-01 14:30:52",
        "updateTime": "2024-12-01 14:30:52"
      }
    ]
  }
}
```

### 错误响应

**HTTP 状态码**: 400/500

**响应体结构**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "查询参数绑定错误: 页码必须大于0",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:30:00Z",
  "data": null
}
```

## 使用示例

### 示例 1: 基础分页查询

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 2: 按客户查询

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records?customerAccountId=1001&page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 3: 按状态和时间范围查询

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records?status=UNPAID&billDateStart=2024-11-01&billDateEnd=2024-11-30&page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 4: 按金额范围查询

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records?minAmount=100&maxAmount=5000&currency=CNY&page=1&pageSize=15" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

### 示例 5: 综合条件查询

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/records?customerAccountId=1001&status=UNPAID&billDateStart=2024-10-01&billDateEnd=2024-12-31&minAmount=500&billPeriodTimeType=SHIPMENT_TIME&page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## 响应字段说明

### BillingRecord 字段说明

| 字段名               | 类型   | 说明                                            |
| -------------------- | ------ | ----------------------------------------------- |
| id                   | int64  | 账单主键 ID                                     |
| billNumber           | string | 账单编号                                        |
| customerAccountId    | int64  | 客户账户 ID                                     |
| customerNickname     | string | 客户昵称                                        |
| billDate             | string | 账单日期（yyyy-MM-dd 格式）                     |
| dueDate              | string | 付款截止日期（yyyy-MM-dd 格式），可为 null      |
| billingPeriodStart   | string | 账期开始日期（yyyy-MM-dd 格式）                 |
| billingPeriodEnd     | string | 账期结束日期（yyyy-MM-dd 格式）                 |
| billPeriodTimeType   | string | 账期时间类型（CREATE_TIME/SHIPMENT_TIME）       |
| totalAmount          | number | 账单总金额                                      |
| amountPaid           | number | 已付金额                                        |
| balanceDue           | number | 应付余额                                        |
| currency             | string | 货币单位                                        |
| status               | string | 账单状态                                        |
| paymentMethod        | string | 支付方式，可为 null                             |
| paymentTransactionId | string | 支付交易号，可为 null                           |
| paymentDate          | string | 支付日期（yyyy-MM-dd HH:mm:ss 格式），可为 null |
| notes                | string | 账单备注，可为 null                             |
| generatedByUserId    | int64  | 账单生成操作员 ID，可为 null                    |
| generatedByNickname  | string | 账单生成操作员昵称，可为 null                   |
| createTime           | string | 记录创建时间（yyyy-MM-dd HH:mm:ss 格式）        |
| updateTime           | string | 记录更新时间（yyyy-MM-dd HH:mm:ss 格式）        |

## 账单状态说明

| 状态值       | 说明     |
| ------------ | -------- |
| UNPAID       | 未付款   |
| PAID         | 已付款   |
| PARTIAL_PAID | 部分付款 |
| OVERDUE      | 逾期     |
| CANCELLED    | 已取消   |

## 错误码说明

| 错误码 | 说明              |
| ------ | ----------------- |
| 100000 | 操作成功          |
| 100002 | 无效参数          |
| 100003 | 缺少必要参数      |
| 100004 | 未授权/未登录     |
| 100006 | 请求的资源未找到  |
| 100001 | 未知错误/系统繁忙 |

## 注意事项

1. **分页参数**: page 最小值为 1，pageSize 范围为 1-100
2. **日期格式**: 所有日期参数必须使用`yyyy-MM-dd`格式
3. **时间范围**: 结束时间必须晚于开始时间
4. **金额范围**: maxAmount 必须大于等于 minAmount
5. **认证**: 需要在请求头中包含有效的 Bearer Token
6. **性能**: 建议合理设置 pageSize，避免一次性查询过多数据
7. **排序**: 结果按创建时间倒序排列（最新创建的在前）

## 业务逻辑说明

1. **权限控制**: 根据当前登录用户的权限，可能只能查看特定客户的账单记录
2. **数据关联**: 系统会自动查询并填充客户昵称和操作员昵称
3. **状态计算**: balanceDue = totalAmount - amountPaid（自动计算）
4. **时区处理**: 所有时间均按服务器时区处理

## 版本历史

| 版本 | 日期       | 变更说明                               |
| ---- | ---------- | -------------------------------------- |
| v1.0 | 2024-12-01 | 初始版本，支持基础分页查询和多条件过滤 |
