package service

import (
	"context"
	"errors"
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// CreateCompensationAdjustmentDTO DTO for creating compensation adjustment
type CreateCompensationAdjustmentDTO struct {
	ManifestID                   int64
	IsFreightDeduction           bool
	FreightDeductionPercentage   *float64
	TotalFreightDeductionAmount  *float64
	IsValueCompensation          bool
	CargoValue                   *float64 // 货值，用于计算赔偿金额
	ValueCompensationPercentage  *float64
	TotalValueCompensationAmount *float64
	ProofOfValueImageUrls        []string
	TotalCompensationAmount      float64
	Description                  string
	EffectiveDate                time.Time
	Currency                     string
	CustomerAccountID            int64
	CreatorID                    int64 // User ID of the creator
}

// FinancialAdjustmentItemResponse DTO for single financial adjustment item response
type FinancialAdjustmentItemResponse struct {
	ID                int64                  `json:"id"`
	ManifestID        int64                  `json:"manifestId"`
	TrackingNumber    *string                `json:"trackingNumber,omitempty"`    // 运单号
	TransferredNumber *string                `json:"transferredNumber,omitempty"` // 转单号
	AdjustmentType    string                 `json:"adjustmentType"`
	Description       string                 `json:"description"`
	AdditionalDetails map[string]interface{} `json:"additionalDetails,omitempty"`
	Amount            float64                `json:"amount"`
	Currency          string                 `json:"currency"`
	EffectiveDate     time.Time              `json:"effectiveDate"`
	CustomerAccountID int64                  `json:"customerAccountId"`
	CreatorID         *int64                 `json:"creatorId,omitempty"`
	CreateTime        time.Time              `json:"createTime"`
	UpdateTime        time.Time              `json:"updateTime"`
}

// VoidFinancialAdjustmentRequest 作废财务调整记录请求
type VoidFinancialAdjustmentRequest struct {
	ID         int64  `json:"id" binding:"required"`         // 财务调整记录ID
	VoidReason string `json:"voidReason" binding:"required"` // 作废原因
	VoidedBy   int64  `json:"voidedBy"`                      // 作废操作员
}

// VoidFinancialAdjustmentResponse 作废财务调整记录响应
type VoidFinancialAdjustmentResponse struct {
	ID         int64  `json:"id"`         // 财务调整记录ID
	IsVoid     bool   `json:"isVoid"`     // 是否已作废
	VoidReason string `json:"voidReason"` // 作废原因
}

// ListFinancialAdjustmentsRequest 查询财务调整记录请求
type ListFinancialAdjustmentsRequest struct {
	ManifestID         *int64  `form:"manifestId"`                  // 运单ID
	AdjustmentType     *string `form:"adjustmentType"`              // 调整类型
	EffectiveDateStart *string `form:"effectiveDateStart"`          // 生效日期范围-开始 (格式: YYYY-MM-DD)
	EffectiveDateEnd   *string `form:"effectiveDateEnd"`            // 生效日期范围-结束 (格式: YYYY-MM-DD)
	CustomerAccountID  *int64  `form:"customerAccountId"`           // 客户账户ID
	TrackingNumber     *string `form:"trackingNumber"`              // 快递单号/转单号（模糊查询，匹配express_number或transferred_tracking_number）
	OrderBy            string  `form:"orderBy,default=create_time"` // 排序字段，默认按创建时间
	IsAsc              bool    `form:"isAsc,default=false"`         // 是否升序，默认降序
	Page               int     `form:"page,default=1"`              // 页码，默认第1页
	PageSize           int     `form:"pageSize,default=10"`         // 每页数量，默认10条
}

// FinancialAdjustmentItem 财务调整记录项
type FinancialAdjustmentItem struct {
	ID                int64                  `json:"id"`
	ManifestID        int64                  `json:"manifestId"`
	TrackingNumber    *string                `json:"trackingNumber,omitempty"`    // 运单号
	TransferredNumber *string                `json:"transferredNumber,omitempty"` // 转单号
	AdjustmentType    string                 `json:"adjustmentType"`
	Description       string                 `json:"description"`
	AdditionalDetails map[string]interface{} `json:"additionalDetails,omitempty"`
	Amount            float64                `json:"amount"`
	Currency          string                 `json:"currency"`
	EffectiveDate     time.Time              `json:"effectiveDate"`
	CustomerAccountID int64                  `json:"customerAccountId"`
	CustomerNickname  *string                `json:"customerNickname,omitempty"`
	IsVoid            bool                   `json:"isVoid"`
	VoidReason        *string                `json:"voidReason,omitempty"`
	VoidedBy          *int64                 `json:"voidedBy,omitempty"`
	VoidedTime        *time.Time             `json:"voidedTime,omitempty"`
	CreatorID         *int64                 `json:"creatorId,omitempty"`
	CreatorNickname   *string                `json:"creatorNickname,omitempty"` // 创建人昵称
	CreateTime        time.Time              `json:"createTime"`
	UpdateTime        time.Time              `json:"updateTime"`
}

// ListFinancialAdjustmentsResponse 查询财务调整记录响应
type ListFinancialAdjustmentsResponse struct {
	Items    []*FinancialAdjustmentItem `json:"items"`    // 当前页数据
	Total    int64                      `json:"total"`    // 总记录数
	Page     int                        `json:"page"`     // 当前页码
	PageSize int                        `json:"pageSize"` // 每页数量
}

// CreateReassignmentAdjustmentDTO DTO for creating reassignment adjustment
type CreateReassignmentAdjustmentDTO struct {
	ManifestID         int64
	ReassignmentNumber string
	Amount             float64
	CargoValue         *float64 // 货值，用于计算改派费用
	Description        string
	EffectiveDate      time.Time
	Currency           string
	CustomerAccountID  int64
	CreatorID          int64 // User ID of the creator
}

// CreateDestructionAdjustmentDTO DTO for creating destruction adjustment
type CreateDestructionAdjustmentDTO struct {
	ManifestID        int64
	Amount            float64
	Description       string
	EffectiveDate     time.Time
	Currency          string
	CustomerAccountID int64
	CreatorID         int64 // User ID of the creator
}

// CreateReturnAdjustmentDTO DTO for creating return adjustment
type CreateReturnAdjustmentDTO struct {
	ManifestID        int64
	Amount            float64
	Description       string
	EffectiveDate     time.Time
	Currency          string
	CustomerAccountID int64
	CreatorID         int64 // User ID of the creator
}

// BatchCreateAdjustmentItem 批量创建调整项
type BatchCreateAdjustmentItem struct {
	AdjustmentType string  `json:"adjustmentType" binding:"required"` // 调整类型：REASSIGNMENT、RETURN、DESTRUCTION等
	Amount         float64 `json:"amount" binding:"required"`         // 该类型的调整金额
}

// BatchCreateFinancialAdjustmentDTO 批量创建财务调整记录DTO
type BatchCreateFinancialAdjustmentDTO struct {
	TrackingNumbers   []string                    `json:"trackingNumbers" binding:"required,min=1,max=100"` // 订单号列表，最多100个
	AdjustmentItems   []BatchCreateAdjustmentItem `json:"adjustmentItems" binding:"required,min=1"`         // 调整项列表，包含类型和金额
	EffectiveDate     time.Time                   `json:"effectiveDate" binding:"required"`                 // 生效日期
	Description       string                      `json:"description"`                                      // 调整描述
	Currency          string                      `json:"currency" binding:"required"`                      // 货币代码
	CreatorID         int64                       `json:"creatorId"`                                        // 创建者ID
}

// BatchCreateFinancialAdjustmentResponse 批量创建财务调整记录响应
type BatchCreateFinancialAdjustmentResponse struct {
	SuccessCount      int                                      `json:"successCount"`      // 成功创建的记录数
	FailureCount      int                                      `json:"failureCount"`      // 失败的记录数
	TotalCount        int                                      `json:"totalCount"`        // 总记录数
	SuccessItems      []*FinancialAdjustmentItemResponse       `json:"successItems"`      // 成功创建的记录列表
	FailureItems      []*BatchCreateFailureItem                `json:"failureItems"`      // 失败的记录列表
	Summary           *BatchCreateSummary                      `json:"summary"`           // 汇总信息
}

// BatchCreateFailureItem 批量创建失败项
type BatchCreateFailureItem struct {
	TrackingNumber  string `json:"trackingNumber"`  // 订单号
	AdjustmentType  string `json:"adjustmentType"`  // 调整类型
	ErrorCode       int    `json:"errorCode"`       // 错误码
	ErrorMessage    string `json:"errorMessage"`    // 错误消息
}

// BatchCreateSummary 批量创建汇总信息
type BatchCreateSummary struct {
	TotalAmount       float64 `json:"totalAmount"`       // 总调整金额
	ReassignmentCount int     `json:"reassignmentCount"` // 改派记录数
	ReturnCount       int     `json:"returnCount"`       // 退回记录数
	DestructionCount  int     `json:"destructionCount"`  // 销毁记录数
}

// FinancialAdjustmentService 财务调整记录服务接口
type FinancialAdjustmentService interface {
	// ListFinancialAdjustments 查询财务调整记录列表
	ListFinancialAdjustments(ctx context.Context, req *ListFinancialAdjustmentsRequest) (*ListFinancialAdjustmentsResponse, int, error)

	// VoidFinancialAdjustment 作废财务调整记录
	VoidFinancialAdjustment(ctx context.Context, req *VoidFinancialAdjustmentRequest) (*VoidFinancialAdjustmentResponse, int, error)

	// CreateCompensationAdjustment 创建赔偿类财务调整记录
	CreateCompensationAdjustment(ctx context.Context, req *CreateCompensationAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error)

	// CreateReassignmentAdjustment 创建改派类财务调整记录
	CreateReassignmentAdjustment(ctx context.Context, req *CreateReassignmentAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error)

	// CreateDestructionAdjustment 创建销毁类财务调整记录
	CreateDestructionAdjustment(ctx context.Context, req *CreateDestructionAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error)

	// CreateReturnAdjustment 创建退回类财务调整记录
	CreateReturnAdjustment(ctx context.Context, req *CreateReturnAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error)

	// BatchCreateFinancialAdjustments 批量创建财务调整记录
	BatchCreateFinancialAdjustments(ctx context.Context, req *BatchCreateFinancialAdjustmentDTO) (*BatchCreateFinancialAdjustmentResponse, int, error)
}

// FinancialAdjustmentServiceImpl 财务调整记录服务实现
type FinancialAdjustmentServiceImpl struct {
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository
	manifestRepo                    repository.ManifestRepository
	userRepo                        repository.UserRepository
}

// NewFinancialAdjustmentService 创建财务调整记录服务
func NewFinancialAdjustmentService(
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository,
	manifestRepo repository.ManifestRepository,
	userRepo repository.UserRepository,
) FinancialAdjustmentService {
	return &FinancialAdjustmentServiceImpl{
		manifestFinancialAdjustmentRepo: manifestFinancialAdjustmentRepo,
		manifestRepo:                    manifestRepo,
		userRepo:                        userRepo,
	}
}

// CreateDestructionAdjustment 创建销毁类财务调整记录
func (s *FinancialAdjustmentServiceImpl) CreateDestructionAdjustment(ctx context.Context, req *CreateDestructionAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error) {
	// 1. 验证运单是否存在
	_, err := s.manifestRepo.GetManifestByID(ctx, req.ManifestID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("运单不存在")
		}
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 创建 entity.ManifestFinancialAdjustment
	adjustment := &entity.ManifestFinancialAdjustment{
		ManifestID:        req.ManifestID,
		AdjustmentType:    string(valueobject.AdjustmentTypeDestruction), // 使用常量定义销毁类型
		Description:       req.Description,
		Amount:            req.Amount,
		Currency:          req.Currency,
		EffectiveDate:     req.EffectiveDate,
		CustomerAccountID: req.CustomerAccountID,
		IsVoid:            false,
		CreatorID:         &req.CreatorID,
	}

	// 3. 保存到数据库
	if err := s.manifestFinancialAdjustmentRepo.Save(ctx, adjustment); err != nil {
		// 记录错误日志
		logger := util.GetLoggerFromContext(ctx)
		if logger != nil {
			logger.Error("Failed to create destruction adjustment",
				zap.Error(err),
				zap.Int64("manifestId", req.ManifestID))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("创建销毁调整记录失败")
	}

	// 4. 返回响应
	resp := &FinancialAdjustmentItemResponse{
		ID:                adjustment.ID,
		ManifestID:        adjustment.ManifestID,
		AdjustmentType:    adjustment.AdjustmentType,
		Description:       adjustment.Description,
		Amount:            adjustment.Amount,
		Currency:          adjustment.Currency,
		EffectiveDate:     adjustment.EffectiveDate,
		CustomerAccountID: adjustment.CustomerAccountID,
		CreatorID:         adjustment.CreatorID,
		CreateTime:        adjustment.CreateTime,
		UpdateTime:        adjustment.UpdateTime,
	}

	return resp, valueobject.SUCCESS, nil
}

// CreateCompensationAdjustment 创建赔偿类财务调整记录
func (s *FinancialAdjustmentServiceImpl) CreateCompensationAdjustment(ctx context.Context, req *CreateCompensationAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error) {
	// 1. 验证运单是否存在
	_, err := s.manifestRepo.GetManifestByID(ctx, req.ManifestID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("运单不存在")
		}
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 准备附加详情数据 (JSON 格式)
	additionalDetailsMap := map[string]interface{}{}

	// 赔偿方式一：运费扣除
	if req.IsFreightDeduction {
		additionalDetailsMap["is_freight_deduction"] = true
		if req.FreightDeductionPercentage != nil {
			additionalDetailsMap["freight_deduction_percentage"] = *req.FreightDeductionPercentage
		}
		if req.TotalFreightDeductionAmount != nil {
			additionalDetailsMap["total_freight_deduction_amount"] = *req.TotalFreightDeductionAmount
		}
	}

	// 赔偿方式二：货值赔偿
	if req.IsValueCompensation {
		additionalDetailsMap["is_value_compensation"] = true
		if req.ValueCompensationPercentage != nil {
			additionalDetailsMap["value_compensation_percentage"] = *req.ValueCompensationPercentage
		}
		if req.TotalValueCompensationAmount != nil {
			additionalDetailsMap["total_value_compensation_amount"] = *req.TotalValueCompensationAmount
		}
	}

	if len(req.ProofOfValueImageUrls) > 0 {
		additionalDetailsMap["proof_of_value_image_urls"] = req.ProofOfValueImageUrls
	}

	if req.CargoValue != nil {
		additionalDetailsMap["cargo_value"] = *req.CargoValue
	}

	// 3. 创建 entity.ManifestFinancialAdjustment
	additionalDetails := entity.AdditionalDetails(additionalDetailsMap)

	adjustment := &entity.ManifestFinancialAdjustment{
		ManifestID:        req.ManifestID,
		AdjustmentType:    string(valueobject.AdjustmentTypeCompensation), // 使用常量定义赔偿类型
		Description:       req.Description,
		AdditionalDetails: &additionalDetails,
		Amount:            req.TotalCompensationAmount, // 赔偿金额一般为负数
		Currency:          req.Currency,
		EffectiveDate:     req.EffectiveDate,
		CustomerAccountID: req.CustomerAccountID,
		IsVoid:            false,
		CreatorID:         &req.CreatorID,
	}

	// 4. 保存到数据库
	if err := s.manifestFinancialAdjustmentRepo.Save(ctx, adjustment); err != nil {
		// 记录错误日志
		logger := util.GetLoggerFromContext(ctx)
		if logger != nil {
			logger.Error("Failed to create compensation adjustment",
				zap.Error(err),
				zap.Int64("manifestId", req.ManifestID))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("创建赔偿调整记录失败")
	}

	// 5. 获取运单信息
	var trackingNumber, transferredNumber *string
	manifest, err := s.manifestRepo.GetManifestByID(ctx, req.ManifestID)
	if err == nil && manifest != nil {
		if manifest.ExpressNumber != "" {
			expressNumber := manifest.ExpressNumber
			trackingNumber = &expressNumber
		}
		if manifest.TransferredTrackingNumber != "" {
			transferredTrackingNumberValue := manifest.TransferredTrackingNumber
			transferredNumber = &transferredTrackingNumberValue
		}
	}

	// 6. 返回响应
	resp := &FinancialAdjustmentItemResponse{
		ID:                adjustment.ID,
		ManifestID:        adjustment.ManifestID,
		TrackingNumber:    trackingNumber,
		TransferredNumber: transferredNumber,
		AdjustmentType:    adjustment.AdjustmentType,
		Description:       adjustment.Description,
		AdditionalDetails: additionalDetailsMap,
		Amount:            adjustment.Amount,
		Currency:          adjustment.Currency,
		EffectiveDate:     adjustment.EffectiveDate,
		CustomerAccountID: adjustment.CustomerAccountID,
		CreatorID:         adjustment.CreatorID,
		CreateTime:        adjustment.CreateTime,
		UpdateTime:        adjustment.UpdateTime,
	}

	return resp, valueobject.SUCCESS, nil
}

// CreateReassignmentAdjustment 创建改派类财务调整记录
func (s *FinancialAdjustmentServiceImpl) CreateReassignmentAdjustment(ctx context.Context, req *CreateReassignmentAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error) {
	// 1. 验证运单是否存在
	_, err := s.manifestRepo.GetManifestByID(ctx, req.ManifestID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("运单不存在")
		}
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 准备附加详情数据
	additionalDetails := map[string]interface{}{
		"reassignmentNumber": req.ReassignmentNumber,
	}

	// 如果提供了货值，将其添加到附加详情中
	if req.CargoValue != nil {
		additionalDetails["cargoValue"] = *req.CargoValue
	}

	// 3. 创建 entity.ManifestFinancialAdjustment
	adjustmentDetails := entity.AdditionalDetails(additionalDetails)

	adjustment := &entity.ManifestFinancialAdjustment{
		ManifestID:        req.ManifestID,
		AdjustmentType:    string(valueobject.AdjustmentTypeReassignment), // 使用常量定义改派类型
		Description:       req.Description,
		AdditionalDetails: &adjustmentDetails,
		Amount:            req.Amount,
		Currency:          req.Currency,
		EffectiveDate:     req.EffectiveDate,
		CustomerAccountID: req.CustomerAccountID,
		IsVoid:            false,
		CreatorID:         &req.CreatorID,
	}

	// 4. 保存到数据库
	if err := s.manifestFinancialAdjustmentRepo.Save(ctx, adjustment); err != nil {
		// 记录错误日志
		logger := util.GetLoggerFromContext(ctx)
		if logger != nil {
			logger.Error("Failed to create reassignment adjustment",
				zap.Error(err),
				zap.Int64("manifestId", req.ManifestID))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("创建改派调整记录失败")
	}

	// 5. 获取运单信息
	var trackingNumber, transferredNumber *string
	manifest, err := s.manifestRepo.GetManifestByID(ctx, req.ManifestID)
	if err == nil && manifest != nil {
		if manifest.ExpressNumber != "" {
			expressNumber := manifest.ExpressNumber
			trackingNumber = &expressNumber
		}
		if manifest.TransferredTrackingNumber != "" {
			transferredTrackingNumberValue := manifest.TransferredTrackingNumber
			transferredNumber = &transferredTrackingNumberValue
		}
	}

	// 6. 返回响应
	resp := &FinancialAdjustmentItemResponse{
		ID:                adjustment.ID,
		ManifestID:        adjustment.ManifestID,
		TrackingNumber:    trackingNumber,
		TransferredNumber: transferredNumber,
		AdjustmentType:    adjustment.AdjustmentType,
		Description:       adjustment.Description,
		AdditionalDetails: additionalDetails,
		Amount:            adjustment.Amount,
		Currency:          adjustment.Currency,
		EffectiveDate:     adjustment.EffectiveDate,
		CustomerAccountID: adjustment.CustomerAccountID,
		CreatorID:         adjustment.CreatorID,
		CreateTime:        adjustment.CreateTime,
		UpdateTime:        adjustment.UpdateTime,
	}

	return resp, valueobject.SUCCESS, nil
}

// ListFinancialAdjustments 查询财务调整记录列表
func (s *FinancialAdjustmentServiceImpl) ListFinancialAdjustments(ctx context.Context, req *ListFinancialAdjustmentsRequest) (*ListFinancialAdjustmentsResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 解析日期字符串为时间对象
	var effectiveDateStart, effectiveDateEnd *time.Time

	if req.EffectiveDateStart != nil && *req.EffectiveDateStart != "" {
		parsedStart, err := time.ParseInLocation("2006-01-02", *req.EffectiveDateStart, time.Local)
		if err != nil {
			if logger != nil {
				logger.Warn("Invalid effectiveDateStart format", zap.String("date", *req.EffectiveDateStart), zap.Error(err))
			}
			return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("开始日期格式错误，请使用 YYYY-MM-DD 格式")
		}
		effectiveDateStart = &parsedStart
	}

	if req.EffectiveDateEnd != nil && *req.EffectiveDateEnd != "" {
		parsedEnd, err := time.ParseInLocation("2006-01-02", *req.EffectiveDateEnd, time.Local)
		if err != nil {
			if logger != nil {
				logger.Warn("Invalid effectiveDateEnd format", zap.String("date", *req.EffectiveDateEnd), zap.Error(err))
			}
			return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("结束日期格式错误，请使用 YYYY-MM-DD 格式")
		}
		effectiveDateEnd = &parsedEnd
	}

	// 2. 查询总数
	total, err := s.manifestFinancialAdjustmentRepo.CountByFilters(
		ctx,
		req.ManifestID,
		req.AdjustmentType,
		effectiveDateStart,
		effectiveDateEnd,
		req.CustomerAccountID,
		req.TrackingNumber,
	)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to count financial adjustments",
				zap.Error(err),
				zap.Any("criteria", req))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询财务调整记录失败")
	}

	// 3. 查询分页数据
	adjustments, err := s.manifestFinancialAdjustmentRepo.FindByFilters(
		ctx,
		req.ManifestID,
		req.AdjustmentType,
		effectiveDateStart,
		effectiveDateEnd,
		req.CustomerAccountID,
		req.TrackingNumber,
		req.OrderBy,
		req.IsAsc,
		req.Page,
		req.PageSize,
	)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to list financial adjustments",
				zap.Error(err),
				zap.Any("criteria", req))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询财务调整记录失败")
	}

	// 3. 构建基础响应结构
	result := &ListFinancialAdjustmentsResponse{
		Items:    make([]*FinancialAdjustmentItem, 0, len(adjustments)),
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	// 如果没有数据，直接返回
	if len(adjustments) == 0 {
		return result, valueobject.SUCCESS, nil
	}

	// 4. 收集需要查询的ID集合
	customerIDs := make([]int64, 0)
	manifestIDs := make([]int64, 0)
	creatorIDs := make([]int64, 0)
	customerIDSet := make(map[int64]bool) // 用于去重
	manifestIDSet := make(map[int64]bool) // 用于去重
	creatorIDSet := make(map[int64]bool)  // 用于去重

	for _, adj := range adjustments {
		// 收集客户ID
		if adj.CustomerAccountID > 0 && !customerIDSet[adj.CustomerAccountID] {
			customerIDs = append(customerIDs, adj.CustomerAccountID)
			customerIDSet[adj.CustomerAccountID] = true
		}
		// 收集运单ID
		if adj.ManifestID > 0 && !manifestIDSet[adj.ManifestID] {
			manifestIDs = append(manifestIDs, adj.ManifestID)
			manifestIDSet[adj.ManifestID] = true
		}
		// 收集创建人ID
		if adj.CreatorID != nil && *adj.CreatorID > 0 && !creatorIDSet[*adj.CreatorID] {
			creatorIDs = append(creatorIDs, *adj.CreatorID)
			creatorIDSet[*adj.CreatorID] = true
		}
	}

	// 5. 批量查询客户信息
	customerMap := make(map[int64]string)
	if len(customerIDs) > 0 {
		customers, err := s.userRepo.FindByIDs(ctx, customerIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to get customers by IDs",
					zap.Error(err),
					zap.Int64s("customerIDs", customerIDs))
			}
			// 不因为获取客户信息失败而中断请求
		} else {
			for _, customer := range customers {
				customerMap[customer.ID] = customer.Nickname
			}
		}
	}

	// 批量查询创建人信息
	creatorMap := make(map[int64]string)
	if len(creatorIDs) > 0 {
		creators, err := s.userRepo.FindByIDs(ctx, creatorIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to get creators by IDs",
					zap.Error(err),
					zap.Int64s("creatorIDs", creatorIDs))
			}
			// 不因为获取创建人信息失败而中断请求
		} else {
			for _, creator := range creators {
				creatorMap[creator.ID] = creator.Nickname
			}
		}
	}

	// 6. 批量查询运单信息
	manifestMap := make(map[int64]*entity.Manifest)
	if len(manifestIDs) > 0 {
		// 使用批量查询方法来避免N+1查询问题
		manifests, err := s.manifestRepo.GetManifestsByIDs(ctx, manifestIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to get manifests by IDs",
					zap.Error(err),
					zap.Int64s("manifestIDs", manifestIDs))
			}
			// 不因为获取运单信息失败而中断请求
		} else {
			for _, manifest := range manifests {
				manifestMap[manifest.ID] = manifest
			}
		}
	}

	// 7. 构建返回列表
	for _, adj := range adjustments {
		item := &FinancialAdjustmentItem{
			ID:                adj.ID,
			ManifestID:        adj.ManifestID,
			AdjustmentType:    adj.AdjustmentType,
			Description:       adj.Description,
			Amount:            adj.Amount,
			Currency:          adj.Currency,
			EffectiveDate:     adj.EffectiveDate,
			CustomerAccountID: adj.CustomerAccountID,
			IsVoid:            adj.IsVoid,
			CreatorID:         adj.CreatorID,
			CreateTime:        adj.CreateTime,
			UpdateTime:        adj.UpdateTime,
		}

		// 填充运单号和转单号
		if manifest, exists := manifestMap[adj.ManifestID]; exists {
			if manifest.ExpressNumber != "" {
				expressNumber := manifest.ExpressNumber
				item.TrackingNumber = &expressNumber
			}
			if manifest.TransferredTrackingNumber != "" {
				transferredNumber := manifest.TransferredTrackingNumber
				item.TransferredNumber = &transferredNumber
			}
		}

		// 填充客户昵称
		if nickname, exists := customerMap[adj.CustomerAccountID]; exists {
			item.CustomerNickname = &nickname
		}

		// 填充创建人昵称
		if adj.CreatorID != nil {
			if creatorNickname, exists := creatorMap[*adj.CreatorID]; exists {
				item.CreatorNickname = &creatorNickname
			}
		}

		// 处理可选字段
		if adj.VoidReason != "" {
			item.VoidReason = &adj.VoidReason
		}
		if adj.VoidedBy != nil {
			item.VoidedBy = adj.VoidedBy
		}
		if adj.VoidedTime != nil {
			item.VoidedTime = adj.VoidedTime
		}

		// 处理附加详情JSON
		if adj.AdditionalDetails != nil {
			additionalDetails := make(map[string]interface{})
			for k, v := range *adj.AdditionalDetails {
				additionalDetails[k] = v
			}
			item.AdditionalDetails = additionalDetails
		}

		result.Items = append(result.Items, item)
	}

	if logger != nil {
		logger.Info("Successfully listed financial adjustments",
			zap.Int64("total", total),
			zap.Int("returnedCount", len(result.Items)),
			zap.Int("page", req.Page),
			zap.Int("pageSize", req.PageSize))
	}

	return result, valueobject.SUCCESS, nil
}

// VoidFinancialAdjustment 作废财务调整记录
func (s *FinancialAdjustmentServiceImpl) VoidFinancialAdjustment(ctx context.Context, req *VoidFinancialAdjustmentRequest) (*VoidFinancialAdjustmentResponse, int, error) {
	// 1. 查询当前调整记录
	adjustment, err := s.manifestFinancialAdjustmentRepo.FindByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("财务调整记录不存在")
		}
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 检查是否已作废
	if adjustment.IsVoid {
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("该财务调整记录已作废")
	}

	// 3. 更新作废状态
	now := time.Now()
	adjustment.IsVoid = true
	adjustment.VoidReason = req.VoidReason
	adjustment.VoidedBy = &req.VoidedBy
	adjustment.VoidedTime = &now

	// 4. 保存到数据库
	if err := s.manifestFinancialAdjustmentRepo.Update(ctx, adjustment); err != nil {
		// 记录错误日志
		logger := util.GetLoggerFromContext(ctx)
		if logger != nil {
			logger.Error("Failed to void financial adjustment",
				zap.Error(err),
				zap.Int64("adjustmentId", req.ID))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("作废财务调整记录失败")
	}

	// 5. 返回响应
	resp := &VoidFinancialAdjustmentResponse{
		ID:         adjustment.ID,
		IsVoid:     true,
		VoidReason: adjustment.VoidReason,
	}

	return resp, valueobject.SUCCESS, nil
}

// CreateReturnAdjustment 创建退回类财务调整记录
func (s *FinancialAdjustmentServiceImpl) CreateReturnAdjustment(ctx context.Context, req *CreateReturnAdjustmentDTO) (*FinancialAdjustmentItemResponse, int, error) {
	// 1. 验证运单是否存在
	_, err := s.manifestRepo.GetManifestByID(ctx, req.ManifestID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("运单不存在")
		}
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 创建 entity.ManifestFinancialAdjustment
	adjustment := &entity.ManifestFinancialAdjustment{
		ManifestID:        req.ManifestID,
		AdjustmentType:    string(valueobject.AdjustmentTypeReturn), // 使用常量定义退回类型
		Description:       req.Description,
		Amount:            req.Amount,
		Currency:          req.Currency,
		EffectiveDate:     req.EffectiveDate,
		CustomerAccountID: req.CustomerAccountID,
		IsVoid:            false,
		CreatorID:         &req.CreatorID,
	}

	// 3. 保存到数据库
	if err := s.manifestFinancialAdjustmentRepo.Save(ctx, adjustment); err != nil {
		// 记录错误日志
		logger := util.GetLoggerFromContext(ctx)
		if logger != nil {
			logger.Error("Failed to create return adjustment",
				zap.Error(err),
				zap.Int64("manifestId", req.ManifestID))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("创建退回调整记录失败")
	}

	// 4. 返回响应
	resp := &FinancialAdjustmentItemResponse{
		ID:                adjustment.ID,
		ManifestID:        adjustment.ManifestID,
		AdjustmentType:    adjustment.AdjustmentType,
		Description:       adjustment.Description,
		Amount:            adjustment.Amount,
		Currency:          adjustment.Currency,
		EffectiveDate:     adjustment.EffectiveDate,
		CustomerAccountID: adjustment.CustomerAccountID,
		CreatorID:         adjustment.CreatorID,
		CreateTime:        adjustment.CreateTime,
		UpdateTime:        adjustment.UpdateTime,
	}

	return resp, valueobject.SUCCESS, nil
}

// BatchCreateFinancialAdjustments 批量创建财务调整记录
func (s *FinancialAdjustmentServiceImpl) BatchCreateFinancialAdjustments(ctx context.Context, req *BatchCreateFinancialAdjustmentDTO) (*BatchCreateFinancialAdjustmentResponse, int, error) {
	// 1. 验证调整类型
	validTypes := map[string]bool{
		string(valueobject.AdjustmentTypeReassignment): true,
		string(valueobject.AdjustmentTypeReturn):       true,
		string(valueobject.AdjustmentTypeDestruction):  true,
		string(valueobject.AdjustmentTypeCompensation): true,
		string(valueobject.AdjustmentTypeFee):          true,
		string(valueobject.AdjustmentTypeRebate):       true,
		string(valueobject.AdjustmentTypeOther):        true,
	}
	
	for _, adjItem := range req.AdjustmentItems {
		if !validTypes[adjItem.AdjustmentType] {
			return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("无效的调整类型: " + adjItem.AdjustmentType)
		}
	}

	// 2. 根据订单号查询运单信息
	manifestMap := make(map[string]*entity.Manifest)
	
	// 分别查询每个订单号对应的运单
	for _, trackingNumber := range req.TrackingNumbers {
		// 使用现有的模糊查询方法
		manifests, _, err := s.manifestRepo.FindManifestsByExpressNumber(ctx, trackingNumber, 1, 10)
		if err != nil {
			continue // 查询失败的情况下继续处理下一个
		}
		
		// 查找精确匹配的运单
		for _, manifest := range manifests {
			if manifest.ExpressNumber == trackingNumber || 
			   (manifest.TransferredTrackingNumber != "" && manifest.TransferredTrackingNumber == trackingNumber) {
				manifestMap[trackingNumber] = manifest
				break
			}
		}
	}

	// 3. 初始化响应结构
	response := &BatchCreateFinancialAdjustmentResponse{
		SuccessItems: make([]*FinancialAdjustmentItemResponse, 0),
		FailureItems: make([]*BatchCreateFailureItem, 0),
		Summary: &BatchCreateSummary{
			TotalAmount: 0,
		},
	}

	// 4. 批量创建调整记录
	for _, trackingNumber := range req.TrackingNumbers {
		manifest, exists := manifestMap[trackingNumber]
		if !exists {
			// 订单号不存在，记录失败项
			for _, adjItem := range req.AdjustmentItems {
				response.FailureItems = append(response.FailureItems, &BatchCreateFailureItem{
					TrackingNumber:  trackingNumber,
					AdjustmentType:  adjItem.AdjustmentType,
					ErrorCode:       valueobject.ERROR_RESOURCE_NOT_FOUND,
					ErrorMessage:    "订单号不存在: " + trackingNumber,
				})
				response.FailureCount++
			}
			continue
		}

		// 为每种调整类型创建记录
		for _, adjItem := range req.AdjustmentItems {
			adjustmentItem, err := s.createSingleAdjustment(ctx, manifest, adjItem, req)
			if err != nil {
				// 记录失败项
				response.FailureItems = append(response.FailureItems, &BatchCreateFailureItem{
					TrackingNumber:  trackingNumber,
					AdjustmentType:  adjItem.AdjustmentType,
					ErrorCode:       valueobject.ERROR_UNKNOWN,
					ErrorMessage:    err.Error(),
				})
				response.FailureCount++
			} else {
				// 记录成功项
				response.SuccessItems = append(response.SuccessItems, adjustmentItem)
				response.SuccessCount++
				response.Summary.TotalAmount += adjItem.Amount
				
				// 更新各类型统计
				switch adjItem.AdjustmentType {
				case string(valueobject.AdjustmentTypeReassignment):
					response.Summary.ReassignmentCount++
				case string(valueobject.AdjustmentTypeReturn):
					response.Summary.ReturnCount++
				case string(valueobject.AdjustmentTypeDestruction):
					response.Summary.DestructionCount++
				}
			}
		}
	}

	// 5. 计算总数
	response.TotalCount = len(req.TrackingNumbers) * len(req.AdjustmentItems)

	return response, valueobject.SUCCESS, nil
}

// createSingleAdjustment 创建单个调整记录的辅助方法
func (s *FinancialAdjustmentServiceImpl) createSingleAdjustment(ctx context.Context, manifest *entity.Manifest, adjItem BatchCreateAdjustmentItem, req *BatchCreateFinancialAdjustmentDTO) (*FinancialAdjustmentItemResponse, error) {
	// 创建财务调整实体
	adjustment := &entity.ManifestFinancialAdjustment{
		ManifestID:        manifest.ID,
		AdjustmentType:    adjItem.AdjustmentType,
		Description:       req.Description,
		Amount:            adjItem.Amount,
		Currency:          req.Currency,
		EffectiveDate:     req.EffectiveDate,
		CustomerAccountID: manifest.UserID, // 从运单中获取用户ID作为客户账户ID
		CreatorID:         &req.CreatorID,
		CreateTime:        time.Now(),
		UpdateTime:        time.Now(),
	}

	// 根据调整类型设置附加详情
	switch adjItem.AdjustmentType {
	case string(valueobject.AdjustmentTypeReassignment):
		// 改派类型可能需要改派单号等附加信息
		if req.Description != "" {
			details := make(entity.AdditionalDetails)
			details["description"] = req.Description
			adjustment.AdditionalDetails = &details
		}
	case string(valueobject.AdjustmentTypeReturn):
		// 退回类型的附加信息
		if req.Description != "" {
			details := make(entity.AdditionalDetails)
			details["return_reason"] = req.Description
			adjustment.AdditionalDetails = &details
		}
	case string(valueobject.AdjustmentTypeDestruction):
		// 销毁类型的附加信息
		if req.Description != "" {
			details := make(entity.AdditionalDetails)
			details["destruction_reason"] = req.Description
			adjustment.AdditionalDetails = &details
		}
	}

	// 保存到数据库
	if err := s.manifestFinancialAdjustmentRepo.Save(ctx, adjustment); err != nil {
		return nil, err
	}

	// 构建响应
	resp := &FinancialAdjustmentItemResponse{
		ID:                adjustment.ID,
		ManifestID:        adjustment.ManifestID,
		AdjustmentType:    adjustment.AdjustmentType,
		Description:       adjustment.Description,
		Amount:            adjustment.Amount,
		Currency:          adjustment.Currency,
		EffectiveDate:     adjustment.EffectiveDate,
		CustomerAccountID: adjustment.CustomerAccountID,
		CreatorID:         adjustment.CreatorID,
		CreateTime:        adjustment.CreateTime,
		UpdateTime:        adjustment.UpdateTime,
	}

	// 处理AdditionalDetails字段类型转换
	if adjustment.AdditionalDetails != nil {
		resp.AdditionalDetails = map[string]interface{}(*adjustment.AdditionalDetails)
	}

	// 设置运单相关信息
	if manifest.ExpressNumber != "" {
		resp.TrackingNumber = &manifest.ExpressNumber
	}
	if manifest.TransferredTrackingNumber != "" {
		resp.TransferredNumber = &manifest.TransferredTrackingNumber
	}

	return resp, nil
}
