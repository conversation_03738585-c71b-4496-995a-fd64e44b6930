# 服务重构 - 运费模板查询功能分离

## 修改概述

将运费模板查询功能从`BillingService`中完全分离出来，创建专门的`ConfigService`来处理配置管理相关的业务逻辑，实现更清晰的服务职责分离和架构设计。

## 修改背景

在之前的架构中，运费模板查询功能被放置在`BillingService`中，这导致了以下问题：

1. **职责混乱**: 账单服务处理配置管理功能
2. **耦合度高**: 配置相关功能与账单业务逻辑混合
3. **维护困难**: 配置功能分散在不同的服务中
4. **架构不清晰**: 违反了单一职责原则

## 修改内容

### 1. 创建新的 ConfigService

**文件**: `internal/app/service/config_service.go`

#### 服务接口定义

```go
// ConfigService 配置管理服务接口
type ConfigService interface {
	// GetUserShippingFeeTemplates 根据用户ID查询三种类型的运费模板
	GetUserShippingFeeTemplates(ctx context.Context, req *GetUserShippingFeeTemplatesRequest) (*GetUserShippingFeeTemplatesResponse, int, error)
}
```

#### 服务实现

```go
// ConfigServiceImpl 配置管理服务实现
type ConfigServiceImpl struct {
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository
}

// NewConfigService 创建配置管理服务
func NewConfigService(shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository) ConfigService {
	return &ConfigServiceImpl{
		shippingFeeTemplateRepo: shippingFeeTemplateRepo,
	}
}
```

#### 业务逻辑实现

- 参数验证
- 调用仓储层查询用户运费模板
- 数据转换为 DTO 格式
- 错误处理和日志记录

### 2. 数据传输对象(DTO)迁移

将以下 DTO 从`BillingService`迁移到`ConfigService`:

#### GetUserShippingFeeTemplatesRequest

```go
type GetUserShippingFeeTemplatesRequest struct {
	UserID int64 `uri:"userId" binding:"required"` // 用户ID
}
```

#### UserShippingFeeTemplateDTO

```go
type UserShippingFeeTemplateDTO struct {
	ID                      int64   `json:"id"`                      // 模板ID
	Name                    string  `json:"name"`                    // 模板名称
	Type                    int     `json:"type"`                    // 模板类型：1-普通模板；2-带电模板；3-投函模板
	TypeName                string  `json:"typeName"`                // 模板类型名称
	FirstWeightPrice        float64 `json:"firstWeightPrice"`        // 首重价格
	FirstWeightRange        float64 `json:"firstWeightRange"`        // 首重范围
	ContinuedWeightPrice    float64 `json:"continuedWeightPrice"`    // 续重价格
	ContinuedWeightInterval float64 `json:"continuedWeightInterval"` // 续重区间大小
	BulkCoefficient         int     `json:"bulkCoefficient"`         // 轻抛系数
	ThreeSidesStart         float64 `json:"threeSidesStart"`         // 三边和超过多少开始计算体积重量
	CreateTime              string  `json:"createTime"`              // 创建时间，格式：yyyy-MM-dd HH:mm:ss
	UpdateTime              string  `json:"updateTime"`              // 更新时间，格式：yyyy-MM-dd HH:mm:ss
}
```

#### GetUserShippingFeeTemplatesResponse

```go
type GetUserShippingFeeTemplatesResponse struct {
	UserID    int64                         `json:"userId"`    // 用户ID
	Templates []*UserShippingFeeTemplateDTO `json:"templates"` // 模板列表
}
```

### 3. 从 BillingService 中移除相关功能

#### 移除的接口方法

- `GetUserShippingFeeTemplates` - 已移动到`ConfigService`

#### 移除的 DTO 结构

- `GetUserShippingFeeTemplatesRequest`
- `UserShippingFeeTemplateDTO`
- `GetUserShippingFeeTemplatesResponse`

#### 移除的实现方法

- `GetUserShippingFeeTemplates` - 业务逻辑实现
- `getTemplateTypeName` - 辅助方法

### 4. 更新 ConfigHandler

**文件**: `internal/handler/config_handler.go`

#### 依赖注入调整

```go
// ConfigHandler 配置管理处理器
type ConfigHandler struct {
	configService service.ConfigService  // 从billingService改为configService
}

// NewConfigHandler 创建配置管理处理器
func NewConfigHandler(configService service.ConfigService) *ConfigHandler {
	return &ConfigHandler{
		configService: configService,
	}
}
```

#### 方法调用调整

```go
// 调用服务层查询用户运费模板
resp, code, err := h.configService.GetUserShippingFeeTemplates(c.Request.Context(), &req)
```

### 5. 更新主程序初始化

**文件**: `cmd/api-server/main.go`

#### 服务初始化

```go
// 初始化应用服务
configService := appservice.NewConfigService(shippingFeeTemplateRepo)
```

#### Handler 初始化

```go
// 初始化处理器
configHandler := handler.NewConfigHandler(configService)
```

#### 路由设置

```go
// 设置路由
r := router.SetupRouter(userHandler, manifestHandler, problemTicketHandler, trackingHandler, financeHandler, masterBillHandler, financialAdjustmentHandler, configHandler)
```

### 6. 路由配置保持不变

**文件**: `internal/router/router.go`

路由配置已经在之前的重构中调整为使用`configHandler`，无需进一步修改：

```go
// 配置管理相关路由
configRoutes := authGroup.Group("/config")
{
    configRoutes.GET("/shipping-fee-templates/user/:userId", configHandler.GetUserShippingFeeTemplates) // 根据用户ID查询运费模板
}
```

## 架构优势

### 1. 职责清晰

- **BillingService**: 专注于账单生成、查询、管理等财务业务逻辑
- **ConfigService**: 专注于系统配置管理，包括运费模板、规则配置等

### 2. 低耦合

- 配置管理功能与账单业务逻辑完全分离
- 各服务只依赖自己需要的仓储接口

### 3. 高内聚

- 相关的配置管理功能集中在一个服务中
- 便于后续扩展其他配置管理功能

### 4. 易维护

- 配置相关的修改只需要关注 ConfigService
- 减少了服务间的相互影响

### 5. 可扩展

- 为后续添加其他配置管理功能提供了良好的基础
- 符合微服务架构的设计原则

## 编译验证

所有修改完成后，代码编译成功，无任何错误：

```bash
go build -v ./cmd/api-server/
```

## 总结

通过这次服务重构，我们成功地将运费模板查询功能从`BillingService`中分离出来，创建了专门的`ConfigService`来处理配置管理相关的业务逻辑。这次重构不仅提高了代码的可维护性和可扩展性，还使系统架构更加清晰和合理，为后续的功能扩展奠定了良好的基础。
