package service

import "context"

// TranslateService 翻译服务接口
type TranslateService interface {
	// TranslateText 将文本从源语言翻译到目标语言
	// 如果不指定源语言（传入空字符串），则自动检测源语言
	TranslateText(ctx context.Context, text string, sourceLanguage, targetLanguage string) (string, error)
	
	// TranslateTexts 批量翻译文本从源语言到目标语言
	// 如果不指定源语言（传入空字符串），则自动检测源语言
	TranslateTexts(ctx context.Context, texts []string, sourceLanguage, targetLanguage string) ([]string, error)
} 