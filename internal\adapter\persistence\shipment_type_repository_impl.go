package persistence

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// ShipmentTypeRepositoryImpl 货物类型仓储实现
type ShipmentTypeRepositoryImpl struct {
	db *gorm.DB
}

// NewShipmentTypeRepository 创建货物类型仓储实例
func NewShipmentTypeRepository(db *gorm.DB) repository.ShipmentTypeRepository {
	return &ShipmentTypeRepositoryImpl{db: db}
}

// GetAllShipmentTypes 获取所有货物类型
func (r *ShipmentTypeRepositoryImpl) GetAllShipmentTypes(ctx context.Context) ([]*entity.ShipmentType, error) {
	var shipmentTypes []*entity.ShipmentType
	err := r.db.WithContext(ctx).
		Order("id ASC").
		Find(&shipmentTypes).Error
	return shipmentTypes, err
}

// GetShipmentTypeByID 根据ID获取货物类型
func (r *ShipmentTypeRepositoryImpl) GetShipmentTypeByID(ctx context.Context, id int64) (*entity.ShipmentType, error) {
	var shipmentType entity.ShipmentType
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&shipmentType).Error
	if err != nil {
		return nil, err
	}
	return &shipmentType, nil
}

// GetActiveShipmentTypes 获取所有启用的货物类型
func (r *ShipmentTypeRepositoryImpl) GetActiveShipmentTypes(ctx context.Context) ([]*entity.ShipmentType, error) {
	var shipmentTypes []*entity.ShipmentType
	err := r.db.WithContext(ctx).
		Where("is_active = ?", true).
		Order("id ASC").
		Find(&shipmentTypes).Error
	return shipmentTypes, err
} 