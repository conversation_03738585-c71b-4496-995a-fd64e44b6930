@echo off
REM 账单生成消费者测试脚本

setlocal enabledelayedexpansion

echo ====================================
echo 账单生成消费者功能测试
echo ====================================
echo.

REM 设置工作目录
cd /d "%~dp0\.."

echo [1/6] 检查Go环境...
go version >NUL 2>&1
if !ERRORLEVEL! neq 0 (
    echo ❌ Go环境未安装或未配置
    goto error
)
echo ✅ Go环境正常

echo.
echo [2/6] 检查配置文件...
if not exist "configs\config.yaml" (
    echo ❌ 配置文件不存在: configs\config.yaml
    goto error
)
echo ✅ 配置文件存在

echo.
echo [3/6] 构建消费者程序...
go build -o bin\billing-consumer-test.exe cmd\billing-consumer\main.go
if !ERRORLEVEL! neq 0 (
    echo ❌ 构建失败
    goto error
)
echo ✅ 构建成功

echo.
echo [4/6] 检查RabbitMQ连接...
REM 从配置文件中提取RabbitMQ配置进行简单检测
findstr "host:" configs\config.yaml | findstr -v "#" > temp_host.txt
findstr "port:" configs\config.yaml | findstr -v "#" | findstr "567" > temp_port.txt

if exist temp_host.txt (
    for /f "tokens=2" %%i in (temp_host.txt) do set RABBITMQ_HOST=%%i
)
if exist temp_port.txt (
    for /f "tokens=2" %%i in (temp_port.txt) do set RABBITMQ_PORT=%%i
)

del temp_host.txt temp_port.txt 2>NUL

if defined RABBITMQ_HOST if defined RABBITMQ_PORT (
    echo 检测到RabbitMQ配置: %RABBITMQ_HOST%:%RABBITMQ_PORT%
    
    REM 简单的端口连通性测试
    powershell -Command "try { $socket = New-Object System.Net.Sockets.TcpClient; $socket.Connect('%RABBITMQ_HOST%', %RABBITMQ_PORT%); $socket.Close(); exit 0 } catch { exit 1 }" >NUL 2>&1
    if !ERRORLEVEL! equ 0 (
        echo ✅ RabbitMQ连接正常
    ) else (
        echo ⚠️  RabbitMQ连接可能有问题，但继续测试
    )
) else (
    echo ⚠️  无法解析RabbitMQ配置，跳过连接测试
)

echo.
echo [5/6] 检查数据库连接配置...
findstr "host:" configs\config.yaml | findstr -v "#" | findstr -v "rabbitmq" > temp_db_host.txt
findstr "port:" configs\config.yaml | findstr -v "#" | findstr "333" > temp_db_port.txt

if exist temp_db_host.txt (
    for /f "tokens=2" %%i in (temp_db_host.txt) do set DB_HOST=%%i
)
if exist temp_db_port.txt (
    for /f "tokens=2" %%i in (temp_db_port.txt) do set DB_PORT=%%i
)

del temp_db_host.txt temp_db_port.txt 2>NUL

if defined DB_HOST if defined DB_PORT (
    echo 检测到数据库配置: %DB_HOST%:%DB_PORT%
    echo ✅ 数据库配置检查完成
) else (
    echo ⚠️  无法解析数据库配置
)

echo.
echo [6/6] 运行干运行测试...
echo 启动消费者程序进行配置验证（5秒后自动停止）...

REM 启动消费者程序，并在5秒后停止
start /B bin\billing-consumer-test.exe >test_output.log 2>&1

REM 等待5秒
timeout /t 5 /nobreak >NUL

REM 查找并停止测试进程
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq billing-consumer-test.exe" /FO CSV ^| find /V "PID"') do (
    set "TEST_PID=%%i"
    set "TEST_PID=!TEST_PID:"=!"
    if defined TEST_PID (
        taskkill /PID !TEST_PID! /F >NUL 2>&1
    )
)

REM 检查日志输出
if exist test_output.log (
    echo.
    echo 📋 测试日志输出:
    echo ========================
    type test_output.log
    echo ========================
    
    REM 检查关键词来判断启动是否成功
    findstr "Starting billing consumer service" test_output.log >NUL 2>&1
    if !ERRORLEVEL! equ 0 (
        echo ✅ 消费者服务启动成功
    ) else (
        findstr "Failed" test_output.log >NUL 2>&1
        if !ERRORLEVEL! equ 0 (
            echo ❌ 消费者启动失败，请检查日志
            goto cleanup
        ) else (
            echo ⚠️  无法确定启动状态
        )
    )
    
    REM 清理测试日志
    del test_output.log 2>NUL
) else (
    echo ❌ 未生成测试日志文件
)

echo.
echo ====================================
echo ✅ 消费者功能测试完成！
echo ====================================
echo.
echo 下一步操作:
echo 1. 启动消费者: scripts\start-billing-consumer.bat start
echo 2. 查看状态: scripts\start-billing-consumer.bat status  
echo 3. 查看日志: scripts\start-billing-consumer.bat logs
echo 4. 测试异步账单生成API接口
echo.

goto cleanup

:error
echo.
echo ❌ 测试失败！请检查上述错误信息。
echo.
goto cleanup

:cleanup
REM 清理测试文件
if exist bin\billing-consumer-test.exe del bin\billing-consumer-test.exe 2>NUL
if exist test_output.log del test_output.log 2>NUL

pause 