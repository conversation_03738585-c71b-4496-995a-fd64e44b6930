package message_queue

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/config"
	"zebra-hub-system/internal/domain/entity"

	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// BillingConsumer 账单生成消息消费者
type BillingConsumer struct {
	conn          *amqp.Connection
	taskProcessor *service.BillingTaskProcessor
	logger        *zap.Logger
	
	// 消费者配置
	maxRetries    int    // 最大重试次数
	prefetchCount int    // 预获取消息数量
	workers       int    // 并发工作者数量
}

// NewBillingConsumer 创建账单生成消息消费者
func NewBillingConsumer(
	conn *amqp.Connection,
	taskProcessor *service.BillingTaskProcessor,
	logger *zap.Logger,
) *BillingConsumer {
	return &BillingConsumer{
		conn:          conn,
		taskProcessor: taskProcessor,
		logger:        logger,
		maxRetries:    3,    // 默认最大重试3次
		prefetchCount: 1,    // 每次预获取1条消息，确保任务顺序处理
		workers:       2,    // 默认2个并发工作者
	}
}

// Start 启动消费者
func (c *BillingConsumer) Start(ctx context.Context) error {
	c.logger.Info("启动账单生成消息消费者",
		zap.Int("workers", c.workers),
		zap.Int("prefetchCount", c.prefetchCount),
		zap.Int("maxRetries", c.maxRetries))

	// 创建工作者池
	errChan := make(chan error, c.workers)
	
	for i := 0; i < c.workers; i++ {
		workerID := i + 1
		go func(id int) {
			errChan <- c.startWorker(ctx, id)
		}(workerID)
	}

	// 等待任一工作者出错或上下文取消
	select {
	case err := <-errChan:
		if err != nil {
			c.logger.Error("工作者异常退出", zap.Error(err))
			return err
		}
		return nil
	case <-ctx.Done():
		c.logger.Info("收到停止信号，正在关闭消费者")
		return nil
	}
}

// startWorker 启动单个工作者
func (c *BillingConsumer) startWorker(ctx context.Context, workerID int) error {
	c.logger.Info("启动消费者工作者", zap.Int("workerId", workerID))

	// 创建通道
	ch, err := c.conn.Channel()
	if err != nil {
		return fmt.Errorf("创建通道失败: %w", err)
	}
	defer ch.Close()

	// 设置QoS，控制预获取消息数量
	if err := ch.Qos(c.prefetchCount, 0, false); err != nil {
		return fmt.Errorf("设置QoS失败: %w", err)
	}

	// 声明交换机
	if err := c.declareExchangeAndQueue(ch); err != nil {
		return fmt.Errorf("声明交换机和队列失败: %w", err)
	}

	// 开始消费消息
	msgs, err := ch.Consume(
		config.BillingGenerationQueue, // 队列名
		fmt.Sprintf("billing-consumer-%d", workerID), // 消费者标签
		false, // 不自动确认
		false, // 非独占
		false, // 不等待服务器确认
		false, // 不阻塞
		nil,   // 额外参数
	)
	if err != nil {
		return fmt.Errorf("开始消费消息失败: %w", err)
	}

	c.logger.Info("消费者工作者开始监听消息", zap.Int("workerId", workerID))

	for {
		select {
		case <-ctx.Done():
			c.logger.Info("工作者收到停止信号", zap.Int("workerId", workerID))
			return nil

		case delivery, ok := <-msgs:
			if !ok {
				c.logger.Warn("消息通道已关闭", zap.Int("workerId", workerID))
				return nil
			}

			c.logger.Info("工作者收到消息",
				zap.Int("workerId", workerID),
				zap.String("messageId", delivery.MessageId))

			// 处理消息
			if err := c.handleMessage(ctx, delivery); err != nil {
				c.logger.Error("处理消息失败",
					zap.Int("workerId", workerID),
					zap.String("messageId", delivery.MessageId),
					zap.Error(err))
				
				// 消息处理失败，拒绝消息
				delivery.Nack(false, false) // 不重新入队
			} else {
				c.logger.Info("消息处理成功",
					zap.Int("workerId", workerID),
					zap.String("messageId", delivery.MessageId))
				
				// 消息处理成功，确认消息
				delivery.Ack(false)
			}
		}
	}
}

// handleMessage 处理单条消息
func (c *BillingConsumer) handleMessage(ctx context.Context, delivery amqp.Delivery) error {
	// 解析消息体
	var message entity.BillingGenerationMessage
	if err := json.Unmarshal(delivery.Body, &message); err != nil {
		c.logger.Error("解析消息失败",
			zap.String("messageId", delivery.MessageId),
			zap.ByteString("body", delivery.Body),
			zap.Error(err))
		return fmt.Errorf("解析消息失败: %w", err)
	}

	c.logger.Info("开始处理账单生成任务",
		zap.String("taskId", message.TaskID),
		zap.String("messageId", message.MessageID),
		zap.Int("customerCount", len(message.CustomerIDs)),
		zap.Int("retryCount", message.RetryCount))

	// 创建处理上下文，设置超时
	taskCtx, cancel := context.WithTimeout(ctx, 30*time.Minute) // 30分钟超时
	defer cancel()

	// 调用任务处理器处理任务
	if err := c.taskProcessor.ProcessBillingTask(taskCtx, &message); err != nil {
		c.logger.Error("任务处理失败",
			zap.String("taskId", message.TaskID),
			zap.String("messageId", message.MessageID),
			zap.Error(err))

		// 判断是否需要重试
		if c.shouldRetry(&message, delivery) {
			return c.retryMessage(delivery, &message)
		}

		return fmt.Errorf("任务处理失败: %w", err)
	}

	c.logger.Info("任务处理完成",
		zap.String("taskId", message.TaskID),
		zap.String("messageId", message.MessageID))

	return nil
}

// shouldRetry 判断是否应该重试
func (c *BillingConsumer) shouldRetry(message *entity.BillingGenerationMessage, delivery amqp.Delivery) bool {
	// 检查重试次数
	if message.RetryCount >= c.maxRetries {
		c.logger.Warn("消息已达到最大重试次数",
			zap.String("taskId", message.TaskID),
			zap.String("messageId", message.MessageID),
			zap.Int("retryCount", message.RetryCount),
			zap.Int("maxRetries", c.maxRetries))
		return false
	}

	// 检查消息年龄，如果消息太老就不重试
	if delivery.Timestamp.Before(time.Now().Add(-24 * time.Hour)) {
		c.logger.Warn("消息过于陈旧，不再重试",
			zap.String("taskId", message.TaskID),
			zap.Time("messageTimestamp", delivery.Timestamp))
		return false
	}

	return true
}

// retryMessage 重试消息
func (c *BillingConsumer) retryMessage(delivery amqp.Delivery, message *entity.BillingGenerationMessage) error {
	c.logger.Info("准备重试消息",
		zap.String("taskId", message.TaskID),
		zap.String("messageId", message.MessageID),
		zap.Int("currentRetryCount", message.RetryCount))

	// 增加重试次数
	message.RetryCount++

	// 计算延迟时间（指数退避）
	delay := time.Duration(message.RetryCount*message.RetryCount) * time.Second // 1s, 4s, 9s...
	if delay > 5*time.Minute {
		delay = 5 * time.Minute // 最大延迟5分钟
	}

	c.logger.Info("消息将延迟重试",
		zap.String("taskId", message.TaskID),
		zap.Duration("delay", delay),
		zap.Int("retryCount", message.RetryCount))

	// 创建新的消息体
	retryMessageBody, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化重试消息失败: %w", err)
	}

	// 创建延迟发布的通道
	ch, err := c.conn.Channel()
	if err != nil {
		return fmt.Errorf("创建重试通道失败: %w", err)
	}
	defer ch.Close()

	// 发布延迟消息（这里可以使用RabbitMQ的延迟插件或TTL+DLX实现）
	// 简单实现：使用goroutine延迟发布
	go func() {
		time.Sleep(delay)
		
		// 重新发布消息
		err := ch.Publish(
			config.BillingGenerationExchange,   // 交换机
			config.BillingGenerationRoutingKey, // 路由键
			false, // 不强制
			false, // 不立即
			amqp.Publishing{
				ContentType:  "application/json",
				Body:         retryMessageBody,
				MessageId:    message.MessageID,
				Timestamp:    time.Now(),
				DeliveryMode: amqp.Persistent, // 持久化
				Headers: amqp.Table{
					"taskId":         message.TaskID,
					"billingCycleId": message.BillingCycleID,
					"customerCount":  len(message.CustomerIDs),
					"retryCount":     message.RetryCount,
					"isRetry":        true,
				},
			},
		)

		if err != nil {
			c.logger.Error("重新发布消息失败",
				zap.String("taskId", message.TaskID),
				zap.Error(err))
		} else {
			c.logger.Info("消息重新发布成功",
				zap.String("taskId", message.TaskID),
				zap.Int("retryCount", message.RetryCount))
		}
	}()

	return nil
}

// declareExchangeAndQueue 声明交换机和队列
func (c *BillingConsumer) declareExchangeAndQueue(ch *amqp.Channel) error {
	// 声明交换机
	if err := ch.ExchangeDeclare(
		config.BillingGenerationExchange, // 交换机名称
		"direct",                         // 交换机类型
		true,                             // 持久化
		false,                            // 不自动删除
		false,                            // 不排他
		false,                            // 不等待服务器确认
		nil,                              // 额外参数
	); err != nil {
		return fmt.Errorf("声明交换机失败: %w", err)
	}

	// 声明队列
	queue, err := ch.QueueDeclare(
		config.BillingGenerationQueue, // 队列名称
		true,                          // 持久化
		false,                         // 不自动删除
		false,                         // 不排他
		false,                         // 不等待服务器确认
		nil,                           // 额外参数
	)
	if err != nil {
		return fmt.Errorf("声明队列失败: %w", err)
	}

	// 绑定队列到交换机
	if err := ch.QueueBind(
		queue.Name,                         // 队列名称
		config.BillingGenerationRoutingKey, // 路由键
		config.BillingGenerationExchange,   // 交换机名称
		false,                              // 不等待服务器确认
		nil,                                // 额外参数
	); err != nil {
		return fmt.Errorf("绑定队列失败: %w", err)
	}

	c.logger.Info("成功声明交换机和队列",
		zap.String("exchange", config.BillingGenerationExchange),
		zap.String("queue", config.BillingGenerationQueue),
		zap.String("routingKey", config.BillingGenerationRoutingKey))

	return nil
}

// SetWorkers 设置并发工作者数量
func (c *BillingConsumer) SetWorkers(workers int) {
	if workers <= 0 {
		workers = 1
	}
	c.workers = workers
}

// SetMaxRetries 设置最大重试次数
func (c *BillingConsumer) SetMaxRetries(maxRetries int) {
	if maxRetries < 0 {
		maxRetries = 0
	}
	c.maxRetries = maxRetries
}

// SetPrefetchCount 设置预获取消息数量
func (c *BillingConsumer) SetPrefetchCount(prefetchCount int) {
	if prefetchCount <= 0 {
		prefetchCount = 1
	}
	c.prefetchCount = prefetchCount
} 