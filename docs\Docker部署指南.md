# Docker 部署指南

## 概述

本项目包含两个独立的服务：

- **API 服务器** (`api-server`) - 提供 REST API 接口
- **账单消费者** (`billing-consumer`) - 处理账单生成任务的消息队列消费者

## 部署架构

```
┌─────────────────┐    ┌─────────────────┐
│   API服务器     │    │   账单消费者     │
│   (8080端口)    │    │   (无端口)      │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │   共享资源       │
         │ • MySQL数据库   │
         │ • RabbitMQ      │
         │ • 配置文件      │
         └─────────────────┘
```

## 文件说明

### Docker 配置文件

| 文件                        | 用途         | 说明                          |
| --------------------------- | ------------ | ----------------------------- |
| `Dockerfile`                | 通用构建文件 | 支持构建 API 服务器和其他服务 |
| `Dockerfile.consumer`       | 消费者专用   | 专门用于构建账单消费者        |
| `docker-compose.yml`        | 完整部署     | 包含应用+数据库+消息队列      |
| `docker-compose.simple.yml` | 简化部署     | 仅包含应用服务                |

### 脚本文件

| 脚本                        | 用途                    | 说明                     |
| --------------------------- | ----------------------- | ------------------------ |
| `scripts/build-docker.sh`   | 构建镜像（Linux/macOS） | 批量构建所有 Docker 镜像 |
| `scripts/build-docker.cmd`  | 构建镜像（Windows）     | 批量构建所有 Docker 镜像 |
| `scripts/deploy-docker.sh`  | 部署服务（Linux/macOS） | 启动/停止/重启服务       |
| `scripts/deploy-docker.cmd` | 部署服务（Windows）     | 启动/停止/重启服务       |

## 快速开始

### 1. 构建镜像

#### Linux/macOS:

```bash
# 构建所有镜像
./scripts/build-docker.sh

# 或者分别构建
./scripts/build-docker.sh api      # 仅构建API服务器
./scripts/build-docker.sh consumer # 仅构建账单消费者
```

#### Windows:

```cmd
# 构建所有镜像
scripts\build-docker.cmd

# 或者分别构建
scripts\build-docker.cmd api      # 仅构建API服务器
scripts\build-docker.cmd consumer # 仅构建账单消费者
```

### 2. 部署服务

#### 方式一：简化部署（推荐用于生产环境）

适用于已有外部 MySQL 和 RabbitMQ 的情况：

##### Linux/macOS:

```bash
# 启动服务
./scripts/deploy-docker.sh simple up

# 停止服务
./scripts/deploy-docker.sh simple down

# 重启服务
./scripts/deploy-docker.sh simple restart
```

##### Windows:

```cmd
# 启动服务
scripts\deploy-docker.cmd simple up

# 停止服务
scripts\deploy-docker.cmd simple down

# 重启服务
scripts\deploy-docker.cmd simple restart
```

#### 方式二：完整部署（推荐用于开发/测试环境）

包含 MySQL 和 RabbitMQ 容器：

##### Linux/macOS:

```bash
# 启动完整环境
./scripts/deploy-docker.sh full up

# 停止完整环境
./scripts/deploy-docker.sh full down

# 重启完整环境
./scripts/deploy-docker.sh full restart
```

##### Windows:

```cmd
# 启动完整环境
scripts\deploy-docker.cmd full up

# 停止完整环境
scripts\deploy-docker.cmd full down

# 重启完整环境
scripts\deploy-docker.cmd full restart
```

## 详细部署方案

### 方案一：使用构建参数的统一 Dockerfile

#### 构建 API 服务器

```bash
docker build \
  --build-arg APP_NAME=api-server \
  --build-arg BINARY_NAME=zebra-hub-system \
  -t zebra-hub-system/api-server:latest \
  -f Dockerfile .
```

#### 构建账单消费者

```bash
docker build \
  --build-arg APP_NAME=billing-consumer \
  --build-arg BINARY_NAME=billing-consumer \
  -t zebra-hub-system/billing-consumer:latest \
  -f Dockerfile .
```

### 方案二：使用专用 Dockerfile

#### 构建账单消费者（推荐）

```bash
docker build \
  -t zebra-hub-system/billing-consumer:latest \
  -f Dockerfile.consumer .
```

## Docker Compose 部署

### 简化版部署

适用于生产环境，使用外部数据库和消息队列：

```yaml
# docker-compose.simple.yml
version: "3.8"

services:
  api-server:
    image: zebra-hub-system/api-server:latest
    ports:
      - "8080:8080"
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data

  billing-consumer:
    image: zebra-hub-system/billing-consumer:latest
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
```

```bash
docker-compose -f docker-compose.simple.yml up -d
```

### 完整版部署

适用于开发/测试环境，包含所有依赖：

```bash
docker-compose up -d
```

## 服务配置

### 端口映射

| 服务          | 内部端口 | 外部端口 | 说明                       |
| ------------- | -------- | -------- | -------------------------- |
| API 服务器    | 8080     | 8080     | REST API 接口              |
| MySQL         | 3306     | 3306     | 数据库（仅完整部署）       |
| RabbitMQ      | 5672     | 5672     | 消息队列（仅完整部署）     |
| RabbitMQ 管理 | 15672    | 15672    | Web 管理界面（仅完整部署） |

### 卷挂载

| 本地路径    | 容器路径       | 说明         |
| ----------- | -------------- | ------------ |
| `./configs` | `/app/configs` | 配置文件目录 |
| `./data`    | `/app/data`    | 数据文件目录 |

### 环境变量

| 变量 | 值              | 说明     |
| ---- | --------------- | -------- |
| `TZ` | `Asia/Shanghai` | 时区设置 |

## 监控和日志

### 查看服务状态

```bash
docker-compose ps
```

### 查看日志

```bash
# 查看API服务器日志
docker-compose logs -f api-server

# 查看账单消费者日志
docker-compose logs -f billing-consumer

# 查看所有服务日志
docker-compose logs -f
```

### 进入容器

```bash
# 进入API服务器容器
docker exec -it zebra-api-server sh

# 进入账单消费者容器
docker exec -it zebra-billing-consumer sh
```

## 生产环境建议

### 1. 资源配置

#### API 服务器

- **CPU**: 2 核心
- **内存**: 2GB
- **磁盘**: 10GB

#### 账单消费者

- **CPU**: 1 核心
- **内存**: 1GB
- **磁盘**: 5GB

### 2. 扩展配置

#### 水平扩展账单消费者

```bash
docker-compose up -d --scale billing-consumer=3
```

#### 使用外部负载均衡器

```nginx
upstream api_servers {
    server api-server-1:8080;
    server api-server-2:8080;
    server api-server-3:8080;
}

server {
    listen 80;
    location / {
        proxy_pass http://api_servers;
    }
}
```

### 3. 安全配置

#### 网络隔离

- API 服务器：暴露到公网
- 账单消费者：仅内网访问
- 数据库：仅内网访问

#### 环境变量管理

```bash
# 使用.env文件管理敏感信息
echo "MYSQL_ROOT_PASSWORD=your_secure_password" > .env
echo "RABBITMQ_DEFAULT_PASS=your_secure_password" >> .env
```

### 4. 备份策略

#### 数据备份

```bash
# 备份MySQL数据
docker exec zebra-mysql mysqldump -u root -p zebra_hub > backup.sql

# 备份配置文件
tar -czf configs-backup.tar.gz configs/
```

#### 镜像备份

```bash
# 导出镜像
docker save zebra-hub-system/api-server:latest > api-server.tar
docker save zebra-hub-system/billing-consumer:latest > billing-consumer.tar

# 导入镜像
docker load < api-server.tar
docker load < billing-consumer.tar
```

## 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 查看详细日志
docker-compose logs service-name

# 检查容器状态
docker ps -a
```

#### 2. 数据库连接失败

- 检查 MySQL 服务是否启动
- 验证配置文件中的数据库连接信息
- 确认网络连通性

#### 3. 消息队列连接失败

- 检查 RabbitMQ 服务是否启动
- 验证消息队列配置
- 检查用户权限设置

#### 4. 配置文件问题

```bash
# 验证配置文件语法
docker run --rm -v $(pwd)/configs:/configs alpine cat /configs/config.yaml
```

### 调试模式

#### 开启调试日志

```yaml
# 在docker-compose.yml中添加
environment:
  - LOG_LEVEL=debug
```

#### 临时进入容器调试

```bash
docker run -it --rm \
  -v $(pwd)/configs:/app/configs \
  zebra-hub-system/api-server:latest sh
```

## 更新和版本管理

### 滚动更新

```bash
# 拉取最新代码
git pull

# 重新构建镜像
./scripts/build-docker.sh

# 重启服务
./scripts/deploy-docker.sh simple restart
```

### 版本标记

```bash
# 构建特定版本
docker build -t zebra-hub-system/api-server:v1.0.0 .

# 更新docker-compose.yml使用特定版本
# image: zebra-hub-system/api-server:v1.0.0
```

## 性能优化

### 1. 镜像优化

- 使用多阶段构建减少镜像大小
- 选择合适的基础镜像（alpine）
- 合并 RUN 指令减少层数

### 2. 容器优化

- 设置合适的资源限制
- 使用非 root 用户运行
- 配置健康检查

### 3. 网络优化

- 使用自定义网络
- 避免不必要的端口暴露
- 配置 DNS 策略

## 总结

通过本部署指南，您可以：

1. 使用统一的 Dockerfile 构建多个服务
2. 选择合适的部署方案（简化/完整）
3. 使用脚本自动化构建和部署
4. 监控和管理运行中的服务
5. 处理常见的部署问题

推荐在生产环境中使用简化部署方案，配合外部的数据库和消息队列服务，以获得更好的稳定性和可维护性。
