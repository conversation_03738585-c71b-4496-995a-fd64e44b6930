#!/bin/bash

# Docker构建脚本
# 用法: ./scripts/build-docker.sh [all|api|consumer]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 构建API服务器镜像
build_api_server() {
    print_info "构建API服务器镜像..."
    docker build \
        --build-arg APP_NAME=api-server \
        --build-arg BINARY_NAME=zebra-hub-system \
        -t zebra-hub-system/api-server:latest \
        -f Dockerfile .
    print_success "API服务器镜像构建完成"
}

# 构建账单消费者镜像
build_billing_consumer() {
    print_info "构建账单消费者镜像..."
    docker build \
        -t zebra-hub-system/billing-consumer:latest \
        -f Dockerfile.consumer .
    print_success "账单消费者镜像构建完成"
}

# 主逻辑
case "${1:-all}" in
    "api")
        build_api_server
        ;;
    "consumer")
        build_billing_consumer
        ;;
    "all")
        build_api_server
        build_billing_consumer
        ;;
    *)
        print_error "未知参数: $1"
        echo "用法: $0 [all|api|consumer]"
        echo "  all      - 构建所有镜像（默认）"
        echo "  api      - 仅构建API服务器镜像"
        echo "  consumer - 仅构建账单消费者镜像"
        exit 1
        ;;
esac

print_info "查看构建的镜像："
docker images | grep zebra-hub-system

print_success "构建完成！" 