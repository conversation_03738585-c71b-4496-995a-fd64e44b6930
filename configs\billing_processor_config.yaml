# 账单任务处理器配置文件
# 用于配置多线程账单生成的并发参数

# 开发环境配置
development:
  billing_processor:
    # 最大并发数（建议值：3-5）
    # 开发环境资源有限，使用较小的并发数
    max_concurrency: 3

    # 进度更新间隔（毫秒）
    # 开发环境可以使用较长的更新间隔
    progress_update_interval_ms: 1000

    # 数据库连接池大小（应 >= max_concurrency）
    database:
      max_open_connections: 10
      max_idle_connections: 5

# 测试环境配置
testing:
  billing_processor:
    # 测试环境可以使用中等并发数
    max_concurrency: 5

    # 测试时可能需要更频繁的进度反馈
    progress_update_interval_ms: 1000

    database:
      max_open_connections: 15
      max_idle_connections: 8

# 生产环境配置
production:
  billing_processor:
    # 生产环境使用较高并发数，根据服务器性能调整
    max_concurrency: 10

    # 生产环境使用较短的更新间隔，提供更及时的进度反馈
    progress_update_interval_ms: 500

    database:
      max_open_connections: 25
      max_idle_connections: 15

    # 生产环境监控配置
    monitoring:
      # 是否启用详细日志（Debug级别）
      enable_debug_logs: false

      # 是否启用性能指标收集
      enable_metrics: true

      # 慢处理告警阈值（秒）
      slow_processing_threshold_seconds: 30

# 高负载环境配置（大批量处理）
high_load:
  billing_processor:
    # 高负载环境使用最大并发数
    max_concurrency: 15

    # 更频繁的进度更新
    progress_update_interval_ms: 500

    database:
      max_open_connections: 35
      max_idle_connections: 20

    # 高负载环境优化配置
    optimization:
      # 批处理大小
      batch_size: 1000

      # 连接超时（秒）
      connection_timeout_seconds: 30

      # 查询超时（秒）
      query_timeout_seconds: 60

# 配置说明和最佳实践
configuration_guide:
  max_concurrency:
    description: "最大并发Goroutine数量"
    recommendations:
      - "不应超过CPU核心数的2倍"
      - "需要考虑数据库连接池大小"
      - "建议从小值开始，逐步调优"
    limits:
      min: 1
      max: 20
      default: 5

  progress_update_interval_ms:
    description: "进度更新间隔（毫秒）"
    recommendations:
      - "生产环境建议500-1000ms"
      - "开发环境可以使用1000-2000ms"
      - "过短的间隔会增加数据库负载"
    limits:
      min: 100
      max: 5000
      default: 1000

  database_connections:
    description: "数据库连接池配置"
    recommendations:
      - "max_open_connections应≥max_concurrency+5"
      - "max_idle_connections建议为max_open_connections的60-80%"
      - "根据数据库服务器性能调整"

# 环境变量配置示例
environment_variables:
  # 可以通过环境变量覆盖配置
  examples:
    - name: "BILLING_PROCESSOR_MAX_CONCURRENCY"
      description: "覆盖最大并发数"
      example: "10"

    - name: "BILLING_PROCESSOR_PROGRESS_INTERVAL_MS"
      description: "覆盖进度更新间隔"
      example: "500"

    - name: "BILLING_PROCESSOR_ENABLE_DEBUG"
      description: "启用调试日志"
      example: "true"

# 性能调优指南
performance_tuning:
  guidelines:
    - name: "CPU密集型任务"
      recommendation: "并发数 = CPU核心数"

    - name: "IO密集型任务"
      recommendation: "并发数 = CPU核心数 × 2"

    - name: "数据库密集型任务"
      recommendation: "并发数受数据库连接池限制"

  monitoring_metrics:
    - "平均处理时间"
    - "并发利用率"
    - "数据库连接使用率"
    - "错误率"
    - "内存使用量"

  common_issues:
    - issue: "数据库连接池耗尽"
      solution: "增加连接池大小或减少并发数"

    - issue: "内存使用过高"
      solution: "减少并发数或实现分批处理"

    - issue: "CPU使用率过高"
      solution: "减少并发数或优化处理逻辑"
