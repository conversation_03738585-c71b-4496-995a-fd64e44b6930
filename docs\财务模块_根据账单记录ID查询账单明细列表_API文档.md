# 根据账单记录 ID 查询账单明细列表 API 文档

## 接口概述

该接口用于根据指定的账单记录 ID，分页查询该账单下的所有明细记录。每个明细记录包含完整的运单信息、费用计算详情、物品信息和时间记录等。

## 接口信息

- **请求方法**: GET
- **请求路径**: `/api/v1/finance/billing/records/{billingRecordId}/items`
- **需要认证**: 是

## 请求参数

### 路径参数 (Path Parameters)

| 参数名          | 类型  | 必填 | 说明        |
| --------------- | ----- | ---- | ----------- |
| billingRecordId | int64 | 是   | 账单记录 ID |

### 查询参数 (Query Parameters)

| 参数名   | 类型 | 必填 | 默认值 | 说明                     |
| -------- | ---- | ---- | ------ | ------------------------ |
| page     | int  | 否   | 1      | 页码，最小值为 1         |
| pageSize | int  | 否   | 20     | 每页数量，取值范围 1-100 |

## 业务逻辑说明

### 查询流程

1. **参数验证**:

   - 验证账单记录 ID 是否为正整数
   - 验证分页参数的合法性

2. **权限校验**:

   - 验证账单记录是否存在
   - 确保当前用户有查看该账单的权限

3. **明细查询**:

   - 根据账单记录 ID 查询所有相关明细
   - 按明细 ID 升序排列
   - 支持分页查询

4. **数据组装**:
   - 转换为前端友好的 DTO 格式
   - 补充货物类型名称等扩展信息
   - 格式化时间字段

### 明细数据内容

每条账单明细记录包含以下信息：

- **运单基础信息**: 快递单号、订单号、转单号等
- **时间信息**: 运单创建时间、发货时间、签收时间、揽件时间
- **收件人信息**: 收件人姓名
- **物品信息**: 物品描述、货物类型
- **重量尺寸**: 实际重量、长宽高、三边和、体积重、计费重量
- **费用明细**: 基础运费、首重费用、续重费用、超长费、偏远费、总费用
- **记录时间**: 明细创建时间、更新时间

## 响应格式

### 成功响应 (200 OK)

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1001,
        "billingRecordId": 123,
        "manifestId": 45678,
        "expressNumber": "SF1234567890",
        "orderNo": "ORD20240515001",
        "transferredTrackingNumber": "TF1234567890",
        "orderNumber": "CUST2024051501",
        "manifestCreateTime": "2024-05-15 10:30:00",
        "shipmentTime": "2024-05-15 14:00:00",
        "deliveredTime": "2024-05-17 16:30:00",
        "pickUpTime": "2024-05-15 13:45:00",
        "receiverName": "张三",
        "itemDescription": "电子产品, 手机配件",
        "cargoType": 1,
        "cargoTypeName": "普通货物",
        "weight": 0.5,
        "length": 20.0,
        "width": 15.0,
        "height": 8.0,
        "sumOfSides": 43.0,
        "dimensionalWeight": 0.6,
        "chargeableWeight": 0.6,
        "baseFreightFee": 25.5,
        "firstWeightFee": 18.0,
        "continuedWeightFee": 7.5,
        "overLengthSurcharge": 0.0,
        "remoteAreaSurcharge": 5.0,
        "itemTotalAmount": 30.5,
        "createTime": "2024-05-15 18:00:00",
        "updateTime": "2024-05-15 18:00:00"
      }
    ]
  },
  "requestId": "req_20240515_001",
  "timestamp": "2024-05-15T18:00:00Z"
}
```

### 响应字段说明

| 字段名                    | 类型    | 说明                                |
| ------------------------- | ------- | ----------------------------------- |
| total                     | int64   | 明细总数                            |
| list                      | array   | 明细列表                            |
| **明细字段**              |         |                                     |
| id                        | int64   | 账单明细主键 ID                     |
| billingRecordId           | int64   | 所属账单记录 ID                     |
| manifestId                | int64   | 原始运单 ID（可能为空）             |
| expressNumber             | string  | 快递单号（可能为空）                |
| orderNo                   | string  | 系统订单号（可能为空）              |
| transferredTrackingNumber | string  | 转单号（可能为空）                  |
| orderNumber               | string  | 客户订单号（可能为空）              |
| manifestCreateTime        | string  | 运单创建时间（yyyy-MM-dd HH:mm:ss） |
| shipmentTime              | string  | 发货时间（yyyy-MM-dd HH:mm:ss）     |
| deliveredTime             | string  | 签收时间（yyyy-MM-dd HH:mm:ss）     |
| pickUpTime                | string  | 揽件时间（yyyy-MM-dd HH:mm:ss）     |
| receiverName              | string  | 收件人姓名（可能为空）              |
| itemDescription           | string  | 物品描述                            |
| cargoType                 | int     | 货物类型：1-普通、2-带电、3-投函    |
| cargoTypeName             | string  | 货物类型名称                        |
| weight                    | float64 | 实际重量(KG)（可能为空）            |
| length                    | float64 | 长(cm)（可能为空）                  |
| width                     | float64 | 宽(cm)（可能为空）                  |
| height                    | float64 | 高(cm)（可能为空）                  |
| sumOfSides                | float64 | 三边和(cm)（可能为空）              |
| dimensionalWeight         | float64 | 体积重(KG)（可能为空）              |
| chargeableWeight          | float64 | 计费重量(KG)（可能为空）            |
| baseFreightFee            | float64 | 基础运费（可能为空）                |
| firstWeightFee            | float64 | 首重费用（可能为空）                |
| continuedWeightFee        | float64 | 续重费用（可能为空）                |
| overLengthSurcharge       | float64 | 超长费（可能为空）                  |
| remoteAreaSurcharge       | float64 | 偏远费（可能为空）                  |
| itemTotalAmount           | float64 | 明细总费用                          |
| createTime                | string  | 创建时间（yyyy-MM-dd HH:mm:ss）     |
| updateTime                | string  | 更新时间（yyyy-MM-dd HH:mm:ss）     |

## 使用示例

### 基本请求示例

```bash
# 查询账单ID为123的明细列表，第1页，每页20条
GET /api/v1/finance/billing/records/123/items?page=1&pageSize=20
Authorization: Bearer your_jwt_token
```

### 自定义分页示例

```bash
# 查询账单ID为123的明细列表，第2页，每页50条
GET /api/v1/finance/billing/records/123/items?page=2&pageSize=50
Authorization: Bearer your_jwt_token
```

### cURL 请求示例

```bash
curl -X GET \
  'https://api.zebra-hub.com/api/v1/finance/billing/records/123/items?page=1&pageSize=20' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json'
```

## 错误处理

### 常见错误类型

| HTTP 状态码 | 错误码 | 错误类型   | 说明                 |
| ----------- | ------ | ---------- | -------------------- |
| 400         | 100002 | 参数错误   | 账单记录 ID 格式错误 |
| 400         | 100002 | 参数错误   | 分页参数超出范围     |
| 401         | 100004 | 未授权     | JWT Token 无效或过期 |
| 404         | 100006 | 资源不存在 | 账单记录不存在       |
| 500         | 100001 | 系统错误   | 数据库查询失败       |

### 错误响应示例

```json
{
  "success": false,
  "errorCode": 100006,
  "errorMessage": "账单记录不存在",
  "data": null,
  "requestId": "req_20240515_002",
  "timestamp": "2024-05-15T18:00:00Z"
}
```

## 业务场景

### 适用场景

1. **账单详情查看**: 财务人员查看账单的具体明细构成
2. **费用核实**: 客户核对账单中每个运单的费用计算
3. **数据导出**: 导出账单明细用于进一步分析
4. **问题排查**: 当账单金额有疑问时，查看具体的明细计算

### 使用建议

1. **分页查询**: 对于明细较多的账单，建议使用适当的分页大小，避免一次性加载过多数据
2. **时间格式**: 所有时间字段统一使用`yyyy-MM-dd HH:mm:ss`格式，便于前端展示和处理
3. **空值处理**: 某些字段可能为空（如运单 ID、时间字段等），前端需要妥善处理空值显示
4. **费用精度**: 所有费用相关字段保留小数点后 2 位，符合财务规范

### 注意事项

1. **数据权限**: 只能查询当前用户有权限查看的账单明细
2. **时间一致性**: 明细中的时间信息来源于原始运单记录，可能存在时区差异
3. **费用计算**: 明细中的费用是按照生成账单时的运费模板计算的快照数据
4. **货物类型**: 货物类型的判断基于运单创建时的物品信息，可能需要人工确认

## 相关接口

- [生成账单接口](./财务模块_生成账单_API文档.md)
- [查询账单记录列表接口](./财务模块_查询账单记录列表_API文档.md)
- [查询用户运费模板接口](./财务模块_查询用户运费模板_API文档.md)

## 更新日志

| 版本 | 日期       | 变更内容                |
| ---- | ---------- | ----------------------- |
| v1.0 | 2024-05-15 | 初始版本创建            |
| v1.1 | 2024-05-15 | 统一时间格式为 HH:mm:ss |
