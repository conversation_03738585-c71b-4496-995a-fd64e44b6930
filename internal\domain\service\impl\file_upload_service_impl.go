package impl

import (
	"context"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"
	"zebra-hub-system/internal/domain/service"
	"zebra-hub-system/internal/domain/valueobject"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
	"go.uber.org/zap"
)

// FileUploadServiceImpl 文件上传服务实现
type FileUploadServiceImpl struct {
	ossClient  *oss.Client
	bucketName string
	baseURL    string
	logger     *zap.Logger
}

// NewFileUploadService 创建文件上传服务
func NewFileUploadService(
	accessKeyID string,
	accessKeySecret string,
	endpoint string,
	bucketName string,
	logger *zap.Logger,
) (service.FileUploadService, error) {
	// 创建OSS客户端
	client, err := oss.New(endpoint, accessKeyID, accessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	// 构建基础URL (去掉协议前缀，构建标准的OSS URL)
	baseURL := fmt.Sprintf("https://%s.%s", bucketName, strings.TrimPrefix(endpoint, "https://"))

	return &FileUploadServiceImpl{
		ossClient:  client,
		bucketName: bucketName,
		baseURL:    baseURL,
		logger:     logger,
	}, nil
}

// UploadImage 上传图片文件
func (s *FileUploadServiceImpl) UploadImage(ctx context.Context, req *service.UploadFileRequest) (*service.UploadFileResponse, int, error) {
	// 1. 验证图片文件
	if err := s.ValidateImageFile(req.FileName, req.FileSize, req.ContentType); err != nil {
		s.logger.Warn("图片文件验证失败",
			zap.String("fileName", req.FileName),
			zap.Int64("fileSize", req.FileSize),
			zap.String("contentType", req.ContentType),
			zap.Error(err))
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 生成唯一的文件名
	objectKey := s.generateObjectKey(req.FileName, req.BusinessType)

	// 3. 获取OSS bucket
	bucket, err := s.ossClient.Bucket(s.bucketName)
	if err != nil {
		s.logger.Error("获取OSS bucket失败",
			zap.String("bucketName", s.bucketName),
			zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, errors.New("文件上传服务暂时不可用")
	}

	// 4. 上传文件到OSS
	options := []oss.Option{
		oss.ContentType(req.ContentType),
		oss.Meta("original-name", req.FileName),
		oss.Meta("business-type", req.BusinessType),
		oss.Meta("upload-time", time.Now().Format("2006-01-02 15:04:05")),
	}

	if err := bucket.PutObject(objectKey, req.FileData, options...); err != nil {
		s.logger.Error("上传文件到OSS失败",
			zap.String("objectKey", objectKey),
			zap.String("bucketName", s.bucketName),
			zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, errors.New("文件上传失败")
	}

	// 5. 构建文件URL
	fileUrl := fmt.Sprintf("%s/%s", s.baseURL, objectKey)

	s.logger.Info("文件上传成功",
		zap.String("originalFileName", req.FileName),
		zap.String("objectKey", objectKey),
		zap.String("fileUrl", fileUrl),
		zap.Int64("fileSize", req.FileSize))

	return &service.UploadFileResponse{
		FileUrl:    fileUrl,
		FileName:   objectKey,
		FileSize:   req.FileSize,
		UploadTime: time.Now().Format("2006-01-02 15:04:05"),
	}, valueobject.SUCCESS, nil
}

// DeleteFile 删除文件
func (s *FileUploadServiceImpl) DeleteFile(ctx context.Context, fileUrl string) error {
	// 从URL中提取object key
	objectKey := s.extractObjectKeyFromURL(fileUrl)
	if objectKey == "" {
		return errors.New("无效的文件URL")
	}

	// 获取OSS bucket
	bucket, err := s.ossClient.Bucket(s.bucketName)
	if err != nil {
		s.logger.Error("获取OSS bucket失败",
			zap.String("bucketName", s.bucketName),
			zap.Error(err))
		return fmt.Errorf("删除文件失败: %w", err)
	}

	// 删除文件
	if err := bucket.DeleteObject(objectKey); err != nil {
		s.logger.Error("删除OSS文件失败",
			zap.String("objectKey", objectKey),
			zap.String("fileUrl", fileUrl),
			zap.Error(err))
		return fmt.Errorf("删除文件失败: %w", err)
	}

	s.logger.Info("文件删除成功",
		zap.String("objectKey", objectKey),
		zap.String("fileUrl", fileUrl))

	return nil
}

// ValidateImageFile 验证图片文件
func (s *FileUploadServiceImpl) ValidateImageFile(fileName string, fileSize int64, contentType string) error {
	// 1. 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(fileName))
	allowedExts := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
		".bmp":  true,
		".webp": true,
	}

	if !allowedExts[ext] {
		return fmt.Errorf("不支持的图片格式，支持的格式：jpg, jpeg, png, gif, bmp, webp")
	}

	// 2. 检查MIME类型
	allowedTypes := map[string]bool{
		"image/jpeg": true,
		"image/jpg":  true,
		"image/png":  true,
		"image/gif":  true,
		"image/bmp":  true,
		"image/webp": true,
	}

	if !allowedTypes[contentType] {
		return fmt.Errorf("不支持的文件类型：%s", contentType)
	}

	// 3. 检查文件大小 (限制为10MB)
	const maxFileSize = 10 * 1024 * 1024 // 10MB
	if fileSize > maxFileSize {
		return fmt.Errorf("文件大小超出限制，最大支持10MB，当前文件大小：%s", 
			s.formatFileSize(fileSize))
	}

	if fileSize <= 0 {
		return errors.New("文件大小无效")
	}

	return nil
}

// generateObjectKey 生成对象键名
func (s *FileUploadServiceImpl) generateObjectKey(originalFileName, businessType string) string {
	// 获取文件扩展名
	ext := filepath.Ext(originalFileName)
	
	// 生成唯一ID
	uniqueID := uuid.New().String()
	
	// 生成日期路径 (年/月/日)
	now := time.Now()
	datePath := now.Format("2006/01/02")
	
	// 根据业务类型构建路径
	var basePath string
	switch businessType {
	case "compensation_proof":
		basePath = "compensation/proofs"
	case "adjustment_proof":
		basePath = "adjustments/proofs"
	default:
		basePath = "uploads"
	}
	
	// 构建完整的object key: business_type/date/uuid.ext
	objectKey := fmt.Sprintf("%s/%s/%s%s", basePath, datePath, uniqueID, ext)
	
	return objectKey
}

// extractObjectKeyFromURL 从URL中提取object key
func (s *FileUploadServiceImpl) extractObjectKeyFromURL(fileUrl string) string {
	// 移除基础URL部分，获取object key
	basePrefix := s.baseURL + "/"
	if strings.HasPrefix(fileUrl, basePrefix) {
		return strings.TrimPrefix(fileUrl, basePrefix)
	}
	
	// 尝试其他可能的URL格式
	if strings.Contains(fileUrl, s.bucketName) {
		parts := strings.Split(fileUrl, "/")
		for i, part := range parts {
			if part == s.bucketName && i+1 < len(parts) {
				return strings.Join(parts[i+1:], "/")
			}
		}
	}
	
	return ""
}

// formatFileSize 格式化文件大小
func (s *FileUploadServiceImpl) formatFileSize(size int64) string {
	const (
		KB = 1024
		MB = KB * 1024
		GB = MB * 1024
	)

	switch {
	case size >= GB:
		return fmt.Sprintf("%.2fGB", float64(size)/GB)
	case size >= MB:
		return fmt.Sprintf("%.2fMB", float64(size)/MB)
	case size >= KB:
		return fmt.Sprintf("%.2fKB", float64(size)/KB)
	default:
		return fmt.Sprintf("%dB", size)
	}
} 