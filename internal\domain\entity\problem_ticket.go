package entity

import "zebra-hub-system/internal/util"

// ProblemTicket 问题运单工单实体
type ProblemTicket struct {
	ID                int64                     `json:"id"`                   // 工单主键ID
	ManifestID        int64                     `json:"manifestId"`           // 关联的运单ID
	TrackingNumber    string                    `json:"trackingNumber"`       // 关联运单的物流单号
	CustomerAccountID int64                     `json:"customerAccountId"`    // 关联运单所属的客户ID
	ProblemTypeCode   string                    `json:"problemTypeCode"`      // 问题类型代码
	ProblemDescription string                   `json:"problemDescription"`   // 问题描述
	Status           string                     `json:"status"`               // 当前工单处理状态
	Priority         int                        `json:"priority"`             // 问题优先级
	AssignedToUserID int64                      `json:"assignedToUserId"`     // 处理人ID
	Remarks          string                     `json:"remarks"`              // 处理备注
	CreateTime       util.PointerFormattedTime  `json:"createTime"`           // 工单创建时间
	UpdateTime       util.PointerFormattedTime  `json:"updateTime"`           // 工单最后更新时间
	ResolvedTime     util.PointerFormattedTime  `json:"resolvedTime"`         // 解决时间
	
	// 关联信息(非数据库字段)
	ManifestInfo      *Manifest                 `json:"manifestInfo,omitempty"` // 运单信息
	CustomerInfo      *User                     `json:"customerInfo,omitempty"` // 客户信息
	AssignedUserInfo  *User                     `json:"assignedUserInfo,omitempty"` // 处理人信息
} 