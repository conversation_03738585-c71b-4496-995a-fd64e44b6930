# 智能模板检查功能说明

## 功能概述

在账单生成过程中，系统现在支持智能模板检查功能。与之前简单地检查用户是否配置了任何模板不同，新的逻辑会**先分析运单实际需要哪些类型的模板，然后只检查缺少的必需模板**。

## 业务场景

### 原有逻辑的问题

之前的逻辑是：如果用户没有配置**任何**运费模板（普通、带电、投函模板都为空），就将账单标记为异常状态。

这会导致以下问题：

- 用户只配置了普通模板，没有配置带电模板
- 但是他的运单**全部都是普通货物**，没有带电货物
- 账单仍然会被标记为异常状态，影响正常的业务流程

### 新逻辑的优势

新的智能检查逻辑：

1. **分析运单需求**：扫描所有运单，确定实际需要哪些类型的模板
2. **精确检查**：只检查实际需要但缺失的模板类型
3. **生成具体提示**：在异常信息中明确指出缺少哪些具体的模板

## 技术实现

### 核心方法

#### 1. `analyzeRequiredTemplateTypes`

分析运单集合中实际需要的模板类型：

```go
func (s *BillingServiceImpl) analyzeRequiredTemplateTypes(manifests []*entity.Manifest) []int {
    requiredTemplateTypes := make(map[int]bool)

    for _, manifest := range manifests {
        // 根据运单的运费模板类型判断需要哪种模板
        templateType := manifest.ShippingFeeTemplateType
        if templateType == 0 {
            templateType = 1 // 默认为普通货物
        }
        requiredTemplateTypes[templateType] = true
    }

    // 转换为切片并排序
    templateTypes := make([]int, 0, len(requiredTemplateTypes))
    for templateType := range requiredTemplateTypes {
        templateTypes = append(templateTypes, templateType)
    }

    sort.Ints(templateTypes)
    return templateTypes
}
```

#### 2. `checkMissingTemplates`

检查缺少的必需模板：

```go
func (s *BillingServiceImpl) checkMissingTemplates(templates *entity.AppliedFreightTemplate, requiredTypes []int) []string {
    missingTemplates := make([]string, 0)

    for _, templateType := range requiredTypes {
        switch templateType {
        case 1:
            if templates.GeneralTemplate == nil {
                missingTemplates = append(missingTemplates, "普货模板")
            }
        case 2:
            if templates.BatteryTemplate == nil {
                missingTemplates = append(missingTemplates, "带电模板")
            }
        case 3:
            if templates.PostBoxTemplate == nil {
                missingTemplates = append(missingTemplates, "投函模板")
            }
        }
    }

    return missingTemplates
}
```

### 应用场景示例

#### 场景 1：正常情况

- **用户配置**：只有普通模板
- **运单类型**：全部为普通货物（ShippingFeeTemplateType = 1）
- **结果**：账单正常生成，不会标记为异常

#### 场景 2：缺少必需模板

- **用户配置**：只有普通模板
- **运单类型**：既有普通货物也有带电货物（ShippingFeeTemplateType = 1, 2）
- **结果**：账单标记为异常，notes 字段显示"用户缺少必需的运费模板：带电模板"

#### 场景 3：完整配置

- **用户配置**：普通模板、带电模板、投函模板都有
- **运单类型**：包含所有类型的货物
- **结果**：账单正常生成

## 模板类型映射

| 类型值 | 模板名称 | 货物说明 |
| ------ | -------- | -------- |
| 1      | 普货模板 | 普通货物 |
| 2      | 带电模板 | 带电货物 |
| 3      | 投函模板 | 投函货物 |

## 错误信息格式

当检测到缺少必需模板时，系统会在账单的 notes 字段中记录具体信息：

```
用户缺少必需的运费模板：带电模板、投函模板
```

## 日志记录

系统会记录详细的检查过程：

```go
logger.Warn("用户缺少必需的运费模板，将创建异常状态账单",
    zap.Int64("userId", req.UserID),
    zap.Strings("missingTemplates", missingTemplates),
    zap.Ints("requiredTypes", requiredTemplateTypes))
```

## 向后兼容性

此更新完全向后兼容：

- 现有的 API 接口无变化
- 数据库模式无变化
- 只是优化了内部的检查逻辑

## 受影响的模块

1. **同步账单生成**：`BillingServiceImpl.GenerateBilling`
2. **异步账单生成**：`BillingTaskProcessor.processCustomerBilling`
3. **账单状态**：新增`BillingStatusError`状态常量

## 测试覆盖

已添加单元测试覆盖以下场景：

- 只有普通货物的情况
- 混合货物类型的情况
- 默认模板类型的处理
- 各种模板配置组合的检查

这个优化使得账单生成更加智能和用户友好，减少了不必要的异常状态，提高了系统的实用性。
