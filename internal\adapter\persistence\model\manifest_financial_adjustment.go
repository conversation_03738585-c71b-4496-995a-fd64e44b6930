package model

import (
	"encoding/json"
	"fmt"
	"time"
	"zebra-hub-system/internal/domain/entity"
)

// ManifestFinancialAdjustmentPO 运单财务调整记录持久化对象
type ManifestFinancialAdjustmentPO struct {
	ID                int64           `gorm:"column:id;primaryKey;autoIncrement"`
	ManifestID        int64           `gorm:"column:manifest_id;not null;index:idx_adj_manifest_id"`
	AdjustmentType    string          `gorm:"column:adjustment_type;type:varchar(50);not null;index:idx_adj_type"`
	Description       string          `gorm:"column:description;type:text"`
	AdditionalDetails []byte          `gorm:"column:additional_details;type:json"`
	Amount            float64         `gorm:"column:amount;type:decimal(19,2);not null"`
	Currency          string          `gorm:"column:currency;type:varchar(10);not null"`
	EffectiveDate     time.Time       `gorm:"column:effective_date;type:date;not null;index:idx_adj_effective_date"`
	CustomerAccountID int64           `gorm:"column:customer_account_id;not null;index:idx_adj_customer_id"`
	IsVoid            bool            `gorm:"column:is_void;type:tinyint(1);not null;default:0;index:idx_adj_is_void"`
	VoidReason        string          `gorm:"column:void_reason;type:text"`
	VoidedBy          *int64          `gorm:"column:voided_by;index"`
	VoidedTime        *time.Time      `gorm:"column:voided_time;index"`
	CreatorID         *int64          `gorm:"column:creator_id;index"`
	CreateTime        time.Time       `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP"`
	UpdateTime        time.Time       `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"`
}

// TableName 表名
func (ManifestFinancialAdjustmentPO) TableName() string {
	return "manifest_financial_adjustments"
}

// ToEntity 转换为实体
func (po *ManifestFinancialAdjustmentPO) ToEntity() (*entity.ManifestFinancialAdjustment, error) {
	var additionalDetails *entity.AdditionalDetails
	if len(po.AdditionalDetails) > 0 {
		details := make(entity.AdditionalDetails)
		if err := json.Unmarshal(po.AdditionalDetails, &details); err != nil {
			return nil, fmt.Errorf("failed to unmarshal additional details: %w", err)
		}
		additionalDetails = &details
	}

	return &entity.ManifestFinancialAdjustment{
		ID:                po.ID,
		ManifestID:        po.ManifestID,
		AdjustmentType:    po.AdjustmentType,
		Description:       po.Description,
		AdditionalDetails: additionalDetails,
		Amount:            po.Amount,
		Currency:          po.Currency,
		EffectiveDate:     po.EffectiveDate,
		CustomerAccountID: po.CustomerAccountID,
		IsVoid:            po.IsVoid,
		VoidReason:        po.VoidReason,
		VoidedBy:          po.VoidedBy,
		VoidedTime:        po.VoidedTime,
		CreatorID:         po.CreatorID,
		CreateTime:        po.CreateTime,
		UpdateTime:        po.UpdateTime,
	}, nil
}

// FromEntity 从实体转换为持久化对象
func (po *ManifestFinancialAdjustmentPO) FromEntity(entity *entity.ManifestFinancialAdjustment) error {
	var additionalDetails []byte
	if entity.AdditionalDetails != nil {
		data, err := json.Marshal(entity.AdditionalDetails)
		if err != nil {
			return fmt.Errorf("failed to marshal additional details: %w", err)
		}
		additionalDetails = data
	}

	po.ID = entity.ID
	po.ManifestID = entity.ManifestID
	po.AdjustmentType = entity.AdjustmentType
	po.Description = entity.Description
	po.AdditionalDetails = additionalDetails
	po.Amount = entity.Amount
	po.Currency = entity.Currency
	po.EffectiveDate = entity.EffectiveDate
	po.CustomerAccountID = entity.CustomerAccountID
	po.IsVoid = entity.IsVoid
	po.VoidReason = entity.VoidReason
	po.VoidedBy = entity.VoidedBy
	po.VoidedTime = entity.VoidedTime
	po.CreatorID = entity.CreatorID
	// CreateTime 和 UpdateTime 由数据库自动设置
	return nil
}
