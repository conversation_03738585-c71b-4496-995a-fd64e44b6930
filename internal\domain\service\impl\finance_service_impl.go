package impl

import (
	"archive/zip"
	"bytes"
	"context"
	"fmt"
	"io"
	"math"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	_ "image/gif"  // 支持 GIF 格式
	_ "image/jpeg" // 支持 JPEG 格式
	_ "image/png"  // 支持 PNG 格式

	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"

	appservice "zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/dto"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/service"
)

// FinanceServiceImpl 财务服务实现
type FinanceServiceImpl struct {
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository
	manifestRepo            repository.ManifestRepository
	userRepo                repository.UserRepository
	billingService          appservice.BillingService
}

// NewFinanceService 创建财务服务
func NewFinanceService(
	manifestRepo repository.ManifestRepository,
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository,
	userRepo repository.UserRepository,
	billingService appservice.BillingService,
) service.FinanceService {
	return &FinanceServiceImpl{
		manifestRepo:            manifestRepo,
		shippingFeeTemplateRepo: shippingFeeTemplateRepo,
		userRepo:                userRepo,
		billingService:          billingService,
	}
}

// GenerateShippingBillExcel 生成运费账单Excel
func (s *FinanceServiceImpl) GenerateShippingBillExcel(ctx context.Context, req dto.ShippingBillExportRequest) ([]byte, string, error) {
	// 确保时间是UTC时区
	startTimeUTC := req.StartTime.UTC()
	endTimeUTC := req.EndTime.UTC()

	// 1. 获取用户配置的模板ID映射
	templateMap, err := s.getTemplateIDMap(ctx, req)
	if err != nil {
		return nil, "", fmt.Errorf("获取用户运费模板失败: %w", err)
	}

	// 2. 获取运费模板详情
	templateDetails := make(map[int]entity.ShippingFeeTemplate)
	for templateType, templateID := range templateMap {
		template, err := s.shippingFeeTemplateRepo.GetTemplateByID(ctx, templateID)
		if err != nil {
			return nil, "", fmt.Errorf("获取运费模板详情失败 (类型=%d): %w", templateType, err)
		}
		templateDetails[templateType] = template
	}

	// 3. 根据时间筛选类型查询指定时间范围内的运单
	var manifests []entity.Manifest
	var filterTypeDesc string

	if req.TimeFilterType == 2 {
		// 按预报时间查询
		manifests, err = s.manifestRepo.GetUserManifestsByCreateTimeRange(ctx, req.UserID, startTimeUTC, endTimeUTC)
		filterTypeDesc = "预报时间"
	} else {
		// 默认按发货时间查询
		manifests, err = s.manifestRepo.GetUserManifestsByTimeRange(ctx, req.UserID, startTimeUTC, endTimeUTC)
		filterTypeDesc = "发货时间"
	}

	if err != nil {
		return nil, "", fmt.Errorf("查询运单数据失败: %w", err)
	}

	// 4. 计算每个运单的费用
	billItems := make([]dto.ShippingBillItem, 0, len(manifests))
	var totalAmount float64 = 0

	for _, manifest := range manifests {
		// 根据运单的运费模板类型选择对应的模板
		templateType := manifest.ShippingFeeTemplateType
		if templateType == 0 {
			// 如果没有设置，默认使用普通模板(类型1)
			templateType = 1
		}

		template, exists := templateDetails[templateType]
		if !exists {
			// 如果找不到对应类型的模板，尝试使用默认模板(类型1)
			template, exists = templateDetails[1]
			if !exists {
				// 如果还是找不到，使用第一个可用的模板
				for _, t := range templateDetails {
					template = t
					exists = true
					break
				}
			}
		}

		if !exists {
			// 如果仍然找不到模板，跳过此运单
			continue
		}

		// 计算费用
		billItem := calculateManifestFee(manifest, template)
		billItems = append(billItems, billItem)
		totalAmount += billItem.TotalPrice
	}

	// 获取用户昵称，用于报表标题和文件名
	users, err := s.userRepo.FindByIDs(ctx, []int64{req.UserID})
	if err != nil {
		return nil, "", fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 设置用户昵称，如果未找到用户或昵称为空，则使用默认值
	userNickname := "客户" // 默认昵称
	if len(users) > 0 && users[0].Nickname != "" {
		userNickname = users[0].Nickname
	}

	// 5. 生成Excel文件
	excelData, err := s.generateExcel(billItems, templateDetails, req, totalAmount, userNickname)
	if err != nil {
		return nil, "", fmt.Errorf("生成Excel文件失败: %w", err)
	}

	// 文件名中包含筛选类型信息
	fileName := fmt.Sprintf("%s_%s运费账单_%s_%s.xlsx",
		userNickname,
		filterTypeDesc, // 添加筛选类型说明
		startTimeUTC.Format("20060102"),
		endTimeUTC.Format("20060102"))

	return excelData, fileName, nil
}

// getTemplateIDMap 获取用户的模板ID映射表
func (s *FinanceServiceImpl) getTemplateIDMap(ctx context.Context, req dto.ShippingBillExportRequest) (map[int]int64, error) {
	// 创建模板类型->模板ID的映射
	templateMap := make(map[int]int64)

	// 处理普通模板(类型1)
	if req.NormalTemplateID != nil && *req.NormalTemplateID > 0 {
		// 使用请求中指定的模板ID
		templateMap[1] = *req.NormalTemplateID
	}

	// 处理带电模板(类型2)
	if req.ElectronicTemplateID != nil && *req.ElectronicTemplateID > 0 {
		templateMap[2] = *req.ElectronicTemplateID
	}

	// 处理投函模板(类型3)
	if req.MailboxTemplateID != nil && *req.MailboxTemplateID > 0 {
		templateMap[3] = *req.MailboxTemplateID
	}

	// 如果没有指定任何模板，尝试获取用户的默认模板
	if len(templateMap) == 0 {
		defaultTemplateID, err := s.shippingFeeTemplateRepo.GetDefaultUserTemplateID(ctx, req.UserID)
		if err == nil && defaultTemplateID > 0 {
			templateMap[1] = defaultTemplateID // 作为普通模板使用
		}
	}

	// 验证是否至少有一个可用的模板
	if len(templateMap) == 0 {
		return nil, fmt.Errorf("用户未配置任何运费模板，也未在请求中指定模板")
	}

	return templateMap, nil
}

// calculateManifestFee 计算运单费用
func calculateManifestFee(manifest entity.Manifest, template entity.ShippingFeeTemplate) dto.ShippingBillItem {
	// 计算三边和
	threeSidesSum := manifest.Length + manifest.Width + manifest.Height

	// 计算体积重
	var volumeWeight float64 = 0
	if threeSidesSum > template.ThreeSidesStart {
		// 体积重 = 长*宽*高 / 轻抛系数
		volumeWeight = (manifest.Length * manifest.Width * manifest.Height) / float64(template.BulkCoefficient)
		// 保留两位小数
		volumeWeight = math.Round(volumeWeight*1000) / 1000
	}

	// 确定计费重量 (实重与体积重取大值)
	chargeableWeight := manifest.Weight
	if volumeWeight > manifest.Weight {
		chargeableWeight = volumeWeight
	}

	// 计算费用
	var firstWeightPrice, continuedPrice float64

	// 首重费用
	firstWeightPrice = template.FirstWeightPrice

	// 续重费用
	if chargeableWeight > template.FirstWeightRange {
		// 超出首重的部分
		exceededWeight := chargeableWeight - template.FirstWeightRange
		// 计算需要几个续重区间，向上取整
		continuedIntervals := math.Ceil(exceededWeight / template.ContinuedWeightInterval)
		continuedPrice = continuedIntervals * template.ContinuedWeightPrice
	}

	// 从运单获取额外费用
	overLengthSurcharge := manifest.OverLengthSurcharge
	remoteAreaSurcharge := manifest.RemoteAreaSurcharge
	otherCost := manifest.OtherCost
	otherCostName := manifest.OtherCostName

	// 总费用
	totalPrice := firstWeightPrice + continuedPrice + overLengthSurcharge + remoteAreaSurcharge + otherCost

	// 获取物品名称列表
	var itemNames string
	if len(manifest.Items) > 0 {
		var names []string
		for _, item := range manifest.Items {
			names = append(names, item.Name)
		}
		itemNames = strings.Join(names, ", ")
	}

	// 物流费用精确到小数点后两位
	firstWeightPrice = math.Round(firstWeightPrice*100) / 100
	continuedPrice = math.Round(continuedPrice*100) / 100
	totalPrice = math.Round(totalPrice*100) / 100

	// 确保运费模板类型有值
	templateType := manifest.ShippingFeeTemplateType
	if templateType == 0 {
		templateType = 1 // 默认为普通货物
	}

	// 获取发货时间，如果为空则使用当前时间
	var shipmentTime time.Time
	if manifest.ShipmentTime.Time != nil && !manifest.ShipmentTime.IsZero() {
		shipmentTime = *manifest.ShipmentTime.Time
	} else {
		shipmentTime = time.Now()
	}

	return dto.ShippingBillItem{
		ManifestID:                manifest.ID,
		ExpressNumber:             manifest.ExpressNumber,
		OrderNo:                   manifest.OrderNo,
		TransferredTrackingNumber: manifest.TransferredTrackingNumber,
		ShippedTime:               shipmentTime,
		CreateTime:                manifest.CreateTime, // 添加预报时间字段
		ReceiverName:              manifest.ReceiverName,
		ItemNames:                 itemNames,
		CargoType:                 templateType, // 货物类型与模板类型相同
		Weight:                    manifest.Weight,
		Length:                    manifest.Length,
		Width:                     manifest.Width,
		Height:                    manifest.Height,
		ThreeSidesSum:             threeSidesSum,
		VolumeWeight:              volumeWeight,
		ChargeableWeight:          chargeableWeight,
		FirstWeightPrice:          firstWeightPrice,
		ContinuedPrice:            continuedPrice,
		OverLengthSurcharge:       overLengthSurcharge,
		RemoteAreaSurcharge:       remoteAreaSurcharge,
		OtherCost:                 otherCost,
		OtherCostName:             otherCostName,
		TotalPrice:                totalPrice,
		ShippingFeeType:           templateType,
	}
}

// getCargoTypeName 获取货物类型名称
func getCargoTypeName(cargoType int) string {
	switch cargoType {
	case 1:
		return "普通货物"
	case 2:
		return "带电货物"
	case 3:
		return "投函货物"
	default:
		return fmt.Sprintf("未知类型(%d)", cargoType)
	}
}

// getTemplateTypeName 获取模板类型名称
func getTemplateTypeName(templateType int) string {
	switch templateType {
	case 1:
		return "普通货物"
	case 2:
		return "带电货物"
	case 3:
		return "投函货物"
	default:
		return fmt.Sprintf("未知类型(%d)", templateType)
	}
}

// generateExcel 生成Excel文件
func (s *FinanceServiceImpl) generateExcel(
	billItems []dto.ShippingBillItem,
	templateDetails map[int]entity.ShippingFeeTemplate,
	req dto.ShippingBillExportRequest,
	totalAmount float64,
	userNickname string,
) ([]byte, error) {
	// 确保使用UTC时区
	startTimeUTC := req.StartTime.UTC()
	endTimeUTC := req.EndTime.UTC()

	// 确定时间筛选类型文本
	var filterTypeDesc string
	if req.TimeFilterType == 2 {
		filterTypeDesc = "预报时间"
	} else {
		filterTypeDesc = "发货时间"
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer f.Close()

	// 设置表头
	sheetName := "运费账单"
	f.SetSheetName("Sheet1", sheetName)

	// 设置标题样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 14,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建标题样式失败: %w", err)
	}

	// 设置副标题样式
	subtitleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#F2F2F2"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建副标题样式失败: %w", err)
	}

	// 设置内容样式
	contentStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建内容样式失败: %w", err)
	}

	// 设置金额样式（保留2位小数并添加¥符号）
	numFmt := "¥#,##0.00"
	amountStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		CustomNumFmt: &numFmt,
	})
	if err != nil {
		return nil, fmt.Errorf("创建金额样式失败: %w", err)
	}

	// 设置普通数值样式（保留2位小数，不带货币符号）
	weightNumFmt := "#,##0.00"
	weightNumberStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		CustomNumFmt: &weightNumFmt,
	})
	if err != nil {
		return nil, fmt.Errorf("创建数值样式失败: %w", err)
	}

	// 设置文本样式（允许文本换行）
	textStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建文本样式失败: %w", err)
	}

	// 设置表头样式（居中、加粗）
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 11,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 2}, // 双线
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建表头样式失败: %w", err)
	}

	// 设置总计行样式
	totalRowStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 11,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E7E6E6"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 2}, // 双线
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建总计行样式失败: %w", err)
	}

	// 设置总计金额样式
	totalAmountStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:  true,
			Size:  11,
			Color: "#FF0000", // 红色
		},
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E7E6E6"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 2}, // 双线
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		CustomNumFmt: &numFmt,
	})
	if err != nil {
		return nil, fmt.Errorf("创建总计金额样式失败: %w", err)
	}

	// 简化表头占用空间 - 合并公司名称和报表标题
	currentRow := 1
	f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "V"+fmt.Sprint(currentRow))
	f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), fmt.Sprintf("斑马物巢物流有限公司 - %s运费账单报表", userNickname))
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "V"+fmt.Sprint(currentRow), titleStyle)
	currentRow++

	// 添加报表期间，并附加货币单位信息
	f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "V"+fmt.Sprint(currentRow))
	timeRange := fmt.Sprintf("报表期间：%s 至 %s（%s筛选）| 金额单位：人民币元",
		startTimeUTC.Format("2006-01-02"),
		endTimeUTC.Format("2006-01-02"),
		filterTypeDesc)
	f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), timeRange)
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "V"+fmt.Sprint(currentRow), subtitleStyle)
	currentRow++

	// 简化模板信息显示 - 直接显示所有模板，不添加额外标题行
	templateInfos := []string{}

	// 定义模板类型顺序和名称
	templateTypes := []int{1, 2, 3}
	templateTypeNames := map[int]string{
		1: "普通货物",
		2: "带电货物",
		3: "投函货物",
	}

	// 收集所有模板信息
	for _, templateType := range templateTypes {
		template, exists := templateDetails[templateType]
		if exists {
			templateInfo := fmt.Sprintf("%s模板: %s [首重%.2f元/%.2f公斤, 续重%.2f元/%.2f公斤]",
				templateTypeNames[templateType],
				template.Name,
				template.FirstWeightPrice,
				template.FirstWeightRange,
				template.ContinuedWeightPrice,
				template.ContinuedWeightInterval)
			templateInfos = append(templateInfos, templateInfo)
		}
	}

	// 显示模板信息（一行显示所有模板）
	if len(templateInfos) > 0 {
		f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow)) // 扩展到AA列
		f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), strings.Join(templateInfos, " | "))
		f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow), contentStyle)
		currentRow++
	}

	// 设置表头
	headerRow := currentRow
	headers := []string{
		"序号", "快递单号", "系统单号", "转单号", "发货时间", "预报时间", "收件人", "物品名称", "货物类型",
		"重量(kg)", "长(cm)", "宽(cm)", "高(cm)",
		"三边和(cm)", "体积重(kg)", "计费重量(kg)",
		"首重费用(元)", "续重费用(元)", "超长费(元)", "偏远费(元)", "超重费(元)", "总费用(元)",
	}

	for i, header := range headers {
		col := string(rune('A' + i))
		cell := fmt.Sprintf("%s%d", col, headerRow)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// 填充数据
	for i, item := range billItems {
		row := headerRow + 1 + i

		// 转换时间到UTC时区
		shippedTimeUTC := item.ShippedTime.UTC()
		createTimeUTC := item.CreateTime.UTC()

		// 先设置单元格值
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), i+1)                                          // 序号
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.ExpressNumber)                           // 快递单号
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.OrderNo)                                 // 系统单号
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.TransferredTrackingNumber)               // 转单号
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), shippedTimeUTC.Format("2006-01-02 15:04:05")) // 发货时间 (UTC)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), createTimeUTC.Format("2006-01-02 15:04:05"))  // 预报时间 (UTC)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.ReceiverName)                            // 收件人
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.ItemNames)                               // 物品名称
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), getCargoTypeName(item.CargoType))             // 货物类型
		setCellNumberValue(f, sheetName, fmt.Sprintf("J%d", row), item.Weight)                           // 重量(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("K%d", row), item.Length)                           // 长(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("L%d", row), item.Width)                            // 宽(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("M%d", row), item.Height)                           // 高(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("N%d", row), item.ThreeSidesSum)                    // 三边和(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("O%d", row), item.VolumeWeight)                     // 体积重(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("P%d", row), item.ChargeableWeight)                 // 计费重量(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("Q%d", row), item.FirstWeightPrice)                 // 首重费用(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("R%d", row), item.ContinuedPrice)                   // 续重费用(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("S%d", row), item.OverLengthSurcharge)              // 超长费(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("T%d", row), item.RemoteAreaSurcharge)              // 偏远费(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("U%d", row), item.OtherCost)                        // 超重费(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("V%d", row), item.TotalPrice)                       // 总费用(如果为0则不显示)

		// 设置奇偶行不同背景色
		var rowBaseStyle int
		var rowBaseErr error
		if i%2 == 0 { // 偶数行保持白色背景
			rowBaseStyle = contentStyle
		} else { // 奇数行使用浅灰色背景
			rowBaseStyle, rowBaseErr = f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#F9F9F9"}, // 更浅的灰色
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
			})
		}

		// 为非特殊单元格应用基础样式
		for _, col := range []string{"A", "I"} {
			if i%2 == 1 && rowBaseErr == nil { // 奇数行使用浅灰色背景
				f.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, row), fmt.Sprintf("%s%d", col, row), rowBaseStyle)
			} else { // 偶数行使用默认样式
				f.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, row), fmt.Sprintf("%s%d", col, row), contentStyle)
			}
		}

		// 为文本单元格应用左对齐样式
		textCellStyle := textStyle
		if i%2 == 1 && rowBaseErr == nil { // 奇数行使用带背景色的样式
			textCellStyle, _ = f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#F9F9F9"}, // 更浅的灰色
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "left",
					Vertical:   "center",
					WrapText:   true,
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
			})
		}

		// 应用文本样式到文本单元格
		for _, col := range []string{"B", "C", "D", "E", "F", "G", "H"} {
			f.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, row), fmt.Sprintf("%s%d", col, row), textCellStyle)
		}

		// 应用数值样式到数值单元格
		numberCellStyle := weightNumberStyle
		if i%2 == 1 && rowBaseErr == nil { // 奇数行使用带背景色的样式
			numberCellStyle, _ = f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#F9F9F9"}, // 更浅的灰色
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "right",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
				CustomNumFmt: &weightNumFmt,
			})
		}

		// 为数值列应用右对齐的数值样式
		for _, col := range []string{"J", "K", "L", "M", "N", "O", "P"} {
			f.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, row), fmt.Sprintf("%s%d", col, row), numberCellStyle)
		}

		// 为金额列应用金额样式
		moneyCellStyle := amountStyle
		if i%2 == 1 && rowBaseErr == nil { // 奇数行使用带背景色的样式
			moneyCellStyle, _ = f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#F9F9F9"}, // 更浅的灰色
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "right",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
				CustomNumFmt: &numFmt,
			})
		}

		// 应用金额样式到金额列
		for _, col := range []string{"Q", "R", "S", "T", "U", "V"} {
			f.SetCellStyle(sheetName, fmt.Sprintf("%s%d", col, row), fmt.Sprintf("%s%d", col, row), moneyCellStyle)
		}

		// 为货物类型设置特殊样式
		switch item.CargoType {
		case 1: // 普通货物：淡橙色
			cargoTypeStyle, styleErr := f.NewStyle(&excelize.Style{
				Font: &excelize.Font{
					Bold: true,
				},
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#FFE4C4"}, // 淡橙色 (Bisque)
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
			})
			if styleErr == nil {
				f.SetCellStyle(sheetName, fmt.Sprintf("I%d", row), fmt.Sprintf("I%d", row), cargoTypeStyle)
			}
		case 2: // 带电货物：淡红色
			cargoTypeStyle, styleErr := f.NewStyle(&excelize.Style{
				Font: &excelize.Font{
					Bold: true,
				},
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#FFD1DC"}, // 淡红色 (Pink)
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
			})
			if styleErr == nil {
				f.SetCellStyle(sheetName, fmt.Sprintf("I%d", row), fmt.Sprintf("I%d", row), cargoTypeStyle)
			}
		case 3: // 投函货物：淡绿色
			cargoTypeStyle, styleErr := f.NewStyle(&excelize.Style{
				Font: &excelize.Font{
					Bold: true,
				},
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#CCFFCC"}, // 淡绿色 (Light Green)
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "center",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
			})
			if styleErr == nil {
				f.SetCellStyle(sheetName, fmt.Sprintf("I%d", row), fmt.Sprintf("I%d", row), cargoTypeStyle)
			}
		}

		// 转单号不为空标为橙色
		if item.TransferredTrackingNumber != "" {
			transferredNumberStyle, styleErr := f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{
					Type:    "pattern",
					Color:   []string{"#FFAC4A"}, // 橙色背景
					Pattern: 1,
				},
				Alignment: &excelize.Alignment{
					Horizontal: "left",
					Vertical:   "center",
				},
				Border: []excelize.Border{
					{Type: "left", Color: "#000000", Style: 1},
					{Type: "top", Color: "#000000", Style: 1},
					{Type: "right", Color: "#000000", Style: 1},
					{Type: "bottom", Color: "#000000", Style: 1},
				},
			})
			if styleErr == nil {
				f.SetCellStyle(sheetName, fmt.Sprintf("D%d", row), fmt.Sprintf("D%d", row), transferredNumberStyle)
			}
		}

		// 为物品名称设置较高的行高
		f.SetRowHeight(sheetName, row, 30)
	}

	// 添加合计行
	totalRow := headerRow + 1 + len(billItems)

	// 合计行在"序号"到"其他费用"列合并
	f.MergeCell(sheetName, "A"+fmt.Sprint(totalRow), "U"+fmt.Sprint(totalRow))
	f.SetCellValue(sheetName, "A"+fmt.Sprint(totalRow), "合 计")
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(totalRow), "U"+fmt.Sprint(totalRow), totalRowStyle)

	// 单独设置总金额列
	f.SetCellValue(sheetName, "V"+fmt.Sprint(totalRow), totalAmount)
	f.SetCellStyle(sheetName, "V"+fmt.Sprint(totalRow), "V"+fmt.Sprint(totalRow), totalAmountStyle)

	// 添加简短的报表生成时间（移除页码信息）
	footerRow := totalRow + 2
	f.MergeCell(sheetName, "A"+fmt.Sprint(footerRow), "V"+fmt.Sprint(footerRow))
	f.SetCellValue(sheetName, "A"+fmt.Sprint(footerRow), fmt.Sprintf("报表生成时间：%s", time.Now().Format("2006-01-02 15:04:05")))

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 8)  // 序号
	f.SetColWidth(sheetName, "B", "B", 12) // 记录类型
	f.SetColWidth(sheetName, "C", "C", 18) // 快递单号
	f.SetColWidth(sheetName, "D", "D", 18) // 系统单号
	f.SetColWidth(sheetName, "E", "E", 25) // 转单号
	f.SetColWidth(sheetName, "F", "F", 18) // 商家订单号
	f.SetColWidth(sheetName, "G", "G", 20) // 发货时间
	f.SetColWidth(sheetName, "H", "H", 20) // 预报时间
	f.SetColWidth(sheetName, "I", "I", 15) // 收件人
	f.SetColWidth(sheetName, "J", "J", 25) // 物品名称
	f.SetColWidth(sheetName, "K", "K", 12) // 货物类型
	f.SetColWidth(sheetName, "L", "R", 12) // 重量相关列
	f.SetColWidth(sheetName, "S", "W", 12) // 费用相关列
	f.SetColWidth(sheetName, "X", "X", 25) // 附加详情
	f.SetColWidth(sheetName, "Y", "Y", 15) // 金额
	f.SetColWidth(sheetName, "Z", "Z", 40) // 备注（调整为40以更好容纳图片）

	// 添加表头筛选功能 - 扩展到AF列以包含可能的图片列
	err = f.AutoFilter(sheetName, "A"+fmt.Sprint(headerRow)+":AF"+fmt.Sprint(headerRow), nil)
	if err != nil {
		return nil, fmt.Errorf("添加筛选功能失败: %w", err)
	}

	// 设置冻结窗格 - 冻结表头和前三列（序号、记录类型、快递单号）
	err = f.SetPanes(sheetName, &excelize.Panes{
		Freeze:      true,
		Split:       false,
		XSplit:      3,                             // 冻结前三列(A-C)：序号、记录类型、快递单号
		YSplit:      headerRow,                     // 冻结表头行
		TopLeftCell: "D" + fmt.Sprint(headerRow+1), // 活动区域从D列第一个数据行开始
		ActivePane:  "bottomRight",                 // 激活右下角窗格
	})
	if err != nil {
		return nil, fmt.Errorf("设置冻结窗格失败: %w", err)
	}

	// 将Excel文件写入内存
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// GenerateBillingRecordExcel 根据账单记录ID生成Excel
func (s *FinanceServiceImpl) GenerateBillingRecordExcel(ctx context.Context, req dto.BillingRecordExportRequest) ([]byte, string, error) {
	// 1. 获取账单记录详情
	billingDetailReq := &appservice.GetBillingRecordDetailRequest{
		BillingRecordID: req.BillingRecordID,
	}
	billingDetailResp, code, err := s.billingService.GetBillingRecordDetail(ctx, billingDetailReq)
	if err != nil {
		return nil, "", fmt.Errorf("获取账单记录详情失败 (错误码:%d): %w", code, err)
	}

	billingRecord := billingDetailResp.BillingRecord

	// 2. 获取账单明细列表（分页获取所有数据）
	var allBillingItems []*appservice.BillingRecordItemDTO
	page := 1
	pageSize := 1000 // 修改：从100改为1000，提高查询性能

	for {
		itemsReq := &appservice.ListBillingRecordItemsRequest{
			BillingRecordID: req.BillingRecordID,
			Page:            page,
			PageSize:        pageSize,
		}
		itemsResp, code, err := s.billingService.ListBillingRecordItems(ctx, itemsReq)
		if err != nil {
			return nil, "", fmt.Errorf("获取账单明细失败 (错误码:%d): %w", code, err)
		}

		allBillingItems = append(allBillingItems, itemsResp.List...)

		// 如果当前页数据少于pageSize，说明已经是最后一页
		if len(itemsResp.List) < pageSize {
			break
		}
		page++
	}

	// 3. 获取财务调整快照列表（分页获取所有数据）
	var allAdjustmentSnapshots []*appservice.BillingAdjustmentSnapshotDTO
	page = 1

	for {
		adjustmentReq := &appservice.ListBillingAdjustmentSnapshotsRequest{
			BillingRecordID: req.BillingRecordID,
			Page:            page,
			PageSize:        pageSize, // 同样使用1000
		}
		adjustmentResp, code, err := s.billingService.ListBillingAdjustmentSnapshots(ctx, adjustmentReq)
		if err != nil {
			return nil, "", fmt.Errorf("获取财务调整快照失败 (错误码:%d): %w", code, err)
		}

		allAdjustmentSnapshots = append(allAdjustmentSnapshots, adjustmentResp.List...)

		// 如果当前页数据少于pageSize，说明已经是最后一页
		if len(adjustmentResp.List) < pageSize {
			break
		}
		page++
	}

	// 4. 转换账单明细为ShippingBillItem格式
	billItems := s.convertBillingItemsToShippingBillItems(allBillingItems)

	// 5. 转换财务调整快照为BillingAdjustmentItem格式
	adjustmentItems := s.convertAdjustmentSnapshotsToAdjustmentItems(allAdjustmentSnapshots)

	// 6. 构建模板信息（从账单记录中获取）
	templateDetails := s.buildTemplateDetailsFromBillingRecord(billingRecord)

	// 7. 生成Excel文件
	excelData, err := s.generateBillingRecordExcel(billItems, adjustmentItems, templateDetails, billingRecord)
	if err != nil {
		return nil, "", fmt.Errorf("生成Excel文件失败: %w", err)
	}

	// 8. 生成文件名
	fileName := fmt.Sprintf("%s_账单_%s_%s.xlsx",
		billingRecord.CustomerNickname,
		billingRecord.BillNumber,
		billingRecord.BillDate)

	return excelData, fileName, nil
}

// convertBillingItemsToShippingBillItems 转换账单明细为运费账单项格式
func (s *FinanceServiceImpl) convertBillingItemsToShippingBillItems(items []*appservice.BillingRecordItemDTO) []dto.ShippingBillItem {
	billItems := make([]dto.ShippingBillItem, len(items))

	for i, item := range items {
		// 解析时间字段
		var shippedTime, createTime time.Time
		if item.ShipmentTime != nil {
			if t, err := time.ParseInLocation("2006-01-02 15:04:05", *item.ShipmentTime, time.Local); err == nil {
				shippedTime = t
			}
		}
		if item.ManifestCreateTime != nil {
			if t, err := time.ParseInLocation("2006-01-02 15:04:05", *item.ManifestCreateTime, time.Local); err == nil {
				createTime = t
			}
		}

		// 安全获取指针值
		var expressNumber, orderNo, transferredTrackingNumber, orderNumber, receiverName string
		var weight, length, width, height, sumOfSides, dimensionalWeight, chargeableWeight float64
		var firstWeightFee, continuedWeightFee, overLengthSurcharge, remoteAreaSurcharge, overweightSurcharge float64

		if item.ExpressNumber != nil {
			expressNumber = *item.ExpressNumber
		}
		if item.OrderNo != nil {
			orderNo = *item.OrderNo
		}
		if item.TransferredTrackingNumber != nil {
			transferredTrackingNumber = *item.TransferredTrackingNumber
		}
		// 新增：获取商家订单号
		if item.OrderNumber != nil {
			orderNumber = *item.OrderNumber
		}
		if item.ReceiverName != nil {
			receiverName = *item.ReceiverName
		}
		if item.Weight != nil {
			weight = *item.Weight
		}
		if item.Length != nil {
			length = *item.Length
		}
		if item.Width != nil {
			width = *item.Width
		}
		if item.Height != nil {
			height = *item.Height
		}
		if item.SumOfSides != nil {
			sumOfSides = *item.SumOfSides
		}
		if item.DimensionalWeight != nil {
			dimensionalWeight = *item.DimensionalWeight
		}
		if item.ChargeableWeight != nil {
			chargeableWeight = *item.ChargeableWeight
		}
		if item.BaseFreightFee != nil {
			// baseFreightFee = *item.BaseFreightFee // 暂时不使用基础运费字段
		}
		if item.FirstWeightFee != nil {
			firstWeightFee = *item.FirstWeightFee
		}
		if item.ContinuedWeightFee != nil {
			continuedWeightFee = *item.ContinuedWeightFee
		}
		if item.OverLengthSurcharge != nil {
			overLengthSurcharge = *item.OverLengthSurcharge
		}
		if item.RemoteAreaSurcharge != nil {
			remoteAreaSurcharge = *item.RemoteAreaSurcharge
		}
		if item.OverweightSurcharge != nil {
			overweightSurcharge = *item.OverweightSurcharge
		}

		billItems[i] = dto.ShippingBillItem{
			ManifestID:                item.ID, // 使用账单明细ID
			ExpressNumber:             expressNumber,
			OrderNo:                   orderNo,
			TransferredTrackingNumber: transferredTrackingNumber,
			OrderNumber:               orderNumber, // 新增：商家订单号
			ShippedTime:               shippedTime,
			CreateTime:                createTime,
			ReceiverName:              receiverName,
			ItemNames:                 item.ItemDescription,
			CargoType:                 item.CargoType,
			Weight:                    weight,
			Length:                    length,
			Width:                     width,
			Height:                    height,
			ThreeSidesSum:             sumOfSides,
			VolumeWeight:              dimensionalWeight,
			ChargeableWeight:          chargeableWeight,
			FirstWeightPrice:          firstWeightFee,
			ContinuedPrice:            continuedWeightFee,
			OverLengthSurcharge:       overLengthSurcharge,
			RemoteAreaSurcharge:       remoteAreaSurcharge,
			OtherCost:                 overweightSurcharge, // 超重费对应tb_manifest.other_cost
			OtherCostName:             "超重费",
			TotalPrice:                item.ItemTotalAmount,
			ShippingFeeType:           item.CargoType,
		}
	}

	return billItems
}

// convertAdjustmentSnapshotsToAdjustmentItems 转换财务调整快照为调整项格式
func (s *FinanceServiceImpl) convertAdjustmentSnapshotsToAdjustmentItems(snapshots []*appservice.BillingAdjustmentSnapshotDTO) []dto.BillingAdjustmentItem {
	adjustmentItems := make([]dto.BillingAdjustmentItem, len(snapshots))

	for i, snapshot := range snapshots {
		// 安全获取指针值
		var description, manifestExpressNumber, manifestOrderNo, manifestTransferredTrackingNumber, manifestCustomerOrderNumber string
		var manifestReceiverName, manifestItemDescription, manifestCargoType string
		var manifestCreateTime, manifestShipmentTime string
		var manifestWeight, manifestLength, manifestWidth, manifestHeight, manifestSumOfSides float64
		var manifestDimensionalWeight, manifestChargeableWeight float64
		var additionalDetails map[string]interface{}

		if snapshot.Description != nil {
			description = *snapshot.Description
		}
		if snapshot.ManifestExpressNumber != nil {
			manifestExpressNumber = *snapshot.ManifestExpressNumber
		}
		if snapshot.ManifestOrderNo != nil {
			manifestOrderNo = *snapshot.ManifestOrderNo
		}
		if snapshot.ManifestTransferredTrackingNumber != nil {
			manifestTransferredTrackingNumber = *snapshot.ManifestTransferredTrackingNumber
		}
		if snapshot.ManifestCustomerOrderNumber != nil {
			manifestCustomerOrderNumber = *snapshot.ManifestCustomerOrderNumber
		}
		if snapshot.ManifestReceiverName != nil {
			manifestReceiverName = *snapshot.ManifestReceiverName
		}
		if snapshot.ManifestItemDescription != nil {
			manifestItemDescription = *snapshot.ManifestItemDescription
		}
		if snapshot.ManifestCargoType != nil {
			manifestCargoType = *snapshot.ManifestCargoType
		}
		if snapshot.ManifestCreateTime != nil {
			manifestCreateTime = *snapshot.ManifestCreateTime
		}
		if snapshot.ManifestShipmentTime != nil {
			manifestShipmentTime = *snapshot.ManifestShipmentTime
		}
		if snapshot.ManifestWeight != nil {
			manifestWeight = *snapshot.ManifestWeight
		}
		if snapshot.ManifestLength != nil {
			manifestLength = *snapshot.ManifestLength
		}
		if snapshot.ManifestWidth != nil {
			manifestWidth = *snapshot.ManifestWidth
		}
		if snapshot.ManifestHeight != nil {
			manifestHeight = *snapshot.ManifestHeight
		}
		if snapshot.ManifestSumOfSides != nil {
			manifestSumOfSides = *snapshot.ManifestSumOfSides
		}
		if snapshot.ManifestDimensionalWeight != nil {
			manifestDimensionalWeight = *snapshot.ManifestDimensionalWeight
		}
		if snapshot.ManifestChargeableWeight != nil {
			manifestChargeableWeight = *snapshot.ManifestChargeableWeight
		}
		if snapshot.AdditionalDetails != nil {
			additionalDetails = snapshot.AdditionalDetails
		}

		adjustmentItems[i] = dto.BillingAdjustmentItem{
			AdjustmentType:                    snapshot.AdjustmentType,
			AdjustmentTypeName:                snapshot.AdjustmentTypeName,
			Description:                       description,
			Amount:                            snapshot.Amount,
			Currency:                          snapshot.Currency,
			EffectiveDate:                     snapshot.EffectiveDate,
			ManifestExpressNumber:             manifestExpressNumber,
			ManifestOrderNo:                   manifestOrderNo,
			ManifestTransferredTrackingNumber: manifestTransferredTrackingNumber,
			ManifestCustomerOrderNumber:       manifestCustomerOrderNumber,
			ManifestCreateTime:                manifestCreateTime,
			ManifestShipmentTime:              manifestShipmentTime,
			ManifestReceiverName:              manifestReceiverName,
			ManifestItemDescription:           manifestItemDescription,
			ManifestCargoType:                 manifestCargoType,
			ManifestWeight:                    manifestWeight,
			ManifestLength:                    manifestLength,
			ManifestWidth:                     manifestWidth,
			ManifestHeight:                    manifestHeight,
			ManifestSumOfSides:                manifestSumOfSides,
			ManifestDimensionalWeight:         manifestDimensionalWeight,
			ManifestChargeableWeight:          manifestChargeableWeight,
			AdditionalDetails:                 additionalDetails,
			OriginalCreateTime:                snapshot.SnapshotTime, // 使用快照时间替代原始创建时间
		}
	}

	return adjustmentItems
}

// buildTemplateDetailsFromBillingRecord 从账单记录构建模板详情
func (s *FinanceServiceImpl) buildTemplateDetailsFromBillingRecord(billingRecord *appservice.BillingRecordDetailDTO) map[int]entity.ShippingFeeTemplate {
	templateDetails := make(map[int]entity.ShippingFeeTemplate)

	if billingRecord.AppliedFreightTemplates != nil {
		// 普通模板
		if billingRecord.AppliedFreightTemplates.GeneralTemplate != nil {
			templateDetails[1] = *billingRecord.AppliedFreightTemplates.GeneralTemplate
		}
		// 带电模板
		if billingRecord.AppliedFreightTemplates.BatteryTemplate != nil {
			templateDetails[2] = *billingRecord.AppliedFreightTemplates.BatteryTemplate
		}
		// 投函模板
		if billingRecord.AppliedFreightTemplates.PostBoxTemplate != nil {
			templateDetails[3] = *billingRecord.AppliedFreightTemplates.PostBoxTemplate
		}
	}

	return templateDetails
}

// generateBillingRecordExcel 生成账单记录Excel文件
func (s *FinanceServiceImpl) generateBillingRecordExcel(
	billItems []dto.ShippingBillItem,
	adjustmentItems []dto.BillingAdjustmentItem,
	templateDetails map[int]entity.ShippingFeeTemplate,
	billingRecord *appservice.BillingRecordDetailDTO,
) ([]byte, error) {
	// 创建Excel文件
	f := excelize.NewFile()
	defer f.Close()

	// 设置表头
	sheetName := "账单明细"
	f.SetSheetName("Sheet1", sheetName)

	// 设置标题样式
	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
			Size: 14,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#DDEBF7"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建标题样式失败: %w", err)
	}

	// 设置副标题样式
	subtitleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Size: 10,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#F2F2F2"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建副标题样式失败: %w", err)
	}

	// 设置表头样式
	headerStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:  true,
			Size:  10,
			Color: "#FFFFFF",
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#4F81BD"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建表头样式失败: %w", err)
	}

	// 设置内容样式
	contentStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建内容样式失败: %w", err)
	}

	// 设置金额样式
	numFmt := "#,##0.00"
	amountStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "right",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
		CustomNumFmt: &numFmt,
	})
	if err != nil {
		return nil, fmt.Errorf("创建金额样式失败: %w", err)
	}

	// 设置运单明细行样式（浅蓝色背景）
	manifestRowStyle, err := f.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E7F3FF"}, // 浅蓝色
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建运单明细行样式失败: %w", err)
	}

	// 设置其他费用行样式（浅黄色背景）
	adjustmentRowStyle, err := f.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#FFF9E7"}, // 浅黄色
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
		},
		Border: []excelize.Border{
			{Type: "left", Color: "#000000", Style: 1},
			{Type: "top", Color: "#000000", Style: 1},
			{Type: "right", Color: "#000000", Style: 1},
			{Type: "bottom", Color: "#000000", Style: 1},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("创建其他费用行样式失败: %w", err)
	}

	// 添加标题信息
	currentRow := 1
	f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow))
	f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), fmt.Sprintf("斑马物巢物流有限公司 - %s账单明细", billingRecord.CustomerNickname))
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow), titleStyle)
	currentRow++

	// 添加账单信息
	f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow))
	accountInfo := fmt.Sprintf("账单编号：%s | 账期：%s 至 %s | 总金额：%.2f %s",
		billingRecord.BillNumber,
		billingRecord.BillingPeriodStart,
		billingRecord.BillingPeriodEnd,
		billingRecord.TotalAmount,
		billingRecord.Currency)
	f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), accountInfo)
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow), subtitleStyle)
	currentRow++

	// 显示模板信息
	templateInfos := []string{}
	templateTypeNames := map[int]string{
		1: "普通货物",
		2: "带电货物",
		3: "投函货物",
	}

	for templateType := 1; templateType <= 3; templateType++ {
		if template, exists := templateDetails[templateType]; exists {
			templateInfo := fmt.Sprintf("%s模板: %s [首重%.2f元/%.2f公斤, 续重%.2f元/%.2f公斤]",
				templateTypeNames[templateType],
				template.Name,
				template.FirstWeightPrice,
				template.FirstWeightRange,
				template.ContinuedWeightPrice,
				template.ContinuedWeightInterval)
			templateInfos = append(templateInfos, templateInfo)
		}
	}

	if len(templateInfos) > 0 {
		f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow))
		f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), strings.Join(templateInfos, " | "))
		f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "AA"+fmt.Sprint(currentRow), contentStyle)
		currentRow++
	}

	// 空行
	currentRow++

	// 统一表头（合并运单明细和其他费用明细的所有字段）
	unifiedHeaders := []string{
		"序号", "记录类型", "快递单号", "系统单号", "转单号", "商家订单号",
		"发货时间", "预报时间", "收件人", "物品名称", "货物类型",
		"重量(kg)", "长(cm)", "宽(cm)", "高(cm)", "三边和(cm)", "体积重(kg)", "计费重量(kg)",
		"首重费用(元)", "续重费用(元)", "超长费(元)", "偏远费(元)", "超重费(元)",
		"附加详情", "金额(元)", "备注",
	}

	headerRow := currentRow
	for i, header := range unifiedHeaders {
		var col string
		if i < 26 {
			col = string(rune('A' + i))
		} else {
			col = "A" + string(rune('A'+i-26))
		}
		cell := fmt.Sprintf("%s%d", col, headerRow)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}
	currentRow++

	// 填充运单明细数据
	var manifestTotal float64
	for i, item := range billItems {
		// 使用运单明细行样式
		rowStyle := manifestRowStyle
		amountRowStyle, _ := f.NewStyle(&excelize.Style{
			Fill: excelize.Fill{
				Type:    "pattern",
				Color:   []string{"#E7F3FF"}, // 浅蓝色
				Pattern: 1,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "right",
				Vertical:   "center",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "#000000", Style: 1},
				{Type: "top", Color: "#000000", Style: 1},
				{Type: "right", Color: "#000000", Style: 1},
				{Type: "bottom", Color: "#000000", Style: 1},
			},
			CustomNumFmt: &numFmt,
		})

		f.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), i+1)                                            // 序号
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", currentRow), "运单明细")                                         // 记录类型
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), item.ExpressNumber)                             // 快递单号
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", currentRow), item.OrderNo)                                   // 系统单号
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", currentRow), item.TransferredTrackingNumber)                 // 转单号
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", currentRow), item.OrderNumber)                               // 商家订单号
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", currentRow), item.ShippedTime.Format("2006-01-02 15:04:05")) // 发货时间
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", currentRow), item.CreateTime.Format("2006-01-02 15:04:05"))  // 预报时间
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", currentRow), item.ReceiverName)                              // 收件人
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), item.ItemNames)                                 // 物品名称
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), getCargoTypeName(item.CargoType))               // 货物类型
		setCellNumberValue(f, sheetName, fmt.Sprintf("L%d", currentRow), item.Weight)                             // 重量(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("M%d", currentRow), item.Length)                             // 长(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("N%d", currentRow), item.Width)                              // 宽(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("O%d", currentRow), item.Height)                             // 高(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("P%d", currentRow), item.ThreeSidesSum)                      // 三边和(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("Q%d", currentRow), item.VolumeWeight)                       // 体积重(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("R%d", currentRow), item.ChargeableWeight)                   // 计费重量(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("S%d", currentRow), item.FirstWeightPrice)                   // 首重费用(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("T%d", currentRow), item.ContinuedPrice)                     // 续重费用(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("U%d", currentRow), item.OverLengthSurcharge)                // 超长费(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("V%d", currentRow), item.RemoteAreaSurcharge)                // 偏远费(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("W%d", currentRow), item.OtherCost)                          // 超重费(如果为0则不显示)
		// X列为附加详情，运单明细留空
		setCellNumberValue(f, sheetName, fmt.Sprintf("Y%d", currentRow), item.TotalPrice) // 金额(元)(如果为0则不显示)
		// Z列为备注，运单明细留空

		// 应用样式
		for col := 'A'; col <= 'Z'; col++ {
			cell := fmt.Sprintf("%c%d", col, currentRow)
			if (col >= 'L' && col <= 'R') || (col >= 'S' && col <= 'W') || col == 'Y' { // 数值列和金额列
				f.SetCellStyle(sheetName, cell, cell, amountRowStyle)
			} else {
				f.SetCellStyle(sheetName, cell, cell, rowStyle)
			}
		}

		manifestTotal += item.TotalPrice
		currentRow++
	}

	// 填充其他费用明细数据
	var adjustmentTotal float64
	for i, item := range adjustmentItems {
		// 使用其他费用行样式
		rowStyle := adjustmentRowStyle
		amountRowStyle, _ := f.NewStyle(&excelize.Style{
			Fill: excelize.Fill{
				Type:    "pattern",
				Color:   []string{"#FFF9E7"}, // 浅黄色
				Pattern: 1,
			},
			Alignment: &excelize.Alignment{
				Horizontal: "right",
				Vertical:   "center",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "#000000", Style: 1},
				{Type: "top", Color: "#000000", Style: 1},
				{Type: "right", Color: "#000000", Style: 1},
				{Type: "bottom", Color: "#000000", Style: 1},
			},
			CustomNumFmt: &numFmt,
		})

		// 解析附加详情为中文描述
		additionalDetailsStr := s.parseAdditionalDetailsToChineseText(item.AdjustmentType, item.AdditionalDetails)

		f.SetCellValue(sheetName, fmt.Sprintf("A%d", currentRow), len(billItems)+i+1)                     // 序号
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", currentRow), item.AdjustmentTypeName)                // 记录类型
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", currentRow), item.ManifestExpressNumber)             // 快递单号
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", currentRow), item.ManifestOrderNo)                   // 系统单号
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", currentRow), item.ManifestTransferredTrackingNumber) // 转单号
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", currentRow), item.ManifestCustomerOrderNumber)       // 商家订单号
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", currentRow), item.ManifestShipmentTime)              // 发货时间
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", currentRow), item.ManifestCreateTime)                // 预报时间
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", currentRow), item.ManifestReceiverName)              // 收件人
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", currentRow), item.ManifestItemDescription)           // 物品名称
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", currentRow), item.ManifestCargoType)                 // 货物类型
		setCellNumberValue(f, sheetName, fmt.Sprintf("L%d", currentRow), item.ManifestWeight)             // 重量(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("M%d", currentRow), item.ManifestLength)             // 长(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("N%d", currentRow), item.ManifestWidth)              // 宽(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("O%d", currentRow), item.ManifestHeight)             // 高(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("P%d", currentRow), item.ManifestSumOfSides)         // 三边和(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("Q%d", currentRow), item.ManifestDimensionalWeight)  // 体积重(如果为0则不显示)
		setCellNumberValue(f, sheetName, fmt.Sprintf("R%d", currentRow), item.ManifestChargeableWeight)   // 计费重量(如果为0则不显示)
		// S-W列为运费相关字段，其他费用留空
		f.SetCellValue(sheetName, fmt.Sprintf("X%d", currentRow), additionalDetailsStr) // 附加详情
		setCellNumberValue(f, sheetName, fmt.Sprintf("Y%d", currentRow), item.Amount)   // 金额(元)(如果为0则不显示)
		f.SetCellValue(sheetName, fmt.Sprintf("Z%d", currentRow), item.Description)     // 备注

		// 如果是赔偿类调整，处理价值证明图片
		if item.AdjustmentType == "COMPENSATION" && item.AdditionalDetails != nil {
			if proofUrls, ok := item.AdditionalDetails["proof_of_value_image_urls"]; ok {
				var imageUrls []string

				// 处理不同的数据类型（[]interface{} 或 []string）
				switch urls := proofUrls.(type) {
				case []interface{}:
					for _, url := range urls {
						if strUrl, ok := url.(string); ok && strUrl != "" {
							imageUrls = append(imageUrls, strUrl)
						}
					}
				case []string:
					imageUrls = urls
				case string:
					if urls != "" {
						imageUrls = []string{urls}
					}
				}

				// 如果有图片URL，下载并嵌入到备注列及其右侧列
				if len(imageUrls) > 0 {
					// 设置图片列的宽度，每列都设为20个字符以容纳单张图片
					imageColWidth := 20.0

					// 最多显示8张图片（Z到AG列），避免表格过宽
					maxImages := 8
					imageCount := len(imageUrls)
					if imageCount > maxImages {
						imageCount = maxImages
					}

					// 设置图片所在各列的宽度
					for i := 0; i < imageCount; i++ {
						var colName string
						if i == 0 {
							colName = "Z" // 第一张图片在Z列
						} else if i <= 7 { // 最多8张图片，索引0-7
							colName = "A" + string(rune('A'+i-1)) // AA, AB, AC...
						} else {
							// 超过8张，跳过
							break
						}
						f.SetColWidth(sheetName, colName, colName, imageColWidth)
					}

					// 设置行高以容纳图片（单行高度，所有图片都在同一行）
					requiredHeight := 120.0 // 120磅高度容纳图片
					f.SetRowHeight(sheetName, currentRow, requiredHeight)

					// 计算单张图片的最佳尺寸
					// 每列宽度20字符 ≈ 160像素，减去边距后的可用空间
					cellWidth := 140.0
					cellHeight := requiredHeight * 1.33 // 磅转像素 ≈ 160像素

					// 计算图片实际显示尺寸（明显小于单元格，留出边距）
					// 图片占单元格的60-70%，留出30-40%作为边距
					imagePadding := 20.0                               // 四周各留20像素边距
					availableWidth := cellWidth - (imagePadding * 2)   // 减去左右边距
					availableHeight := cellHeight - (imagePadding * 2) // 减去上下边距

					// 图片尺寸（正方形，取较小值，确保不超出可用空间）
					imageSize := availableWidth
					if availableHeight < availableWidth {
						imageSize = availableHeight
					}

					// 进一步缩小图片，确保视觉上有明显的"内嵌"效果
					imageSize = imageSize * 0.75 // 图片再缩小25%，增强内嵌视觉效果

					// 计算图片缩放比例
					originalImageSize := 500.0 // 假设原始图片500像素
					optimalScale := imageSize / originalImageSize

					// 限制缩放范围，确保图片不会太小或太大
					if optimalScale > 0.5 {
						optimalScale = 0.5 // 最大缩放50%，确保不会占满单元格
					}
					if optimalScale < 0.1 {
						optimalScale = 0.1 // 最小缩放10%，确保图片可见
					}

					// 下载并嵌入图片到各自的列
					successCount := 0
					for imgIndex, imageUrl := range imageUrls {
						if imgIndex >= maxImages { // 最多显示8张图片
							break
						}

						// 下载图片
						imageData, ext, err := s.downloadImageFromURL(imageUrl)
						if err != nil {
							zap.L().Warn("下载价值证明图片失败",
								zap.String("url", imageUrl),
								zap.Error(err))
							continue
						}

						// 计算当前图片所在的列名
						var colName string
						if imgIndex == 0 {
							colName = "Z" // 第一张图片在Z列
						} else if imgIndex <= 7 { // 最多8张图片，索引0-7
							colName = "A" + string(rune('A'+imgIndex-1)) // AA, AB, AC...
						} else {
							// 超过8张，跳过
							break
						}

						// 计算图片在单元格中的真正居中偏移
						// 实际显示的图片尺寸
						actualImageSize := originalImageSize * optimalScale

						// 在单元格中居中对齐
						offsetX := (cellWidth - actualImageSize) / 2  // 水平居中
						offsetY := (cellHeight - actualImageSize) / 2 // 垂直居中

						// 创建图片选项 - 使用oneCell定位实现真正的嵌入单元格效果
						// oneCell模式：图片锚定到单元格，随筛选/排序移动，类似Excel右键"嵌入单元格"功能
						printObj := true
						lockAspect := true

						// 将图片嵌入到对应列 - 使用oneCell定位实现真正的嵌入单元格效果
						// 此模式确保图片与数据行保持绑定关系，在表格操作时不会丢失关联
						err = f.AddPictureFromBytes(sheetName, fmt.Sprintf("%s%d", colName, currentRow), &excelize.Picture{
							Extension: ext,
							File:      imageData,
							Format: &excelize.GraphicOptions{
								OffsetX:         int(offsetX), // 水平居中偏移
								OffsetY:         int(offsetY), // 垂直居中偏移
								ScaleX:          optimalScale, // 水平缩放比例
								ScaleY:          optimalScale, // 垂直缩放比例
								Positioning:     "oneCell",    // 单元格锚定：最接近Excel"嵌入单元格"的模式
								PrintObject:     &printObj,    // 确保图片可以被打印
								LockAspectRatio: lockAspect,   // 锁定宽高比，防止变形
							},
						})
						if err != nil {
							zap.L().Warn("嵌入价值证明图片失败",
								zap.String("url", imageUrl),
								zap.String("column", colName),
								zap.Error(err))
							continue
						}

						successCount++

						// 记录成功嵌入的图片信息
						zap.L().Info("成功嵌入价值证明图片（内嵌单元格效果）",
							zap.String("url", imageUrl),
							zap.Int("index", imgIndex+1),
							zap.String("column", colName),
							zap.Float64("offsetX", offsetX),
							zap.Float64("offsetY", offsetY),
							zap.Float64("scale", optimalScale),
							zap.Float64("actualImageSize", actualImageSize),
							zap.Float64("cellWidth", cellWidth),
							zap.Float64("cellHeight", cellHeight))
					}

					// 如果有超出显示数量的图片，在最后一个图片列添加提示
					if len(imageUrls) > successCount && successCount > 0 {
						// 在最后一张图片的下方添加文字提示
						lastColIndex := successCount - 1
						var lastColName string
						if lastColIndex == 0 {
							lastColName = "Z"
						} else {
							lastColName = "A" + string(rune('A'+lastColIndex-1))
						}

						// 在图片下方添加小字提示（不影响图片显示）
						f.SetCellValue(sheetName, fmt.Sprintf("%s%d", lastColName, currentRow), fmt.Sprintf("+%d张", len(imageUrls)-successCount))
					}
				}
			}
		} else {
			// 非赔偿类调整，设置备注内容
			if item.Description != "" {
				f.SetCellValue(sheetName, fmt.Sprintf("Z%d", currentRow), item.Description)
			}
		}

		// 应用样式
		for col := 'A'; col <= 'Z'; col++ {
			cell := fmt.Sprintf("%c%d", col, currentRow)
			if (col >= 'L' && col <= 'R') || col == 'Y' { // 数值列和金额列
				f.SetCellStyle(sheetName, cell, cell, amountRowStyle)
			} else {
				f.SetCellStyle(sheetName, cell, cell, rowStyle)
			}
		}

		adjustmentTotal += item.Amount
		currentRow++
	}

	// 空行
	currentRow++

	// 总计
	finalTotalAmount := manifestTotal + adjustmentTotal

	f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "X"+fmt.Sprint(currentRow))
	f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), "账单总计")
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "X"+fmt.Sprint(currentRow), titleStyle)
	f.SetCellValue(sheetName, "Y"+fmt.Sprint(currentRow), finalTotalAmount)
	f.SetCellStyle(sheetName, "Y"+fmt.Sprint(currentRow), "Y"+fmt.Sprint(currentRow), amountStyle)
	currentRow++

	// 空行
	currentRow++

	// 报表生成时间
	f.MergeCell(sheetName, "A"+fmt.Sprint(currentRow), "AF"+fmt.Sprint(currentRow))
	f.SetCellValue(sheetName, "A"+fmt.Sprint(currentRow), fmt.Sprintf("报表生成时间：%s", time.Now().Format("2006-01-02 15:04:05")))
	f.SetCellStyle(sheetName, "A"+fmt.Sprint(currentRow), "AF"+fmt.Sprint(currentRow), contentStyle)

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 8)  // 序号
	f.SetColWidth(sheetName, "B", "B", 12) // 记录类型
	f.SetColWidth(sheetName, "C", "C", 18) // 快递单号
	f.SetColWidth(sheetName, "D", "D", 18) // 系统单号
	f.SetColWidth(sheetName, "E", "E", 25) // 转单号
	f.SetColWidth(sheetName, "F", "F", 18) // 商家订单号
	f.SetColWidth(sheetName, "G", "G", 20) // 发货时间
	f.SetColWidth(sheetName, "H", "H", 20) // 预报时间
	f.SetColWidth(sheetName, "I", "I", 15) // 收件人
	f.SetColWidth(sheetName, "J", "J", 25) // 物品名称
	f.SetColWidth(sheetName, "K", "K", 12) // 货物类型
	f.SetColWidth(sheetName, "L", "R", 12) // 重量相关列
	f.SetColWidth(sheetName, "S", "W", 12) // 费用相关列
	f.SetColWidth(sheetName, "X", "X", 25) // 附加详情
	f.SetColWidth(sheetName, "Y", "Y", 15) // 金额
	f.SetColWidth(sheetName, "Z", "Z", 40) // 备注（调整为40以更好容纳图片）

	// 添加表头筛选功能 - 扩展到AF列以包含可能的图片列
	err = f.AutoFilter(sheetName, "A"+fmt.Sprint(headerRow)+":AF"+fmt.Sprint(headerRow), nil)
	if err != nil {
		return nil, fmt.Errorf("添加筛选功能失败: %w", err)
	}

	// 设置冻结窗格 - 冻结表头和前三列（序号、记录类型、快递单号）
	err = f.SetPanes(sheetName, &excelize.Panes{
		Freeze:      true,
		Split:       false,
		XSplit:      3,                             // 冻结前三列(A-C)：序号、记录类型、快递单号
		YSplit:      headerRow,                     // 冻结表头行
		TopLeftCell: "D" + fmt.Sprint(headerRow+1), // 活动区域从D列第一个数据行开始
		ActivePane:  "bottomRight",                 // 激活右下角窗格
	})
	if err != nil {
		return nil, fmt.Errorf("设置冻结窗格失败: %w", err)
	}

	// 将Excel文件写入内存
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// parseAdditionalDetailsToChineseText 根据调整类型解析附加详情JSON为中文描述
func (s *FinanceServiceImpl) parseAdditionalDetailsToChineseText(adjustmentType string, additionalDetails map[string]interface{}) string {
	if additionalDetails == nil || len(additionalDetails) == 0 {
		return ""
	}

	var details []string

	switch adjustmentType {
	case "COMPENSATION": // 赔偿类
		// 运费赔偿
		if isFreightDeduction, ok := additionalDetails["is_freight_deduction"].(bool); ok && isFreightDeduction {
			if amount, ok := additionalDetails["total_freight_deduction_amount"].(float64); ok {
				details = append(details, fmt.Sprintf("赔偿运费: %.2f元", amount))
			}
		}

		// 货值赔偿
		if isValueCompensation, ok := additionalDetails["is_value_compensation"].(bool); ok && isValueCompensation {
			if amount, ok := additionalDetails["total_value_compensation_amount"].(float64); ok {
				details = append(details, fmt.Sprintf("赔偿货值: %.2f元", amount))
			}
		}

		// 货值
		if cargoValue, ok := additionalDetails["cargo_value"].(float64); ok {
			details = append(details, fmt.Sprintf("货值: %.2f元", cargoValue))
		}

		// 价值证明图片
		if proofUrls, ok := additionalDetails["proof_of_value_image_urls"].([]interface{}); ok && len(proofUrls) > 0 {
			details = append(details, fmt.Sprintf("价值证明图片: %d张", len(proofUrls)))
		}

	case "REASSIGNMENT": // 改派类
		if reassignmentNumber, ok := additionalDetails["reassignmentNumber"].(string); ok && reassignmentNumber != "" {
			details = append(details, fmt.Sprintf("改派单号: %s", reassignmentNumber))
		}
		if cargoValue, ok := additionalDetails["cargoValue"].(float64); ok {
			details = append(details, fmt.Sprintf("货值: %.2f元", cargoValue))
		}

	case "DESTRUCTION": // 销毁类
		// 销毁类通常没有特殊的附加详情，可以显示通用信息
		details = append(details, "货物销毁处理")

	case "RETURN": // 退回类
		// 退回类通常没有特殊的附加详情，可以显示通用信息
		details = append(details, "货物退回处理")

	case "FEE": // 费用类
		// 可以根据具体的费用类型添加解析逻辑
		details = append(details, "附加费用")

	case "REBATE": // 返款类
		// 可以根据具体的返款类型添加解析逻辑
		details = append(details, "返款折扣")

	default: // 其他类型或未知类型
		// 对于未知类型，尝试解析通用字段
		for key, value := range additionalDetails {
			if str, ok := value.(string); ok && str != "" {
				details = append(details, fmt.Sprintf("%s: %s", key, str))
			} else if num, ok := value.(float64); ok {
				details = append(details, fmt.Sprintf("%s: %.2f", key, num))
			}
		}
	}

	if len(details) == 0 {
		return ""
	}

	return strings.Join(details, "; ")
}

// downloadImageFromURL 从URL下载图片
func (s *FinanceServiceImpl) downloadImageFromURL(url string) ([]byte, string, error) {
	// 添加超时设置，避免下载时间过长
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		return nil, "", fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, "", fmt.Errorf("下载图片失败，HTTP状态码: %d", resp.StatusCode)
	}

	// 检查文件大小，避免下载过大的文件
	const maxFileSize = 50 * 1024 * 1024 // 50MB
	if resp.ContentLength > maxFileSize {
		return nil, "", fmt.Errorf("图片文件过大: %d bytes", resp.ContentLength)
	}

	// 读取图片数据
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("读取图片数据失败: %w", err)
	}

	// 从URL中提取文件扩展名
	ext := filepath.Ext(url)
	if ext == "" {
		// 如果URL中没有扩展名，尝试从Content-Type判断
		contentType := resp.Header.Get("Content-Type")
		switch contentType {
		case "image/jpeg":
			ext = ".jpg"
		case "image/png":
			ext = ".png"
		case "image/gif":
			ext = ".gif"
		default:
			ext = ".jpg" // 默认使用jpg
		}
	}

	return imageData, ext, nil
}

// 创建一个辅助函数用于设置数值单元格，如果值为0则不显示
func setCellNumberValue(f *excelize.File, sheetName, cell string, value float64) {
	if value == 0 {
		// 当数值为0时，不设置单元格值，保持空白
		return
	}
	f.SetCellValue(sheetName, cell, value)
}

// BatchGenerateBillingRecordExcel 批量生成账单记录Excel并打包成zip文件
func (s *FinanceServiceImpl) BatchGenerateBillingRecordExcel(ctx context.Context, req dto.BatchBillingRecordExportRequest) ([]byte, string, error) {
	// 1. 参数验证
	if len(req.BillingRecordIDs) == 0 {
		return nil, "", fmt.Errorf("账单记录ID列表不能为空")
	}

	if len(req.BillingRecordIDs) > 100 {
		return nil, "", fmt.Errorf("批量导出数量不能超过100个")
	}

	// 2. 创建zip文件
	var zipBuffer bytes.Buffer
	zipWriter := zip.NewWriter(&zipBuffer)
	defer zipWriter.Close()

	// 3. 记录成功和失败的账单记录
	var successCount int
	var errorMessages []string

	// 4. 逐个生成Excel文件并添加到zip中
	for i, billingRecordID := range req.BillingRecordIDs {
		// 创建单个导出请求
		singleReq := dto.BillingRecordExportRequest{
			BillingRecordID: billingRecordID,
		}

		// 生成单个Excel文件
		excelData, fileName, err := s.GenerateBillingRecordExcel(ctx, singleReq)
		if err != nil {
			// 记录错误但继续处理其他文件
			errorMessage := fmt.Sprintf("账单记录ID %d 导出失败: %v", billingRecordID, err)
			errorMessages = append(errorMessages, errorMessage)
			zap.L().Warn("批量导出中单个文件生成失败",
				zap.Int64("billingRecordID", billingRecordID),
				zap.Error(err))
			continue
		}

		// 确保文件名唯一（添加序号前缀）
		uniqueFileName := fmt.Sprintf("%03d_%s", i+1, fileName)

		// 创建zip文件条目
		fileWriter, err := zipWriter.Create(uniqueFileName)
		if err != nil {
			errorMessage := fmt.Sprintf("账单记录ID %d 创建zip条目失败: %v", billingRecordID, err)
			errorMessages = append(errorMessages, errorMessage)
			continue
		}

		// 将Excel数据写入zip文件
		_, err = fileWriter.Write(excelData)
		if err != nil {
			errorMessage := fmt.Sprintf("账单记录ID %d 写入zip文件失败: %v", billingRecordID, err)
			errorMessages = append(errorMessages, errorMessage)
			continue
		}

		successCount++
		zap.L().Info("成功添加文件到zip包",
			zap.Int64("billingRecordID", billingRecordID),
			zap.String("fileName", uniqueFileName))
	}

	// 5. 如果有错误信息，创建错误报告文件
	if len(errorMessages) > 0 {
		errorReport := strings.Join(errorMessages, "\n")
		errorFileName := "导出错误报告.txt"

		errorWriter, err := zipWriter.Create(errorFileName)
		if err == nil {
			errorWriter.Write([]byte(errorReport))
		}

		zap.L().Warn("批量导出包含错误",
			zap.Int("successCount", successCount),
			zap.Int("errorCount", len(errorMessages)),
			zap.Strings("errors", errorMessages))
	}

	// 6. 关闭zip writer以确保所有数据都写入
	if err := zipWriter.Close(); err != nil {
		return nil, "", fmt.Errorf("关闭zip文件失败: %w", err)
	}

	// 7. 检查是否至少有一个文件成功
	if successCount == 0 {
		return nil, "", fmt.Errorf("所有账单记录导出都失败: %s", strings.Join(errorMessages, "; "))
	}

	// 8. 生成zip文件名
	now := time.Now()
	zipFileName := fmt.Sprintf("批量账单导出_%s_%d个文件.zip",
		now.Format("20060102_150405"),
		successCount)

	zap.L().Info("批量导出完成",
		zap.Int("totalRequested", len(req.BillingRecordIDs)),
		zap.Int("successCount", successCount),
		zap.Int("errorCount", len(errorMessages)),
		zap.String("zipFileName", zipFileName))

	return zipBuffer.Bytes(), zipFileName, nil
}
