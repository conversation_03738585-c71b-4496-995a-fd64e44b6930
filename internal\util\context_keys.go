package util

import "github.com/golang-jwt/jwt/v5"

// ContextKey тип для ключей контекста во избежание коллизий.
// type ContextKey string // Можно использовать строковый тип для ключей или более специфичный.

const (
	// RequestIDContextKey ключ для хранения Request ID в контексте Gin и для логирования.
	RequestIDContextKey = "requestId"
	// LoggerContextKey ключ для хранения специфичного для запроса Logger'а в контексте Gin.
	LoggerContextKey = "logger"
	// ClaimsContextKey ключ для хранения JWT Claims в контексте Gin.
	ClaimsContextKey = "claims"
)

// CustomClaims определяет структуру пользовательских утверждений в JWT.
type CustomClaims struct {
	UserID   int64  `json:"userId"`
	Username string `json:"username"`
	RoleID   int64  `json:"roleId"`
	jwt.RegisteredClaims
} 