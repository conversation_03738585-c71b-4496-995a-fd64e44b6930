package service

import (
	"fmt"
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// ShippingFeeTemplateService 运费模板服务接口
type ShippingFeeTemplateService interface {

	
	// GetTemplatesWithSearch 查询运费模板，支持模板名模糊搜索
	GetTemplatesWithSearch(ginCtx *gin.Context, req *GetTemplatesWithSearchRequest) (*GetTemplatesWithSearchResponse, error)
	
	// CreateTemplate 创建运费模板
	CreateTemplate(ginCtx *gin.Context, req *CreateShippingFeeTemplateRequest) (*ShippingFeeTemplateDTO, error)
	
	// UpdateTemplate 更新运费模板
	UpdateTemplate(ginCtx *gin.Context, req *UpdateShippingFeeTemplateRequest) (*ShippingFeeTemplateDTO, error)
	
	// DeleteTemplate 删除运费模板
	DeleteTemplate(ginCtx *gin.Context, id int64) error
	
	// GetTemplateByID 根据ID获取运费模板
	GetTemplateByID(ginCtx *gin.Context, id int64) (*ShippingFeeTemplateDTO, error)
	
	// GetUserConfiguredTemplates 根据用户ID获取其已配置的运费模板
	GetUserConfiguredTemplates(ginCtx *gin.Context, userID int64) (*GetUserConfiguredTemplatesResponse, error)
	
	// UpdateUserTemplateConfigurations 更新用户的模板配置
	UpdateUserTemplateConfigurations(ginCtx *gin.Context, userID int64, req *UpdateUserTemplateConfigurationsRequest) (*GetUserConfiguredTemplatesResponse, error)
}

// ShippingFeeTemplateDTO 运费模板DTO
type ShippingFeeTemplateDTO struct {
	ID                      int64   `json:"id"`                      // 模板ID
	Name                    string  `json:"name"`                    // 模板名称
	Type                    int     `json:"type,omitempty"`          // 模板类型（仅在用户配置的模板中返回）
	FirstWeightPrice        float64 `json:"firstWeightPrice"`        // 首重价格
	FirstWeightRange        float64 `json:"firstWeightRange"`        // 首重范围
	ContinuedWeightPrice    float64 `json:"continuedWeightPrice"`    // 续重价格
	ContinuedWeightInterval float64 `json:"continuedWeightInterval"` // 续重区间大小
	BulkCoefficient         int     `json:"bulkCoefficient"`         // 轻抛系数
	ThreeSidesStart         float64 `json:"threeSidesStart"`         // 三边和超过多少开始计算体积重量
	CreateTime              string  `json:"createTime"`              // 创建时间，格式：yyyy-MM-dd HH:mm:ss
	UpdateTime              string  `json:"updateTime"`              // 更新时间，格式：yyyy-MM-dd HH:mm:ss
}

// GetTemplatesWithSearchRequest 查询运费模板请求
type GetTemplatesWithSearchRequest struct {
	Name string `form:"name"` // 模板名称（模糊搜索）
}

// GetTemplatesWithSearchResponse 查询运费模板响应
type GetTemplatesWithSearchResponse struct {
	Templates []*ShippingFeeTemplateDTO `json:"templates"` // 模板列表
}

// CreateShippingFeeTemplateRequest 创建运费模板请求DTO
type CreateShippingFeeTemplateRequest struct {
	Name                    string  `json:"name" binding:"required" example:"测试模板"`                   // 模板名称
	FirstWeightPrice        float64 `json:"firstWeightPrice" binding:"required" example:"10.5"`        // 首重价格
	FirstWeightRange        float64 `json:"firstWeightRange" binding:"required" example:"1.0"`         // 首重范围(kg)
	ContinuedWeightPrice    float64 `json:"continuedWeightPrice" binding:"required" example:"5.5"`     // 续重价格
	ContinuedWeightInterval float64 `json:"continuedWeightInterval" binding:"required" example:"0.5"`  // 续重计费间隔(kg)
	BulkCoefficient         int     `json:"bulkCoefficient" binding:"required" example:"5000"`         // 泡重系数
	ThreeSidesStart         float64 `json:"threeSidesStart" binding:"required" example:"60.0"`         // 三边之和起算值(cm)
}

// UpdateShippingFeeTemplateRequest 更新运费模板请求DTO
type UpdateShippingFeeTemplateRequest struct {
	ID                      int64   `json:"id" binding:"required" example:"1"`                        // 模板ID
	Name                    string  `json:"name" binding:"required" example:"测试模板"`                   // 模板名称
	FirstWeightPrice        float64 `json:"firstWeightPrice" binding:"required" example:"10.5"`        // 首重价格
	FirstWeightRange        float64 `json:"firstWeightRange" binding:"required" example:"1.0"`         // 首重范围(kg)
	ContinuedWeightPrice    float64 `json:"continuedWeightPrice" binding:"required" example:"5.5"`     // 续重价格
	ContinuedWeightInterval float64 `json:"continuedWeightInterval" binding:"required" example:"0.5"`  // 续重计费间隔(kg)
	BulkCoefficient         int     `json:"bulkCoefficient" binding:"required" example:"5000"`         // 泡重系数
	ThreeSidesStart         float64 `json:"threeSidesStart" binding:"required" example:"60.0"`         // 三边之和起算值(cm)
}

// GetUserConfiguredTemplatesResponse 获取用户已配置的运费模板响应DTO
type GetUserConfiguredTemplatesResponse struct {
	UserID    int64                      `json:"userId"`    // 用户ID
	Templates []*ShippingFeeTemplateDTO `json:"templates"` // 用户已配置的模板列表
}

// UserTemplateConfigurationItem 用户模板配置项
type UserTemplateConfigurationItem struct {
	TemplateID int64 `json:"templateId" binding:"required" example:"1"` // 模板ID
	Type       int   `json:"type" binding:"required" example:"1"`       // 模板类型：1-普通模板，2-带电模板，3-投函模板，6-特殊模板
}

// UpdateUserTemplateConfigurationsRequest 更新用户模板配置请求DTO
type UpdateUserTemplateConfigurationsRequest struct {
	Configurations []*UserTemplateConfigurationItem `json:"configurations" binding:"required,dive" example:"[{\"type\":1,\"templateId\":1},{\"type\":2,\"templateId\":2}]"` // 模板配置列表
}

// ShippingFeeTemplateServiceImpl 运费模板服务实现
type ShippingFeeTemplateServiceImpl struct {
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository
}

// NewShippingFeeTemplateService 创建运费模板服务实例
func NewShippingFeeTemplateService(shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository) ShippingFeeTemplateService {
	return &ShippingFeeTemplateServiceImpl{
		shippingFeeTemplateRepo: shippingFeeTemplateRepo,
	}
}

// GetTemplatesWithSearch 查询运费模板，支持模板名模糊搜索
func (s *ShippingFeeTemplateServiceImpl) GetTemplatesWithSearch(ginCtx *gin.Context, req *GetTemplatesWithSearchRequest) (*GetTemplatesWithSearchResponse, error) {
	ctx := ginCtx.Request.Context()
	
	templates, err := s.shippingFeeTemplateRepo.GetTemplatesWithSearch(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	
	dtos := make([]*ShippingFeeTemplateDTO, len(templates))
	for i, template := range templates {
		dtos[i] = s.convertToDTO(template)
	}
	
	return &GetTemplatesWithSearchResponse{
		Templates: dtos,
	}, nil
}

// convertToDTO 将实体转换为DTO
func (s *ShippingFeeTemplateServiceImpl) convertToDTO(template *entity.ShippingFeeTemplate) *ShippingFeeTemplateDTO {
	return &ShippingFeeTemplateDTO{
		ID:                      template.ID,
		Name:                    template.Name,
		FirstWeightPrice:        template.FirstWeightPrice,
		FirstWeightRange:        template.FirstWeightRange,
		ContinuedWeightPrice:    template.ContinuedWeightPrice,
		ContinuedWeightInterval: template.ContinuedWeightInterval,
		BulkCoefficient:         template.BulkCoefficient,
		ThreeSidesStart:         template.ThreeSidesStart,
		CreateTime:              template.CreateTime.Time.Format("2006-01-02 15:04:05"),
		UpdateTime:              template.UpdateTime.Time.Format("2006-01-02 15:04:05"),
	}
}

// convertToDTOWithType 将用户配置的模板转换为DTO（包含type信息）
func (s *ShippingFeeTemplateServiceImpl) convertToDTOWithType(userTemplate *repository.UserConfiguredTemplate) *ShippingFeeTemplateDTO {
	dto := s.convertToDTO(userTemplate.Template)
	dto.Type = userTemplate.Type
	return dto
}

// CreateTemplate 创建运费模板
func (s *ShippingFeeTemplateServiceImpl) CreateTemplate(ginCtx *gin.Context, req *CreateShippingFeeTemplateRequest) (*ShippingFeeTemplateDTO, error) {
	ctx := ginCtx.Request.Context()
	
	// 检查模板名称是否已存在
	nameExists, err := s.shippingFeeTemplateRepo.CheckTemplateNameExists(ctx, req.Name)
	if err != nil {
		return nil, fmt.Errorf("检查模板名称是否存在失败: %w", err)
	}
	if nameExists {
		return nil, fmt.Errorf("模板名称已存在: %s", req.Name)
	}
	
	// 创建实体
	now := util.FormattedTime{Time: time.Now()}
	template := &entity.ShippingFeeTemplate{
		Name:                    req.Name,
		FirstWeightPrice:        req.FirstWeightPrice,
		FirstWeightRange:        req.FirstWeightRange,
		ContinuedWeightPrice:    req.ContinuedWeightPrice,
		ContinuedWeightInterval: req.ContinuedWeightInterval,
		BulkCoefficient:         req.BulkCoefficient,
		ThreeSidesStart:         req.ThreeSidesStart,
		CreateTime:              now,
		UpdateTime:              now,
	}
	
	err = s.shippingFeeTemplateRepo.CreateTemplate(ctx, template)
	if err != nil {
		return nil, fmt.Errorf("创建运费模板失败: %w", err)
	}
	
	return s.convertToDTO(template), nil
}

// UpdateTemplate 更新运费模板
func (s *ShippingFeeTemplateServiceImpl) UpdateTemplate(ginCtx *gin.Context, req *UpdateShippingFeeTemplateRequest) (*ShippingFeeTemplateDTO, error) {
	ctx := ginCtx.Request.Context()
	
	// 检查模板是否存在
	exists, err := s.shippingFeeTemplateRepo.CheckTemplateExists(ctx, req.ID)
	if err != nil {
		return nil, fmt.Errorf("检查模板是否存在失败: %w", err)
	}
	if !exists {
		return nil, fmt.Errorf("运费模板不存在: ID=%d", req.ID)
	}
	
	// 检查模板名称是否已被其他模板使用
	nameExists, err := s.shippingFeeTemplateRepo.CheckTemplateNameExistsExcludeID(ctx, req.Name, req.ID)
	if err != nil {
		return nil, fmt.Errorf("检查模板名称是否存在失败: %w", err)
	}
	if nameExists {
		return nil, fmt.Errorf("模板名称已被其他模板使用: %s", req.Name)
	}
	
	// 更新实体
	template := &entity.ShippingFeeTemplate{
		ID:                      req.ID,
		Name:                    req.Name,
		FirstWeightPrice:        req.FirstWeightPrice,
		FirstWeightRange:        req.FirstWeightRange,
		ContinuedWeightPrice:    req.ContinuedWeightPrice,
		ContinuedWeightInterval: req.ContinuedWeightInterval,
		BulkCoefficient:         req.BulkCoefficient,
		ThreeSidesStart:         req.ThreeSidesStart,
		UpdateTime:              util.FormattedTime{Time: time.Now()},
	}
	
	err = s.shippingFeeTemplateRepo.UpdateTemplate(ctx, template)
	if err != nil {
		return nil, fmt.Errorf("更新运费模板失败: %w", err)
	}
	
	// 返回更新后的模板详情
	return s.GetTemplateByID(ginCtx, req.ID)
}

// DeleteTemplate 删除运费模板
func (s *ShippingFeeTemplateServiceImpl) DeleteTemplate(ginCtx *gin.Context, id int64) error {
	ctx := ginCtx.Request.Context()
	
	// 检查模板是否存在
	exists, err := s.shippingFeeTemplateRepo.CheckTemplateExists(ctx, id)
	if err != nil {
		return fmt.Errorf("检查模板是否存在失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("运费模板不存在: ID=%d", id)
	}
	
	// 检查模板是否被用户使用
	inUse, err := s.shippingFeeTemplateRepo.CheckTemplateInUse(ctx, id)
	if err != nil {
		return fmt.Errorf("检查模板使用情况失败: %w", err)
	}
	if inUse {
		return fmt.Errorf("运费模板正在被用户使用，无法删除: ID=%d", id)
	}
	
	err = s.shippingFeeTemplateRepo.DeleteTemplate(ctx, id)
	if err != nil {
		return fmt.Errorf("删除运费模板失败: %w", err)
	}
	
	return nil
}

// GetTemplateByID 根据ID获取运费模板
func (s *ShippingFeeTemplateServiceImpl) GetTemplateByID(ginCtx *gin.Context, id int64) (*ShippingFeeTemplateDTO, error) {
	ctx := ginCtx.Request.Context()
	
	template, err := s.shippingFeeTemplateRepo.GetTemplateByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("获取运费模板失败: %w", err)
	}
	
	return s.convertToDTO(&template), nil
}

// GetUserConfiguredTemplates 根据用户ID获取其已配置的运费模板
func (s *ShippingFeeTemplateServiceImpl) GetUserConfiguredTemplates(ginCtx *gin.Context, userID int64) (*GetUserConfiguredTemplatesResponse, error) {
	ctx := ginCtx.Request.Context()
	
	userTemplates, err := s.shippingFeeTemplateRepo.GetUserConfiguredTemplates(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户已配置的运费模板失败: %w", err)
	}
	
	// 转换为DTO
	dtos := make([]*ShippingFeeTemplateDTO, len(userTemplates))
	for i, userTemplate := range userTemplates {
		dtos[i] = s.convertToDTOWithType(userTemplate)
	}
	
	return &GetUserConfiguredTemplatesResponse{
		UserID:    userID,
		Templates: dtos,
	}, nil
}

// UpdateUserTemplateConfigurations 更新用户的模板配置
func (s *ShippingFeeTemplateServiceImpl) UpdateUserTemplateConfigurations(ginCtx *gin.Context, userID int64, req *UpdateUserTemplateConfigurationsRequest) (*GetUserConfiguredTemplatesResponse, error) {
	ctx := ginCtx.Request.Context()
	
	// 验证配置项
	for _, config := range req.Configurations {
		// 验证模板是否存在
		exists, err := s.shippingFeeTemplateRepo.CheckTemplateExists(ctx, config.TemplateID)
		if err != nil {
			return nil, fmt.Errorf("检查模板是否存在失败: %w", err)
		}
		if !exists {
			return nil, fmt.Errorf("模板不存在: ID=%d", config.TemplateID)
		}
	}
	
	// 转换为实体对象
	templateConfigs := make([]*entity.ShippingFeeTemplateUser, len(req.Configurations))
	for i, config := range req.Configurations {
		templateConfigs[i] = &entity.ShippingFeeTemplateUser{
			UserID:     userID,
			TemplateID: config.TemplateID,
			Type:       config.Type,
		}
	}
	
	// 执行更新
	err := s.shippingFeeTemplateRepo.UpdateUserTemplateConfigurations(ctx, userID, templateConfigs)
	if err != nil {
		return nil, fmt.Errorf("更新用户模板配置失败: %w", err)
	}
	
	// 返回更新后的配置
	return s.GetUserConfiguredTemplates(ginCtx, userID)
}

 