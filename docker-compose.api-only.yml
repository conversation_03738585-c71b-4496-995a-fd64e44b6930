version: "3.8"

services:
  # API服务器
  api-server:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_NAME: api-server
        BINARY_NAME: zebra-hub-system
    image: zebra-hub-system/api-server:latest
    container_name: zebra-api-server
    ports:
      - "8080:8080"
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - ./assets:/app/assets
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped

networks:
  default:
    name: zebra-api-network
    driver: bridge
