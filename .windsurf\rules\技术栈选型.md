---
trigger: always_on
---

# 技术栈
## 后端语言： Go (Golang) (最新稳定版)
## Web 框架： Gin (github.com/gin-gonic/gin)
## 数据库交互： GORM (gorm.io/gorm) 配合 MySQL
## 配置管理： Viper (github.com/spf13/viper)
## 日志库： Zap (go.uber.org/zap)
## API 文档： swaggo/swag (github.com/swaggo/swag)
## 参数校验： go-playground/validator (github.com/go-playground/validator/v10)
## 缓存: go-redis/redis (github.com/go-redis/redis/v8 或更高版本) 配合 Redis 服务器
## 消息队列: 已有 RabbitMQ 基础设施，可以使用 streadway/amqp。
## 测试：
- 标准库 testing
- 断言库：testify/assert 和 testify/require (github.com/stretchr/testify)
- Mock 工具：gomock (github.com/golang/mock) 或 testify/mock
## 代码格式化与 Lint：
- 格式化：goimports (或 gofmt)
- Lint：golangci-lint (github.com/golangci/golangci-lint)
## 构建与部署：
- 构建：标准 go build

- 容器化：Docker