# Zebra Hub System Makefile

# 变量定义
APP_NAME = zebra-hub-system
API_SERVER_NAME = api-server
BILLING_CONSUMER_NAME = billing-consumer
BIN_DIR = bin
CMD_DIR = cmd
LOG_DIR = logs

# Go相关变量
GO = go
GOOS ?= $(shell go env GOOS)
GOARCH ?= $(shell go env GOARCH)

# 版本信息
VERSION ?= $(shell git describe --tags --always --dirty)
BUILD_TIME = $(shell date +"%Y-%m-%d %H:%M:%S")
COMMIT = $(shell git rev-parse --short HEAD)

# 编译标记
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X 'main.BuildTime=$(BUILD_TIME)' -X main.Commit=$(COMMIT)"

# 默认目标
.PHONY: all
all: clean build-api build-consumer

# 创建必要的目录
.PHONY: prepare
prepare:
	@echo "创建必要的目录..."
	@if not exist "$(BIN_DIR)" mkdir $(BIN_DIR)
	@if not exist "$(LOG_DIR)" mkdir $(LOG_DIR)

# 清理构建文件
.PHONY: clean
clean:
	@echo "清理构建文件..."
	@if exist "$(BIN_DIR)" rmdir /s /q $(BIN_DIR)
	@if not exist "$(BIN_DIR)" mkdir $(BIN_DIR)

# 格式化代码
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	$(GO) fmt ./...

# 代码检查
.PHONY: vet
vet:
	@echo "代码检查..."
	$(GO) vet ./...

# 运行测试
.PHONY: test
test:
	@echo "运行测试..."
	$(GO) test -v ./...

# 下载依赖
.PHONY: mod-download
mod-download:
	@echo "下载依赖包..."
	$(GO) mod download

# 整理依赖
.PHONY: mod-tidy
mod-tidy:
	@echo "整理依赖包..."
	$(GO) mod tidy

# 构建API服务器
.PHONY: build-api
build-api: prepare fmt vet
	@echo "构建API服务器..."
	$(GO) build $(LDFLAGS) -o $(BIN_DIR)/$(API_SERVER_NAME).exe $(CMD_DIR)/$(API_SERVER_NAME)/

# 构建账单消费者
.PHONY: build-consumer
build-consumer: prepare fmt vet
	@echo "构建账单生成消费者..."
	$(GO) build $(LDFLAGS) -o $(BIN_DIR)/$(BILLING_CONSUMER_NAME).exe $(CMD_DIR)/$(BILLING_CONSUMER_NAME)/

# 构建所有程序
.PHONY: build
build: build-api build-consumer

# 启动API服务器
.PHONY: run-api
run-api: build-api
	@echo "启动API服务器..."
	$(BIN_DIR)/$(API_SERVER_NAME).exe

# 启动账单消费者
.PHONY: run-consumer
run-consumer: build-consumer
	@echo "启动账单生成消费者..."
	$(BIN_DIR)/$(BILLING_CONSUMER_NAME).exe

# 启动API服务器 (开发模式)
.PHONY: dev-api
dev-api:
	@echo "启动API服务器 (开发模式)..."
	$(GO) run $(CMD_DIR)/$(API_SERVER_NAME)/main.go

# 启动账单消费者 (开发模式)
.PHONY: dev-consumer
dev-consumer:
	@echo "启动账单生成消费者 (开发模式)..."
	$(GO) run $(CMD_DIR)/$(BILLING_CONSUMER_NAME)/main.go

# 使用脚本启动消费者
.PHONY: start-consumer
start-consumer:
	@echo "使用脚本启动账单生成消费者..."
	scripts/start-billing-consumer.bat start

# 停止消费者
.PHONY: stop-consumer
stop-consumer:
	@echo "停止账单生成消费者..."
	scripts/start-billing-consumer.bat stop

# 重启消费者
.PHONY: restart-consumer
restart-consumer:
	@echo "重启账单生成消费者..."
	scripts/start-billing-consumer.bat restart

# 查看消费者状态
.PHONY: status-consumer
status-consumer:
	@echo "查看账单生成消费者状态..."
	scripts/start-billing-consumer.bat status

# 查看消费者日志
.PHONY: logs-consumer
logs-consumer:
	@echo "查看账单生成消费者日志..."
	scripts/start-billing-consumer.bat logs

# 实时查看消费者日志
.PHONY: logs-consumer-follow
logs-consumer-follow:
	@echo "实时查看账单生成消费者日志..."
	scripts/start-billing-consumer.bat logs -f

# 安装依赖工具
.PHONY: install-tools
install-tools:
	@echo "安装开发工具..."
	$(GO) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# 代码质量检查
.PHONY: lint
lint:
	@echo "运行代码质量检查..."
	golangci-lint run

# 生成API文档
.PHONY: docs
docs:
	@echo "生成API文档..."
	swag init -g $(CMD_DIR)/$(API_SERVER_NAME)/main.go -o docs/swagger

# 发布构建 (交叉编译)
.PHONY: build-release
build-release: clean
	@echo "构建发布版本..."
	# Windows amd64
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BIN_DIR)/windows_amd64/$(API_SERVER_NAME).exe $(CMD_DIR)/$(API_SERVER_NAME)/
	GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BIN_DIR)/windows_amd64/$(BILLING_CONSUMER_NAME).exe $(CMD_DIR)/$(BILLING_CONSUMER_NAME)/
	
	# Linux amd64
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BIN_DIR)/linux_amd64/$(API_SERVER_NAME) $(CMD_DIR)/$(API_SERVER_NAME)/
	GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(BIN_DIR)/linux_amd64/$(BILLING_CONSUMER_NAME) $(CMD_DIR)/$(BILLING_CONSUMER_NAME)/

# Docker构建
.PHONY: docker-build
docker-build:
	@echo "构建Docker镜像..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker build -t $(APP_NAME)-consumer:$(VERSION) -f Dockerfile.consumer .

# 显示版本信息
.PHONY: version
version:
	@echo "版本信息:"
	@echo "  应用名称: $(APP_NAME)"
	@echo "  版本号: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  提交哈希: $(COMMIT)"
	@echo "  Go版本: $(shell go version)"

# 显示帮助信息
.PHONY: help
help:
	@echo "可用的Make目标:"
	@echo ""
	@echo "构建相关:"
	@echo "  build           - 构建所有程序"
	@echo "  build-api       - 构建API服务器"
	@echo "  build-consumer  - 构建账单生成消费者"
	@echo "  build-release   - 构建发布版本(交叉编译)"
	@echo ""
	@echo "运行相关:"
	@echo "  run-api         - 启动API服务器"
	@echo "  run-consumer    - 启动账单生成消费者"
	@echo "  dev-api         - 启动API服务器(开发模式)"
	@echo "  dev-consumer    - 启动账单生成消费者(开发模式)"
	@echo ""
	@echo "消费者管理:"
	@echo "  start-consumer  - 使用脚本启动消费者"
	@echo "  stop-consumer   - 停止消费者"
	@echo "  restart-consumer- 重启消费者"
	@echo "  status-consumer - 查看消费者状态"
	@echo "  logs-consumer   - 查看消费者日志"
	@echo "  logs-consumer-follow - 实时查看消费者日志"
	@echo ""
	@echo "代码质量:"
	@echo "  fmt             - 格式化代码"
	@echo "  vet             - 代码检查"
	@echo "  test            - 运行测试"
	@echo "  lint            - 代码质量检查"
	@echo ""
	@echo "依赖管理:"
	@echo "  mod-download    - 下载依赖包"
	@echo "  mod-tidy        - 整理依赖包"
	@echo ""
	@echo "其他:"
	@echo "  clean           - 清理构建文件"
	@echo "  docs            - 生成API文档"
	@echo "  docker-build    - 构建Docker镜像"
	@echo "  version         - 显示版本信息"
	@echo "  help            - 显示此帮助信息" 