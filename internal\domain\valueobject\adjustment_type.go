package valueobject

// AdjustmentType 定义了财务调整的类型
type AdjustmentType string

const (
	// AdjustmentTypeCompensation 表示赔偿类调整
	AdjustmentTypeCompensation AdjustmentType = "COMPENSATION"
	// AdjustmentTypeReassignment 表示改派类调整
	AdjustmentTypeReassignment AdjustmentType = "REASSIGNMENT"
	// AdjustmentTypeDestruction 表示销毁类调整
	AdjustmentTypeDestruction AdjustmentType = "DESTRUCTION"
	// AdjustmentTypeReturn 表示退回类调整
	AdjustmentTypeReturn AdjustmentType = "RETURN"
	// AdjustmentTypeFee 表示费用类调整 (例如，操作费、附加费)
	AdjustmentTypeFee AdjustmentType = "FEE"
	// AdjustmentTypeRebate 表示返款、折扣类调整
	AdjustmentTypeRebate AdjustmentType = "REBATE"
	// AdjustmentTypeOther 表示其他类型的调整
	AdjustmentTypeOther AdjustmentType = "OTHER"
)
