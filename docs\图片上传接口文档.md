# 图片上传接口文档

## 概述

本文档描述了斑马物巢系统中的图片上传相关 API 接口，支持将图片文件上传到阿里云 OSS 存储服务。主要用于赔偿类财务调整的货值证明图片上传。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1`
- **认证方式**: JWT Token (Bearer Token)
- **请求格式**: multipart/form-data (上传接口) / JSON (其他接口)
- **响应格式**: JSON

## 接口列表

### 1. 上传图片

#### 基本信息

- **URL**: `/upload/image`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **认证**: 需要 JWT Token

#### 请求参数

| 参数名       | 类型   | 必选 | 说明                                                                             |
| ------------ | ------ | ---- | -------------------------------------------------------------------------------- |
| file         | file   | 是   | 要上传的图片文件                                                                 |
| businessType | string | 是   | 业务类型，支持：`compensation_proof`（赔偿证明）、`adjustment_proof`（调整证明） |

#### 文件限制

- **支持格式**: jpg、jpeg、png、gif、bmp、webp
- **最大文件大小**: 10MB
- **文件名**: 系统自动生成 UUID 文件名

#### 请求示例

**cURL 示例**:

```bash
curl -X POST "http://localhost:8080/api/v1/upload/image" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/image.jpg" \
  -F "businessType=compensation_proof"
```

**JavaScript 示例**:

```javascript
const formData = new FormData();
formData.append("file", fileInput.files[0]);
formData.append("businessType", "compensation_proof");

fetch("/api/v1/upload/image", {
  method: "POST",
  headers: {
    Authorization: "Bearer " + token,
  },
  body: formData,
})
  .then((response) => response.json())
  .then((data) => console.log(data));
```

#### 成功响应

**HTTP Status**: `200 OK`

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": {
    "fileUrl": "https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/compensation/proofs/2024/05/30/550e8400-e29b-41d4-a716-************.jpg",
    "fileName": "compensation/proofs/2024/05/30/550e8400-e29b-41d4-a716-************.jpg",
    "fileSize": 1024000,
    "uploadTime": "2024-05-30 12:00:00"
  }
}
```

#### 错误响应

**参数错误 (400)**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "参数错误: 请选择要上传的文件",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": null
}
```

**文件格式不支持 (400)**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "不支持的图片格式，支持的格式：jpg, jpeg, png, gif, bmp, webp",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": null
}
```

**文件大小超限 (400)**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "文件大小超出限制，最大支持10MB，当前文件大小：15.2MB",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": null
}
```

**认证失败 (401)**:

```json
{
  "success": false,
  "errorCode": 100004,
  "errorMessage": "未授权/未登录",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": null
}
```

**服务器错误 (500)**:

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "文件上传失败",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": null
}
```

---

### 2. 删除图片

#### 基本信息

- **URL**: `/upload/image`
- **Method**: `DELETE`
- **认证**: 需要 JWT Token

#### 请求参数

| 参数名  | 类型   | 必选 | 位置  | 说明                 |
| ------- | ------ | ---- | ----- | -------------------- |
| fileUrl | string | 是   | Query | 要删除的图片文件 URL |

#### 请求示例

**cURL 示例**:

```bash
curl -X DELETE "http://localhost:8080/api/v1/upload/image?fileUrl=https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/compensation/proofs/2024/05/30/uuid.jpg" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**JavaScript 示例**:

```javascript
const fileUrl = encodeURIComponent(
  "https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/compensation/proofs/2024/05/30/uuid.jpg"
);

fetch(`/api/v1/upload/image?fileUrl=${fileUrl}`, {
  method: "DELETE",
  headers: {
    Authorization: "Bearer " + token,
  },
})
  .then((response) => response.json())
  .then((data) => console.log(data));
```

#### 成功响应

**HTTP Status**: `200 OK`

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": {
    "message": "文件删除成功"
  }
}
```

#### 错误响应

**参数缺失 (400)**:

```json
{
  "success": false,
  "errorCode": 100003,
  "errorMessage": "缺少文件URL参数",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": null
}
```

---

### 3. 获取上传配置信息

#### 基本信息

- **URL**: `/upload/info`
- **Method**: `GET`
- **认证**: 需要 JWT Token

#### 请求示例

**cURL 示例**:

```bash
curl -X GET "http://localhost:8080/api/v1/upload/info" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**JavaScript 示例**:

```javascript
fetch("/api/v1/upload/info", {
  method: "GET",
  headers: {
    Authorization: "Bearer " + token,
  },
})
  .then((response) => response.json())
  .then((data) => console.log(data));
```

#### 成功响应

**HTTP Status**: `200 OK`

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-string",
  "timestamp": "2024-05-30 12:00:00",
  "data": {
    "maxFileSize": "10MB",
    "supportedTypes": ["jpg", "jpeg", "png", "gif", "bmp", "webp"],
    "businessTypes": ["compensation_proof", "adjustment_proof"],
    "uploadEndpoint": "/api/v1/upload/image",
    "deleteEndpoint": "/api/v1/upload/image"
  }
}
```

## 文件存储规则

### 存储路径

文件存储路径遵循以下格式：

```
{businessType}/{年}/{月}/{日}/{UUID}.{扩展名}
```

### 路径示例

- **赔偿证明**: `compensation/proofs/2024/05/30/550e8400-e29b-41d4-a716-************.jpg`
- **调整证明**: `adjustments/proofs/2024/05/30/550e8400-e29b-41d4-a716-************.png`

### 完整 URL 示例

```
https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/compensation/proofs/2024/05/30/550e8400-e29b-41d4-a716-************.jpg
```

## 使用流程

### 在赔偿类财务调整中的使用

1. **第一步：上传图片**

   ```javascript
   // 前端上传图片
   const uploadResponse = await uploadImage(imageFile, "compensation_proof");
   const imageUrl = uploadResponse.data.fileUrl;
   ```

2. **第二步：创建财务调整记录**

   ```javascript
   // 在创建赔偿调整时传入图片URL
   const adjustmentData = {
     manifestId: 12345,
     adjustmentType: "COMPENSATION",
     amount: 100.0,
     reason: "货物损坏",
     proofOfValueImageUrls: [imageUrl], // 使用上传得到的URL
     // 其他字段...
   };

   await createCompensationAdjustment(adjustmentData);
   ```

3. **第三步：图片管理（可选）**
   ```javascript
   // 如果需要删除图片
   await deleteImage(imageUrl);
   ```

## 错误码对照表

| 错误码 | 说明          |
| ------ | ------------- |
| 100000 | 操作成功      |
| 100001 | 系统错误      |
| 100002 | 无效参数      |
| 100003 | 缺少必要参数  |
| 100004 | 未授权/未登录 |
| 100005 | 无权限访问    |

## 注意事项

1. **认证要求**: 所有接口都需要在请求头中携带有效的 JWT Token
2. **文件大小**: 单个文件最大支持 10MB
3. **文件格式**: 仅支持常见的图片格式
4. **文件名**: 系统自动生成 UUID 文件名，确保唯一性
5. **存储位置**: 文件存储在阿里云 OSS 福州节点
6. **访问权限**: 上传的图片文件默认为公开读取
7. **删除限制**: 只能删除通过本系统上传的文件
