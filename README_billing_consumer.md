# 账单生成消费者系统

斑马物巢系统的异步账单生成消费者程序。

## 快速启动

### 1. 构建程序

```bash
# 使用Makefile（推荐）
make build-consumer

# 或手动构建
go build -o bin/billing-consumer.exe cmd/billing-consumer/main.go
```

### 2. 配置验证

```bash
# 运行配置验证
scripts\validate-consumer-config.bat
```

### 3. 启动消费者

```bash
# 方式1: 使用管理脚本（推荐）
scripts\start-billing-consumer.bat start

# 方式2: 使用Makefile
make start-consumer

# 方式3: 直接运行
bin\billing-consumer.exe
```

### 4. 监控和管理

```bash
# 查看状态
scripts\start-billing-consumer.bat status

# 查看日志
scripts\start-billing-consumer.bat logs

# 实时查看日志
scripts\start-billing-consumer.bat logs -f

# 停止服务
scripts\start-billing-consumer.bat stop

# 重启服务
scripts\start-billing-consumer.bat restart
```

## 系统架构

```
API接口 -> RabbitMQ队列 -> 消费者程序 -> 数据库
    ↓                        ↓             ↓
  任务创建              任务处理        状态更新
```

## 核心功能

- ✅ **异步处理**: 支持大批量账单生成任务
- ✅ **多客户支持**: 单次任务可处理多个客户
- ✅ **模板配置**: 支持用户特定和默认模板
- ✅ **容错机制**: 自动重试、错误隔离
- ✅ **实时监控**: 进度更新、状态追踪
- ✅ **优雅关闭**: 确保任务完整处理

## 技术特性

- **高可用**: 多工作者、故障恢复
- **可扩展**: 参数可配置、水平扩展
- **可监控**: 结构化日志、状态管理
- **可维护**: 完整文档、运维工具

## 环境要求

- Go 1.19+
- RabbitMQ 3.8+
- MySQL 8.0+
- Windows 10/11 或 Linux

## 配置说明

主要配置在 `configs/config.yaml`:

```yaml
rabbitmq:
  host: *************
  port: 5673
  virtual-host: /
  username: zebra
  password: banma1346797

database:
  host: your_db_host
  port: 3336
  # ... 其他数据库配置
```

## 开发模式

```bash
# 开发模式运行（直接输出日志）
make dev-consumer

# 或
go run cmd/billing-consumer/main.go
```

## 文档

- 📖 [详细使用说明](docs/财务模块_异步账单生成消费者使用说明.md)
- 📖 [功能实现文档](docs/财务模块_异步账单生成功能实现.md)
- 📖 [开发完成总结](docs/账单生成消费者_开发完成总结.md)

## 故障排查

1. **无法启动**

   - 检查配置文件: `configs/config.yaml`
   - 验证数据库连接
   - 检查 RabbitMQ 服务

2. **任务处理失败**

   - 查看日志: `logs/billing-consumer.log`
   - 检查数据库状态
   - 验证消息队列连接

3. **性能问题**
   - 调整工作者数量
   - 优化数据库查询
   - 监控系统资源

## 支持

如有问题，请查看:

1. 详细文档 (docs 目录)
2. 日志文件 (logs 目录)
3. 配置验证脚本结果

## 功能说明

本模块实现了账单生成的异步处理功能，通过消息队列接收账单生成任务并进行处理。

## 主要特性

1. **异步处理**: 支持大批量账单的异步生成
2. **模板检查**: 自动检查用户运费模板配置
3. **异常处理**: 对无模板配置的用户生成异常状态账单
4. **进度跟踪**: 实时更新任务处理进度

## 账单状态说明

| 状态           | 英文标识                   | 中文名称 | 说明                     |
| -------------- | -------------------------- | -------- | ------------------------ |
| UNPAID         | BillingStatusUnpaid        | 未付款   | 正常生成的账单，等待付款 |
| PAID           | BillingStatusPaid          | 已付款   | 已完成付款的账单         |
| PARTIALLY_PAID | BillingStatusPartiallyPaid | 部分付款 | 部分付款的账单           |
| OVERDUE        | BillingStatusOverdue       | 逾期     | 超过付款期限的账单       |
| VOID           | BillingStatusVoid          | 作废     | 已作废的账单             |
| ERROR          | BillingStatusError         | 异常     | 生成过程中出现异常的账单 |

## 异常状态处理

当生成账单时，如果检测到以下情况之一，系统会自动将账单状态设置为"异常"：

1. **模板配置缺失**: 用户未配置任何运费模板（普货、带电、投函模板都为空）
2. **模板查询失败**: 系统无法获取用户的模板配置

### 异常状态账单特点

- **状态**: ERROR（异常）
- **备注**: 包含具体的异常原因说明
- **明细**: 不生成账单明细项，保持为空列表
- **金额**: 总金额为 0
- **处理**: 需要手动修复模板配置后重新生成

### 异常处理流程

```mermaid
flowchart TD
    A[开始生成账单] --> B[检查用户模板配置]
    B --> C{是否有可用模板?}
    C -->|是| D[正常生成账单]
    C -->|否| E[设置状态为异常]
    E --> F[记录异常原因到备注]
    F --> G[跳过明细生成]
    G --> H[保存异常状态账单]
    D --> I[生成账单明细]
    I --> J[保存正常账单]
```

## 模板检查逻辑

系统会按以下顺序检查用户的运费模板配置：

1. **请求参数检查**: 检查生成账单请求中是否包含模板信息
2. **系统配置查询**: 如果请求中无模板，查询用户在系统中配置的默认模板
3. **模板类型检查**: 检查普货模板、带电模板、投函模板是否至少有一个可用
4. **异常处理**: 如果所有类型的模板都不可用，则标记为异常

## 相关接口

### 账单生成接口

- **同步生成**: `POST /api/v1/billing/generate`
- **异步生成**: `POST /api/v1/billing/async-generate`

### 账单查询接口

- **列表查询**: `GET /api/v1/billing/records`
- **详情查询**: `GET /api/v1/billing/records/{id}`

## 错误处理

系统会记录详细的日志信息，包括：

- 模板检查过程
- 异常状态设置原因
- 跳过明细生成的决策

管理员可以通过日志追踪异常账单的产生原因并及时处理。

---

_斑马物巢 © 2024_
