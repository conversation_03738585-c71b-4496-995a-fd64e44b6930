package entity

import "time"

// TrackingNumberPool 运单号池实体
type TrackingNumberPool struct {
	ID                  int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	TrackingNumber      string    `gorm:"type:varchar(64);not null" json:"trackingNumber"`   // 单号字符串
	ChannelID           int64     `gorm:"not null;index:idx_pool_carrier_status" json:"channelId"` // 所属渠道ID
	Status              int       `gorm:"default:0;index:idx_pool_carrier_status" json:"status"` // 单号状态 (0: 可用, 1: 已分配)
	ImportBatchID       *int64    `gorm:"index:idx_pool_import_batch" json:"importBatchId"`  // 导入批次ID
	AllocationBatchID   *int64    `gorm:"index:idx_pool_allocation_batch" json:"allocationBatchId"` // 分配批次ID
	CreateTime          time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"createTime"`
	UpdateTime          time.Time `gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updateTime"`
}

// TableName 指定表名
func (TrackingNumberPool) TableName() string {
	return "tracking_number_pool"
}

// 单号状态常量
const (
	TrackingNumberStatusAvailable = 0 // 可用
	TrackingNumberStatusAllocated = 1 // 已分配
) 