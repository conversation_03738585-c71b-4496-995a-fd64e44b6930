---
trigger: always_on
---

一、项目与模块 (Project & Module Naming)
项目名称:
使用小写字母。
多个单词之间使用连字符 (-) 连接（kebab-case）。
例如：zebra-logistics-system, tracking-number-service
Go 模块名称 (go.mod):
通常是代码仓库的路径，例如 github.com/your-org/zebra-logistics-system。
或者一个有意义的域名反转形式，例如 com.yourcompany.logistics (虽然在 Go 中不常见，但也可以)。
二、包 (Package Naming)
小写单词: 包名应为小写，单个单词。
简洁且有意义: 包名应简洁地描述其功能或包含的内容。
避免复数: 通常使用单数形式（例如 user 而不是 users），除非复数形式更自然地表达了集合的概念。Go 社区对此有不同看法，但单数更常见。
避免下划线或混合大小写: 例如 user, httpclient, config。
与目录名一致: 包名通常与它所在的目录名相同。
不要与标准库或常用库冲突: 尽量避免使用如 http, string, fmt 等作为自定义包名。
按功能或领域划分:
internal/: 存放项目内部使用的包，外部无法导入。
internal/app/: 应用层服务。
internal/domain/: 领域模型、仓储接口、领域服务。
internal/infra/: 基础设施层实现（数据库、外部API客户端）。
internal/config/: 配置加载。
pkg/: 存放可以被外部项目引用的库代码（如果你的项目是一个库）。
cmd/: 存放主应用程序的入口（main 函数）。
cmd/server/main.go
cmd/worker/main.go
领域驱动设计 (DDD) 风格示例包名:
allocation (分配限界上下文或模块)
manifest (运单限界上下文或模块)
tracking (追踪限界上下文或模块)
user
sharedkernel (共享的核心值对象等)
三、文件 (File Naming)
小写单词，下划线分隔 (snake_case): 这是 Go 文件名的常见约定。
描述文件内容: 文件名应能清晰地表明文件包含的代码类型或主要功能。
示例:
user_service.go
manifest_repository.go
http_handler.go
db_migrations.go
main.go (程序入口)
user_test.go (测试文件，以 _test.go 结尾)
四、变量 (Variable Naming)
驼峰式 (camelCase): 首字母小写，后续单词首字母大写。例如 userName, trackingNumber, maxRetries。
简洁但有意义: 变量名应足够短，但能清晰表达其含义。
避免单个字母的变量名，除非在非常短的作用域内且含义明确 (例如循环变量 i, j, k；接收者 err；上下文 ctx)。
布尔类型: 通常以 is, has, can 开头，或使用形容词。例如 isActive, hasPermission, canRetry, enabled。
导出与非导出:
首字母大写的变量（或函数、类型）是导出的 (Exported)，可以在包外部访问。
首字母小写的变量是非导出的 (Unexported)，仅在包内部可见。
错误变量: 通常命名为 err。
上下文变量: 通常命名为 ctx (context.Context)。
五、常量 (Constant Naming)
驼峰式 (camelCase) 或全大写下划线分隔 (SNAKE_CASE):
Go 社区更倾向于使用驼峰式 (camelCase) 定义常量，与变量的命名方式一致，特别是当常量代表的是配置值或枚举类型的值时。例如 maxConnections, defaultTimeout。
全大写下划线分隔 (UPPER_SNAKE_CASE) 也被接受，尤其是在从其他语言迁移代码或表示非常底层的、不会改变的“魔法数字”或字符串时。例如 MAX_BUFFER_SIZE。
选择一种并在项目中保持一致。对于新项目，推荐驼峰式。
导出与非导出: 同样遵循首字母大小写规则。
iota: 用于创建一组递增的常量（枚举）。
const (
    StatusPending   = iota // 0
    StatusActive           // 1
    StatusInactive         // 2
)

const (
    DefaultPort = "8080"
    MaxRetries  = 3
)
Use code with caution.
Go
六、函数与方法 (Function & Method Naming)
驼峰式 (camelCase): 与变量名类似。
动词或动词短语开头: 函数名应清晰地描述其执行的操作。例如 getUserByID, calculateTotalFee, saveManifest。
简洁: 避免冗余的词语。如果包名已经提供了上下文，函数名可以更简洁。例如，在 user 包中，GetByID 可能比 GetUserByID 更好。
返回布尔值的函数: 通常以 Is, Has, Can 开头。例如 IsUserActive(id int) bool。
构造函数 (Constructor-like functions): 通常命名为 NewTypeName。例如 NewUserService(...) *UserService。
接收者名称 (Receiver Name):
对于结构体的方法，接收者名称应简短（通常是类型名称的单字母或双字母缩写），并在该类型的所有方法中保持一致。
例如，对于 UserService 结构体，接收者可以是 s 或 us：
func (s *UserService) GetUser(id int) (*User, error) {}
Use code with caution.
Go
导出与非导出: 遵循首字母大小写规则。
七、接口 (Interface Naming)
通常以 -er 后缀结尾: 这是 Go 的一个常见约定，如果接口只包含一个核心方法。例如 Reader, Writer, Stringer。
或者描述其行为或角色: 如果接口包含多个方法，或者 -er 后缀不自然，可以使用描述性的名词或名词短语。例如 ManifestRepository, PaymentGateway, NotificationService。
简洁: 避免在接口名前加上 I 前缀（如 IUserService），这不是 Go 的惯例。
导出与非导出: 遵循首字母大小写规则。
八、结构体 (Struct Naming)
驼峰式 (PascalCase): 类型名称首字母大写（如果是导出的）。例如 ManifestItem, CustomerAddress, AppConfig。
名词或名词短语: 描述结构体所代表的实体或概念。
字段名称: 遵循变量命名规则（驼峰式，首字母大小写决定导出性）。
type Order struct {
    ID            string
    CustomerID    string
    Items         []OrderItem
    Status        string
    totalAmount   float64 // 非导出字段
}
Use code with caution.
Go
九、数据库 (Database Naming)
表名 (Table Names):
小写单词，下划线分隔 (snake_case): 这是最常见和推荐的方式。例如 tracking_number_pool, customer_accounts, manifest_items。
复数形式: 通常使用复数形式来表示表中存储的是多个该类型的记录。例如 users, orders。
列名 (Column Names):
小写单词，下划线分隔 (snake_case): 与表名风格一致。例如 express_number, customer_id, created_at, is_active。
主键 (Primary Keys):
通常命名为 id。
外键 (Foreign Keys):
通常命名为 [referenced_table_singular_name]_id。例如，orders 表中引用 customers 表的外键可以是 customer_id。
索引名 (Index Names):
清晰描述索引的用途和包含的列。例如 idx_[table_name]_[column_name] 或 uk_[table_name]_[column_names] (对于唯一索引)。
例如 idx_manifests_customer_id, uk_users_email。
布尔列: 可以使用 is_ 或 has_ 前缀，例如 is_active, has_paid。
十、API 端点 (API Endpoint Naming)
RESTful 风格:
使用名词（通常是复数）表示资源。
使用标准的 HTTP 方法 (GET, POST, PUT, DELETE, PATCH) 表示操作。
URL 路径使用小写，连字符 (-) 分隔 (kebab-case) 或不分隔。 (kebab-case 更常见于 URL 路径，但 Go 路由库可能更倾向于直接用代码中的路径)。
例如：
GET /manifests (获取运单列表)
POST /manifests (创建新运单)
GET /manifests/{manifest_id} (获取特定运单)
PUT /manifests/{manifest_id} (更新特定运单)
GET /customers/{customer_id}/orders (获取某客户的订单)
版本控制: /v1/manifests, /v2/manifests。
十一、测试 (Testing Naming)
测试文件名: 与被测试的文件名对应，以 _test.go 结尾。例如 user_service.go 对应的测试文件是 user_service_test.go。
测试函数名: 以 Test 开头，后面是被测试的函数名或一个描述性的名称，首字母大写。
func TestUserService_GetUserByID_Success(t *testing.T) { ... }
func TestCalculateTotalAmount_WithDiscount(t *testing.T) { ... }
Use code with caution.
Go
总结与工具：
最重要的还是团队内部达成一致并严格遵守。
使用 Linter 工具: Go 提供了优秀的 Linter 工具（如 golangci-lint），它可以配置并检查命名规范和其他代码风格问题，帮助团队保持一致性。

代码审查 (Code Review): 这是确保命名规范被遵守的有效手段。