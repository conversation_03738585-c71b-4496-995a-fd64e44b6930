package service

import (
	"encoding/csv"
	"errors"
	"io"
	"os"
	"regexp"
	"strings"
	"sync"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"golang.org/x/text/encoding/japanese"
	"golang.org/x/text/transform"
)

var (
	// 日本邮编格式：3位数字-4位数字
	japaneseZipCodeRegex = regexp.MustCompile(`^\d{3}-\d{4}$`)
	// 纯数字邮编格式：7位数字
	japaneseDigitsOnlyZipCodeRegex = regexp.MustCompile(`^\d{7}$`)

	// 提取数字的正则表达式
	digitsOnlyRegex = regexp.MustCompile(`\d+`)
)

// AddressInfo 地址信息结构体
type AddressInfo struct {
	ZipCode       string `json:"zipCode"`       // 邮编
	PrefectureJA  string `json:"prefectureJA"`  // 都道府县（日文）
	PrefectureEN  string `json:"prefectureEN"`  // 都道府县（英文）
	CityJA        string `json:"cityJA"`        // 市区町村（日文）
	CityEN        string `json:"cityEN"`        // 市区町村（英文）
	TownJA        string `json:"townJA"`        // 町域名（日文）
	TownEN        string `json:"townEN"`        // 町域名（英文）
	FullAddressJA string `json:"fullAddressJA"` // 完整地址（日文）
	FullAddressEN string `json:"fullAddressEN"` // 完整地址（英文）
}

// ZipCodeService 邮编服务接口
type ZipCodeService interface {
	// IsValidFormat 检查邮编格式是否有效
	IsValidFormat(zipCode string) bool

	// IsExist 检查邮编是否存在
	IsExist(zipCode string) bool

	// NormalizeZipCode 标准化邮编格式(只保留数字)
	NormalizeZipCode(zipCode string) string

	// FormatZipCode 将纯数字邮编格式化为展示格式(XXX-XXXX)
	FormatZipCode(zipCode string) string

	// GetAddressInfo 根据邮编获取地址信息
	GetAddressInfo(zipCode string) (*AddressInfo, error)

	// GetAddressByZipCode 根据邮编获取完整地址（日文）
	GetAddressByZipCode(zipCode string) string

	// IsOkinawaPrefecture 判断是否为冲绳县邮编
	IsOkinawaPrefecture(zipCode string) bool
}

// JapaneseZipCodeService 日本邮编服务实现
type JapaneseZipCodeService struct {
	addressData map[string]*AddressInfo // 邮编->地址信息映射
	loaded      bool                    // 是否已加载数据
	mu          sync.RWMutex            // 读写锁
	logger      *zap.Logger             // 日志
}

// 单例实例
var (
	japaneseZipCodeServiceInstance *JapaneseZipCodeService
	once                           sync.Once
)

// NewJapaneseZipCodeService 创建日本邮编服务
func NewJapaneseZipCodeService(logger *zap.Logger) ZipCodeService {
	once.Do(func() {
		japaneseZipCodeServiceInstance = &JapaneseZipCodeService{
			addressData: make(map[string]*AddressInfo),
			loaded:      false,
			logger:      logger,
		}
		// 初始化时加载邮编数据
		if err := japaneseZipCodeServiceInstance.loadZipCodes(); err != nil {
			logger.Error("Failed to load Japan ZIP codes", zap.Error(err))
		}
	})
	return japaneseZipCodeServiceInstance
}

// NormalizeZipCode 标准化邮编格式(只保留数字)
func (s *JapaneseZipCodeService) NormalizeZipCode(zipCode string) string {
	digits := digitsOnlyRegex.FindAllString(zipCode, -1)
	return strings.Join(digits, "")
}

// FormatZipCode 将纯数字邮编格式化为展示格式(XXX-XXXX)
func (s *JapaneseZipCodeService) FormatZipCode(zipCode string) string {
	// 首先标准化邮编，确保只有数字
	normalizedZip := s.NormalizeZipCode(zipCode)

	// 如果长度为7位，则格式化为XXX-XXXX
	if len(normalizedZip) == 7 {
		return normalizedZip[0:3] + "-" + normalizedZip[3:7]
	}

	// 否则返回原格式
	return zipCode
}

// IsValidFormat 检查邮编格式是否有效 (7位数字，或3位数字-4位数字)
func (s *JapaneseZipCodeService) IsValidFormat(zipCode string) bool {
	// 检查带连字符的格式 (XXX-XXXX)
	if japaneseZipCodeRegex.MatchString(zipCode) {
		return true
	}

	// 或者检查纯数字格式 (XXXXXXX)
	normalized := s.NormalizeZipCode(zipCode)
	return len(normalized) == 7
}

// IsExist 检查邮编是否存在
func (s *JapaneseZipCodeService) IsExist(zipCode string) bool {
	// 如果未加载数据，返回true避免阻止正常流程
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.loaded {
		s.logger.Warn("ZIP code data not loaded, skipping existence check")
		return true
	}

	// 将邮编转为纯数字格式后查询
	normalized := s.NormalizeZipCode(zipCode)
	_, exists := s.addressData[normalized]

	return exists
}

// GetAddressInfo 根据邮编获取地址信息
func (s *JapaneseZipCodeService) GetAddressInfo(zipCode string) (*AddressInfo, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.loaded {
		return nil, errors.New("ZIP code data not loaded")
	}

	normalized := s.NormalizeZipCode(zipCode)
	if info, exists := s.addressData[normalized]; exists {
		return info, nil
	}

	return nil, errors.New("ZIP code not found")
}

// GetAddressByZipCode 根据邮编获取完整地址（日文）
func (s *JapaneseZipCodeService) GetAddressByZipCode(zipCode string) string {
	info, err := s.GetAddressInfo(zipCode)
	if err != nil {
		return ""
	}
	return info.FullAddressJA
}

// IsOkinawaPrefecture 判断是否为冲绳县邮编
func (s *JapaneseZipCodeService) IsOkinawaPrefecture(zipCode string) bool {
	info, err := s.GetAddressInfo(zipCode)
	if err != nil {
		// 如果无法获取地址信息，则通过邮编前缀判断（备用方案）
		normalized := s.NormalizeZipCode(zipCode)
		if len(normalized) >= 3 {
			prefix := normalized[:3]
			// 冲绳县邮编前缀：900-907
			return prefix >= "900" && prefix <= "907"
		}
		return false
	}

	// 通过都道府县名称判断
	return strings.Contains(info.PrefectureJA, "沖縄") ||
		strings.Contains(info.PrefectureEN, "Okinawa")
}

// loadZipCodes 从CSV文件加载邮编数据
func (s *JapaneseZipCodeService) loadZipCodes() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 从配置获取文件路径，默认值为data/zipcode/japan_zipcode.csv
	filePath := viper.GetString("zipcode.japan.filepath")
	if filePath == "" {
		filePath = "data/zipcode/japan_zipcode.csv"
	}

	// 打开CSV文件
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用Shift-JIS解码器创建转换读取器
	reader := transform.NewReader(file, japanese.ShiftJIS.NewDecoder())

	// 创建CSV读取器
	csvReader := csv.NewReader(reader)
	csvReader.FieldsPerRecord = -1 // 允许可变字段数

	// 逐行读取CSV文件，不跳过第一行（没有标题行）
	zipCount := 0
	lineNumber := 0
	for {
		lineNumber++
		record, err := csvReader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			s.logger.Warn("Error reading CSV row", zap.Error(err), zap.Int("line", lineNumber))
			continue
		}

		// 确保记录至少有7个字段
		if len(record) < 7 {
			s.logger.Debug("CSV record has insufficient fields", zap.Int("fields", len(record)), zap.Int("line", lineNumber))
			continue
		}

		// 解析CSV记录到地址信息
		addressInfo, err := s.parseCSVRecord(record)
		if err != nil {
			s.logger.Debug("Failed to parse CSV record", zap.Error(err), zap.Int("line", lineNumber))
			continue
		}

		// 验证邮编不为空
		if addressInfo.ZipCode == "" {
			s.logger.Debug("Empty ZIP code", zap.Int("line", lineNumber))
			continue
		}

		// 验证邮编格式
		if !japaneseDigitsOnlyZipCodeRegex.MatchString(addressInfo.ZipCode) {
			// 如果不是7位数字格式，尝试标准化提取数字
			normalizedZip := s.NormalizeZipCode(addressInfo.ZipCode)
			if len(normalizedZip) != 7 {
				s.logger.Debug("Invalid ZIP code length after normalization",
					zap.String("zipCode", addressInfo.ZipCode),
					zap.String("normalized", normalizedZip),
					zap.Int("line", lineNumber))
				continue
			}
			addressInfo.ZipCode = normalizedZip
		}

		// 跳过空地址信息的记录
		if addressInfo.PrefectureJA == "" && addressInfo.CityJA == "" && addressInfo.TownJA == "" {
			s.logger.Debug("Empty address info", zap.String("zipCode", addressInfo.ZipCode), zap.Int("line", lineNumber))
			continue
		}

		// 添加到地址数据映射
		s.addressData[addressInfo.ZipCode] = addressInfo
		zipCount++

		// 每10000条记录输出一次进度日志
		if zipCount%10000 == 0 {
			s.logger.Info("Loading ZIP codes progress", zap.Int("loaded", zipCount), zap.Int("line", lineNumber))
		}
	}

	if zipCount == 0 {
		return errors.New("no valid ZIP codes found in the file")
	}

	s.loaded = true
	s.logger.Info("Loaded Japan ZIP codes with address data", zap.Int("count", zipCount))
	return nil
}

// parseCSVRecord 解析CSV记录为地址信息
// 实际CSV格式：邮编,都道府县(日),市区町村(日),町域(日),都道府県(英),市区町村(英),町域(英)
func (s *JapaneseZipCodeService) parseCSVRecord(record []string) (*AddressInfo, error) {
	// 确保至少有邮编字段
	if len(record) == 0 {
		return nil, errors.New("empty CSV record")
	}

	// 清理双引号和空格
	for i := range record {
		record[i] = strings.Trim(strings.TrimSpace(record[i]), "\"")
	}

	addressInfo := &AddressInfo{
		ZipCode: record[0],
	}

	// 根据实际CSV格式解析字段：邮编,都道府县(日),市区町村(日),町域(日),都道府県(英),市区町村(英),町域(英)
	if len(record) > 1 {
		addressInfo.PrefectureJA = record[1]
	}
	if len(record) > 2 {
		addressInfo.CityJA = record[2]
	}
	if len(record) > 3 {
		addressInfo.TownJA = record[3]
	}
	if len(record) > 4 {
		addressInfo.PrefectureEN = record[4]
	}
	if len(record) > 5 {
		addressInfo.CityEN = record[5]
	}
	if len(record) > 6 {
		addressInfo.TownEN = record[6]
	}

	// 生成完整地址
	if addressInfo.PrefectureJA != "" || addressInfo.CityJA != "" || addressInfo.TownJA != "" {
		parts := []string{}
		if addressInfo.PrefectureJA != "" {
			parts = append(parts, addressInfo.PrefectureJA)
		}
		if addressInfo.CityJA != "" {
			parts = append(parts, addressInfo.CityJA)
		}
		if addressInfo.TownJA != "" {
			parts = append(parts, addressInfo.TownJA)
		}
		addressInfo.FullAddressJA = strings.Join(parts, "")
	}

	if addressInfo.PrefectureEN != "" || addressInfo.CityEN != "" || addressInfo.TownEN != "" {
		parts := []string{}
		if addressInfo.TownEN != "" && addressInfo.TownEN != "IKANIKEISAIGANA" {
			parts = append(parts, addressInfo.TownEN)
		}
		if addressInfo.CityEN != "" {
			parts = append(parts, addressInfo.CityEN)
		}
		if addressInfo.PrefectureEN != "" {
			parts = append(parts, addressInfo.PrefectureEN)
		}
		addressInfo.FullAddressEN = strings.Join(parts, ", ")
	}

	return addressInfo, nil
}
