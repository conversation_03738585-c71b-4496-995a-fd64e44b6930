package service

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

// GenerateBillingRequest 生成账单请求
type GenerateBillingRequest struct {
	StartTime         string                      `json:"startTime" binding:"required"`      // 开始时间，格式：yyyy-MM-dd HH:mm:ss
	EndTime           string                      `json:"endTime" binding:"required"`        // 结束时间，格式：yyyy-MM-dd HH:mm:ss
	UserID            int64                       `json:"userId" binding:"required"`         // 用户ID
	BillingCycleID    int64                       `json:"billingCycleId" binding:"required"` // 所属账期批次ID
	GeneratedByUserID *int64                      `json:"generatedByUserId,omitempty"`       // 账单生成操作员ID
	GeneralTemplate   *entity.ShippingFeeTemplate `json:"generalTemplate,omitempty"`         // 普货模板信息
	BatteryTemplate   *entity.ShippingFeeTemplate `json:"batteryTemplate,omitempty"`         // 带电货物模板信息
	PostBoxTemplate   *entity.ShippingFeeTemplate `json:"postBoxTemplate,omitempty"`         // 投函货物模板信息
	DueDate           *string                     `json:"dueDate,omitempty"`                 // 付款截止日期，格式：yyyy-MM-dd
	Notes             *string                     `json:"notes,omitempty"`                   // 账单备注
	Currency          string                      `json:"currency" binding:"required"`       // 货币单位，默认CNY
}

// GenerateBillingResponse 生成账单响应
type GenerateBillingResponse struct {
	BillingRecordID int64   `json:"billingRecordId"` // 生成的账单ID
	BillNumber      string  `json:"billNumber"`      // 账单编号
	TotalAmount     float64 `json:"totalAmount"`     // 账单总金额
	ItemsCount      int     `json:"itemsCount"`      // 明细项数量
	Message         string  `json:"message"`         // 响应消息
}

// ListBillingRecordsRequest 分页查询账单记录请求
type ListBillingRecordsRequest struct {
	Page               int      `form:"page" json:"page" default:"1"`                            // 页码
	PageSize           int      `form:"pageSize" json:"pageSize" binding:"max=100" default:"10"` // 每页数量
	CustomerAccountID  *int64   `form:"customerAccountId" json:"customerAccountId,omitempty"`    // 客户账户ID（可选）
	BillingCycleID     *int64   `form:"billingCycleId" json:"billingCycleId,omitempty"`          // 账期批次ID（可选）
	Status             *string  `form:"status" json:"status,omitempty"`                          // 账单状态（可选）
	BillNumber         *string  `form:"billNumber" json:"billNumber,omitempty"`                  // 账单编号（可选，模糊查询）
	BillDateStart      *string  `form:"billDateStart" json:"billDateStart,omitempty"`            // 账单日期开始（格式：yyyy-MM-dd）
	BillDateEnd        *string  `form:"billDateEnd" json:"billDateEnd,omitempty"`                // 账单日期结束（格式：yyyy-MM-dd）
	BillingPeriodStart *string  `form:"billingPeriodStart" json:"billingPeriodStart,omitempty"`  // 账期开始日期（格式：yyyy-MM-dd）
	BillingPeriodEnd   *string  `form:"billingPeriodEnd" json:"billingPeriodEnd,omitempty"`      // 账期结束日期（格式：yyyy-MM-dd）
	MinAmount          *float64 `form:"minAmount" json:"minAmount,omitempty"`                    // 最小金额（可选）
	MaxAmount          *float64 `form:"maxAmount" json:"maxAmount,omitempty"`                    // 最大金额（可选）
	Currency           *string  `form:"currency" json:"currency,omitempty"`                      // 货币单位（可选）
}

// BillingRecordDTO 账单记录DTO
type BillingRecordDTO struct {
	ID                   int64   `json:"id"`                             // 账单主键ID
	BillNumber           string  `json:"billNumber"`                     // 账单编号
	CustomerAccountID    int64   `json:"customerAccountId"`              // 客户账户ID
	BillingCycleID       int64   `json:"billingCycleId"`                 // 所属账期批次ID
	CustomerNickname     string  `json:"customerNickname"`               // 客户昵称
	CustomerUsername     string  `json:"customerUsername"`               // 客户用户名
	BillDate             string  `json:"billDate"`                       // 账单日期（yyyy-MM-dd格式）
	DueDate              *string `json:"dueDate,omitempty"`              // 付款截止日期（yyyy-MM-dd格式）
	BillingPeriodStart   string  `json:"billingPeriodStart"`             // 账期开始日期（yyyy-MM-dd HH:mm:ss格式）
	BillingPeriodEnd     string  `json:"billingPeriodEnd"`               // 账期结束日期（yyyy-MM-dd HH:mm:ss格式）
	TotalAmount          float64 `json:"totalAmount"`                    // 账单总金额
	AmountPaid           float64 `json:"amountPaid"`                     // 已付金额
	BalanceDue           float64 `json:"balanceDue"`                     // 应付余额
	Currency             string  `json:"currency"`                       // 货币单位
	Status               string  `json:"status"`                         // 账单状态
	PaymentMethod        *string `json:"paymentMethod,omitempty"`        // 支付方式
	PaymentTransactionID *string `json:"paymentTransactionId,omitempty"` // 支付交易号
	PaymentDate          *string `json:"paymentDate,omitempty"`          // 支付日期（yyyy-MM-dd HH:mm:ss格式）
	Notes                *string `json:"notes,omitempty"`                // 账单备注
	GeneratedByUserID    *int64  `json:"generatedByUserId,omitempty"`    // 账单生成操作员ID
	GeneratedByNickname  *string `json:"generatedByNickname,omitempty"`  // 账单生成操作员昵称
	CreateTime           string  `json:"createTime"`                     // 记录创建时间（yyyy-MM-dd HH:mm:ss格式）
	UpdateTime           string  `json:"updateTime"`                     // 记录更新时间（yyyy-MM-dd HH:mm:ss格式）
}

// ListBillingRecordsResponse 分页查询账单记录响应
type ListBillingRecordsResponse struct {
	Total int64               `json:"total"` // 总数
	List  []*BillingRecordDTO `json:"list"`  // 账单记录列表
}

// GetUsersForBillingRequest 查询可生成账单用户列表请求
type GetUsersForBillingRequest struct {
	StartTime string `form:"startTime" json:"startTime" binding:"required"` // 开始时间，格式：yyyy-MM-dd HH:mm:ss
	EndTime   string `form:"endTime" json:"endTime" binding:"required"`     // 结束时间，格式：yyyy-MM-dd HH:mm:ss
}

// BillingUserDTO 可生成账单的用户DTO
type BillingUserDTO struct {
	UserID          int64  `json:"userId"`          // 用户ID
	Nickname        string `json:"nickname"`        // 用户昵称
	Username        string `json:"username"`        // 用户名
	ManifestCount   int64  `json:"manifestCount"`   // 可生成账单的运单数量
	AdjustmentCount int64  `json:"adjustmentCount"` // 财务调整记录数量
	TotalCount      int64  `json:"totalCount"`      // 总数量（运单+调整记录）
	HasManifests    bool   `json:"hasManifests"`    // 是否有运单记录
	HasAdjustments  bool   `json:"hasAdjustments"`  // 是否有调整记录
}

// GetUsersForBillingResponse 查询可生成账单用户列表响应
type GetUsersForBillingResponse struct {
	Total int64             `json:"total"` // 总用户数
	List  []*BillingUserDTO `json:"list"`  // 用户列表
}

// ListBillingRecordItemsRequest 查询账单明细列表请求
type ListBillingRecordItemsRequest struct {
	BillingRecordID int64 `uri:"billingRecordId" binding:"required"`                       // 账单记录ID
	Page            int   `form:"page" json:"page" default:"1"`                            // 页码
	PageSize        int   `form:"pageSize" json:"pageSize" binding:"max=100" default:"20"` // 每页数量
}

// BillingRecordItemDTO 账单明细DTO
type BillingRecordItemDTO struct {
	ID                        int64   `json:"id"`                                  // 账单明细主键ID
	BillingRecordID           int64   `json:"billingRecordId"`                     // 所属账单ID
	ManifestID                *int64  `json:"manifestId,omitempty"`                // 原始运单ID
	ExpressNumber             *string `json:"expressNumber,omitempty"`             // 快递单号
	OrderNo                   *string `json:"orderNo,omitempty"`                   // 系统订单号
	TransferredTrackingNumber *string `json:"transferredTrackingNumber,omitempty"` // 转单号
	OrderNumber               *string `json:"orderNumber,omitempty"`               // 客户订单号
	ManifestCreateTime        *string `json:"manifestCreateTime,omitempty"`        // 运单创建时间（yyyy-MM-dd HH:mm:ss格式）
	ShipmentTime              *string `json:"shipmentTime,omitempty"`              // 发货时间（yyyy-MM-dd HH:mm:ss格式）

	ReceiverName        *string  `json:"receiverName,omitempty"`        // 收件人姓名
	ItemDescription     string   `json:"itemDescription"`               // 物品名称
	CargoType           int      `json:"cargoType"`                     // 货物类型：1-普通货物；2-带电货物；3-投函货物
	CargoTypeName       string   `json:"cargoTypeName"`                 // 货物类型名称
	Weight              *float64 `json:"weight,omitempty"`              // 实际重量(KG)
	Length              *float64 `json:"length,omitempty"`              // 长(cm)
	Width               *float64 `json:"width,omitempty"`               // 宽(cm)
	Height              *float64 `json:"height,omitempty"`              // 高(cm)
	SumOfSides          *float64 `json:"sumOfSides,omitempty"`          // 三边和(cm)
	DimensionalWeight   *float64 `json:"dimensionalWeight,omitempty"`   // 体积重(KG)
	ChargeableWeight    *float64 `json:"chargeableWeight,omitempty"`    // 计费重量(KG)
	BaseFreightFee      *float64 `json:"baseFreightFee,omitempty"`      // 基础运费
	FirstWeightFee      *float64 `json:"firstWeightFee,omitempty"`      // 首重费用
	ContinuedWeightFee  *float64 `json:"continuedWeightFee,omitempty"`  // 续重费用
	OverLengthSurcharge *float64 `json:"overLengthSurcharge,omitempty"` // 超长费
	RemoteAreaSurcharge *float64 `json:"remoteAreaSurcharge,omitempty"` // 偏远费
	OverweightSurcharge *float64 `json:"overweightSurcharge,omitempty"` // 超重费
	ItemTotalAmount     float64  `json:"itemTotalAmount"`               // 总费用
	CreateTime          string   `json:"createTime"`                    // 创建时间（yyyy-MM-dd HH:mm:ss格式）
	UpdateTime          string   `json:"updateTime"`                    // 更新时间（yyyy-MM-dd HH:mm:ss格式）
}

// ListBillingRecordItemsResponse 查询账单明细列表响应
type ListBillingRecordItemsResponse struct {
	Total int64                   `json:"total"` // 总数
	List  []*BillingRecordItemDTO `json:"list"`  // 账单明细列表
}

// GetBillingRecordDetailRequest 获取账单记录详情请求
type GetBillingRecordDetailRequest struct {
	BillingRecordID int64 `uri:"billingRecordId" binding:"required"` // 账单记录ID
}

// BillingRecordDetailDTO 账单记录详情DTO
type BillingRecordDetailDTO struct {
	ID                      int64                          `json:"id"`                             // 账单主键ID
	BillNumber              string                         `json:"billNumber"`                     // 账单编号
	CustomerAccountID       int64                          `json:"customerAccountId"`              // 客户账户ID
	BillingCycleID          int64                          `json:"billingCycleId"`                 // 所属账期批次ID
	CustomerNickname        string                         `json:"customerNickname"`               // 客户昵称
	CustomerUsername        string                         `json:"customerUsername"`               // 客户用户名
	BillDate                string                         `json:"billDate"`                       // 账单日期（yyyy-MM-dd格式）
	DueDate                 *string                        `json:"dueDate,omitempty"`              // 付款截止日期（yyyy-MM-dd格式）
	BillingPeriodStart      string                         `json:"billingPeriodStart"`             // 账期开始日期（yyyy-MM-dd HH:mm:ss格式）
	BillingPeriodEnd        string                         `json:"billingPeriodEnd"`               // 账期结束日期（yyyy-MM-dd HH:mm:ss格式）
	AppliedFreightTemplates *entity.AppliedFreightTemplate `json:"appliedFreightTemplates"`        // 应用的运费模板信息
	FreightChargesTotal     float64                        `json:"freightChargesTotal"`            // 运费明细总费用
	AdjustmentChargesTotal  float64                        `json:"adjustmentChargesTotal"`         // 调整明细总费用
	TotalAmount             float64                        `json:"totalAmount"`                    // 账单总金额
	AmountPaid              float64                        `json:"amountPaid"`                     // 已付金额
	BalanceDue              float64                        `json:"balanceDue"`                     // 应付余额
	Currency                string                         `json:"currency"`                       // 货币单位
	Status                  string                         `json:"status"`                         // 账单状态
	StatusName              string                         `json:"statusName"`                     // 账单状态名称
	PaymentMethod           *string                        `json:"paymentMethod,omitempty"`        // 支付方式
	PaymentTransactionID    *string                        `json:"paymentTransactionId,omitempty"` // 支付交易号
	PaymentDate             *string                        `json:"paymentDate,omitempty"`          // 支付日期（yyyy-MM-dd HH:mm:ss格式）
	Notes                   *string                        `json:"notes,omitempty"`                // 账单备注
	GeneratedByUserID       *int64                         `json:"generatedByUserId,omitempty"`    // 账单生成操作员ID
	GeneratedByNickname     *string                        `json:"generatedByNickname,omitempty"`  // 账单生成操作员昵称
	CreateTime              string                         `json:"createTime"`                     // 记录创建时间（yyyy-MM-dd HH:mm:ss格式）
	UpdateTime              string                         `json:"updateTime"`                     // 记录更新时间（yyyy-MM-dd HH:mm:ss格式）
}

// GetBillingRecordDetailResponse 获取账单记录详情响应
type GetBillingRecordDetailResponse struct {
	BillingRecord *BillingRecordDetailDTO `json:"billingRecord"` // 账单记录详情
}

// ListBillingAdjustmentSnapshotsRequest 查询账单调整明细列表请求
type ListBillingAdjustmentSnapshotsRequest struct {
	BillingRecordID int64 `uri:"billingRecordId" binding:"required"`                       // 账单记录ID
	Page            int   `form:"page" json:"page" default:"1"`                            // 页码
	PageSize        int   `form:"pageSize" json:"pageSize" binding:"max=100" default:"20"` // 每页数量
}

// BillingAdjustmentSnapshotDTO 账单调整明细DTO
type BillingAdjustmentSnapshotDTO struct {
	ID                   int64 `json:"id"`                   // 快照主键ID
	BillingRecordID      int64 `json:"billingRecordId"`      // 关联的账单ID
	OriginalAdjustmentID int64 `json:"originalAdjustmentId"` // 原始财务调整ID

	// 财务调整信息快照
	AdjustmentType     string                 `json:"adjustmentType"`              // 调整类型
	AdjustmentTypeName string                 `json:"adjustmentTypeName"`          // 调整类型名称
	Description        *string                `json:"description,omitempty"`       // 调整描述/原因
	AdditionalDetails  map[string]interface{} `json:"additionalDetails,omitempty"` // 特定调整类型的附加详情
	Amount             float64                `json:"amount"`                      // 调整金额
	Currency           string                 `json:"currency"`                    // 货币单位
	EffectiveDate      string                 `json:"effectiveDate"`               // 费用实际发生/确认日期（yyyy-MM-dd格式）

	// 关联运单信息快照
	ManifestID                        *int64  `json:"manifestId,omitempty"`
	ManifestExpressNumber             *string `json:"manifestExpressNumber,omitempty"`
	ManifestOrderNo                   *string `json:"manifestOrderNo,omitempty"`
	ManifestTransferredTrackingNumber *string `json:"manifestTransferredTrackingNumber,omitempty"`
	ManifestCustomerOrderNumber       *string `json:"manifestCustomerOrderNumber,omitempty"`
	ManifestCreateTime                *string `json:"manifestCreateTime,omitempty"`   // 运单创建时间（yyyy-MM-dd HH:mm:ss格式）
	ManifestShipmentTime              *string `json:"manifestShipmentTime,omitempty"` // 发货时间（yyyy-MM-dd HH:mm:ss格式）
	ManifestReceiverName              *string `json:"manifestReceiverName,omitempty"`
	ManifestItemDescription           *string `json:"manifestItemDescription,omitempty"`
	ManifestCargoType                 *string `json:"manifestCargoType,omitempty"`

	// 运单尺寸重量信息快照
	ManifestWeight            *float64 `json:"manifestWeight,omitempty"`
	ManifestLength            *float64 `json:"manifestLength,omitempty"`
	ManifestWidth             *float64 `json:"manifestWidth,omitempty"`
	ManifestHeight            *float64 `json:"manifestHeight,omitempty"`
	ManifestSumOfSides        *float64 `json:"manifestSumOfSides,omitempty"`
	ManifestDimensionalWeight *float64 `json:"manifestDimensionalWeight,omitempty"`
	ManifestChargeableWeight  *float64 `json:"manifestChargeableWeight,omitempty"`

	// 快照创建时间
	SnapshotTime string `json:"snapshotTime"` // 快照创建时间（yyyy-MM-dd HH:mm:ss格式）
}

// ListBillingAdjustmentSnapshotsResponse 查询账单调整明细列表响应
type ListBillingAdjustmentSnapshotsResponse struct {
	Total int64                           `json:"total"` // 总数
	List  []*BillingAdjustmentSnapshotDTO `json:"list"`  // 调整明细列表
}

// ListBillingGenerationTasksRequest 查询账单生成任务列表请求
type ListBillingGenerationTasksRequest struct {
	BillingCycleID int64 `form:"billingCycleId" json:"billingCycleId" binding:"required"` // 账期批次ID（必须）
	Page           int   `form:"page" json:"page" default:"1"`                            // 页码
	PageSize       int   `form:"pageSize" json:"pageSize" binding:"max=100" default:"20"` // 每页数量
}

// BillingGenerationTaskDTO 账单生成任务DTO
type BillingGenerationTaskDTO struct {
	TaskID              string  `json:"taskId"`              // 任务UUID
	BillingCycleID      int64   `json:"billingCycleId"`      // 关联的账期批次ID
	TargetCustomerIds   *string `json:"targetCustomerIds"`   // 目标客户ID列表
	Status              string  `json:"status"`              // 任务状态
	StatusName          string  `json:"statusName"`          // 任务状态名称
	ProgressPercentage  int     `json:"progressPercentage"`  // 进度百分比（0-100）
	TotalItemsToProcess *int    `json:"totalItemsToProcess"` // 预计总处理项数
	ItemsProcessedCount int     `json:"itemsProcessedCount"` // 已处理项数
	ErrorMessage        *string `json:"errorMessage"`        // 错误信息
	SubmittedByUserID   *int64  `json:"submittedByUserId"`   // 任务提交者ID
	SubmittedByNickname *string `json:"submittedByNickname"` // 任务提交者昵称
	SubmitTime          string  `json:"submitTime"`          // 提交时间（yyyy-MM-dd HH:mm:ss格式）
	StartTime           *string `json:"startTime"`           // 处理开始时间（yyyy-MM-dd HH:mm:ss格式）
	EndTime             *string `json:"endTime"`             // 处理结束时间（yyyy-MM-dd HH:mm:ss格式）
	Duration            *int64  `json:"duration"`            // 执行耗时（秒）
}

// ListBillingGenerationTasksResponse 查询账单生成任务列表响应
type ListBillingGenerationTasksResponse struct {
	Total int64                       `json:"total"` // 总数
	List  []*BillingGenerationTaskDTO `json:"list"`  // 任务列表
}

// BillingServiceImpl 财务账单服务实现
type BillingServiceImpl struct {
	billingRepo                     repository.BillingRepository
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository
	userRepo                        repository.UserRepository
	shippingFeeTemplateRepo         repository.ShippingFeeTemplateRepository
	billingGenerationTaskRepo       repository.BillingGenerationTaskRepository
	billingCycleRepo                repository.BillingCycleRepository
	billingProducer                 repository.MessageProducer
}

// NewBillingService 创建财务账单服务
func NewBillingService(
	billingRepo repository.BillingRepository,
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository,
	userRepo repository.UserRepository,
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository,
	billingGenerationTaskRepo repository.BillingGenerationTaskRepository,
	billingCycleRepo repository.BillingCycleRepository,
	billingProducer repository.MessageProducer,
) BillingService {
	return &BillingServiceImpl{
		billingRepo:                     billingRepo,
		manifestFinancialAdjustmentRepo: manifestFinancialAdjustmentRepo,
		userRepo:                        userRepo,
		shippingFeeTemplateRepo:         shippingFeeTemplateRepo,
		billingGenerationTaskRepo:       billingGenerationTaskRepo,
		billingCycleRepo:                billingCycleRepo,
		billingProducer:                 billingProducer,
	}
}

// GenerateBilling 生成账单
func (s *BillingServiceImpl) GenerateBilling(ctx context.Context, req *GenerateBillingRequest) (*GenerateBillingResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证和转换（固定使用发货时间）
	startTime, endTime, dueDate, err := s.validateAndParseGenerateBillingRequest(req)
	if err != nil {
		if logger != nil {
			logger.Warn("Invalid billing request parameters", zap.Error(err))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 构建应用的运费模板信息
	appliedTemplates := &entity.AppliedFreightTemplate{
		GeneralTemplate: req.GeneralTemplate,
		BatteryTemplate: req.BatteryTemplate,
		PostBoxTemplate: req.PostBoxTemplate,
	}

	// 2.1 检查模板配置，如果用户没有配置模板，尝试查询系统默认模板
	if appliedTemplates.GeneralTemplate == nil && appliedTemplates.BatteryTemplate == nil && appliedTemplates.PostBoxTemplate == nil {
		// 如果用户请求中没有提供模板，尝试查询系统默认模板
		userTemplates, err := s.shippingFeeTemplateRepo.GetUserAllTemplates(ctx, req.UserID)
		if err != nil {
			if logger != nil {
				logger.Error("Failed to get user templates", zap.Error(err))
			}
			return nil, valueobject.ERROR_UNKNOWN, errors.New("查询用户模板失败")
		}

		// 应用系统默认模板
		// 由于删除了Type字段，这里简化处理：使用第一个可用模板作为通用模板
		if len(userTemplates) > 0 && userTemplates[0] != nil {
			appliedTemplates.GeneralTemplate = userTemplates[0]
		}
	}

	// 3. 查询符合条件的运单（固定使用发货时间）
	manifests, err := s.billingRepo.FindManifestsForBilling(ctx, entity.BillingTimeTypeShipmentTime, startTime, endTime, req.UserID)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find manifests for billing", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询运单数据失败")
	}

	if len(manifests) == 0 {
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("指定条件下没有找到符合的运单记录")
	}

	// 3.1 智能检查模板配置 - 只检查实际需要的模板类型
	var isTemplateError bool
	var templateErrorMsg string

	// 分析运单实际需要的模板类型
	requiredTemplateTypes := s.analyzeRequiredTemplateTypes(manifests)
	missingTemplates := s.checkMissingTemplates(appliedTemplates, requiredTemplateTypes)

	if len(missingTemplates) > 0 {
		isTemplateError = true
		templateErrorMsg = fmt.Sprintf("用户缺少必需的运费模板：%s", strings.Join(missingTemplates, "、"))
		if logger != nil {
			logger.Warn("用户缺少必需的运费模板，将创建异常状态账单",
				zap.Int64("userId", req.UserID),
				zap.Strings("missingTemplates", missingTemplates),
				zap.Ints("requiredTypes", requiredTemplateTypes))
		}
	}

	// 4. 生成账单编号
	billNumber, err := s.billingRepo.GenerateBillNumber(ctx)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to generate bill number", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("生成账单编号失败")
	}

	// 5. 创建账单主记录
	// 确定账单状态，如果模板有问题则设为异常状态
	billingStatus := entity.BillingStatusUnpaid
	var notes *string
	if isTemplateError {
		billingStatus = entity.BillingStatusError
		notes = &templateErrorMsg
	} else if req.Notes != nil {
		notes = req.Notes
	}

	billingRecord := &entity.BillingRecord{
		BillNumber:              billNumber,
		CustomerAccountID:       req.UserID,
		BillingCycleID:          req.BillingCycleID,
		BillDate:                time.Now(),
		DueDate:                 dueDate,
		BillingPeriodStart:      startTime,
		BillingPeriodEnd:        endTime,
		AppliedFreightTemplates: appliedTemplates,
		FreightChargesTotal:     0, // 将在后续计算
		AdjustmentChargesTotal:  0, // 将在后续计算
		TotalAmount:             0, // 将在后续计算
		AmountPaid:              0,
		Currency:                req.Currency,
		Status:                  billingStatus,
		Notes:                   notes,
		GeneratedByUserID:       req.GeneratedByUserID, // 设置账单生成操作员ID
		CreateTime:              time.Now(),
		UpdateTime:              time.Now(),
	}

	// 6. 保存账单主记录
	if err := s.billingRepo.SaveBillingRecord(ctx, billingRecord); err != nil {
		if logger != nil {
			logger.Error("Failed to save billing record", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("保存账单记录失败")
	}

	// 7. 生成账单明细
	var billingItems []*entity.BillingRecordItem
	var totalAmount float64

	if isTemplateError {
		// 如果是模板错误导致的异常状态，不生成明细项，但记录错误信息
		if logger != nil {
			logger.Warn("账单因模板配置异常跳过明细生成",
				zap.Int64("billingRecordId", billingRecord.ID),
				zap.String("billNumber", billNumber),
				zap.String("errorMessage", templateErrorMsg))
		}
		billingItems = []*entity.BillingRecordItem{} // 空的明细列表
		totalAmount = 0
	} else {
		// 正常生成账单明细
		var err error
		billingItems, totalAmount, err = s.generateBillingItems(ctx, billingRecord.ID, manifests, appliedTemplates)
		if err != nil {
			if logger != nil {
				logger.Error("Failed to generate billing items", zap.Error(err))
			}
			return nil, valueobject.ERROR_UNKNOWN, err
		}
	}

	// 8. 保存账单明细（支持分批处理大量数据）
	if logger != nil {
		logger.Info("开始保存账单明细",
			zap.Int("itemsCount", len(billingItems)),
			zap.Int64("billingRecordId", billingRecord.ID))
	}

	if err := s.billingRepo.SaveBillingRecordItems(ctx, billingItems); err != nil {
		if logger != nil {
			logger.Error("Failed to save billing record items",
				zap.Int("itemsCount", len(billingItems)),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("保存账单明细失败")
	}

	if logger != nil {
		logger.Info("账单明细保存成功",
			zap.Int("itemsCount", len(billingItems)),
			zap.Int64("billingRecordId", billingRecord.ID))
	}

	// 9. 查询并保存财务调整快照，并获取调整费用总额
	adjustmentTotalAmount, err := s.generateFinancialAdjustmentSnapshots(ctx, billingRecord.ID, req.UserID, startTime, endTime)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to generate financial adjustment snapshots", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 10. 计算最终总金额（运单费用 + 调整费用）
	billingRecord.FreightChargesTotal = totalAmount
	billingRecord.AdjustmentChargesTotal = adjustmentTotalAmount
	finalTotalAmount := totalAmount + adjustmentTotalAmount
	billingRecord.TotalAmount = finalTotalAmount
	billingRecord.BalanceDue = finalTotalAmount
	if err := s.billingRepo.SaveBillingRecord(ctx, billingRecord); err != nil {
		if logger != nil {
			logger.Error("Failed to update billing record total amount", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("更新账单总金额失败")
	}

	if logger != nil {
		logger.Info("Successfully generated billing record",
			zap.Int64("billingRecordId", billingRecord.ID),
			zap.String("billNumber", billNumber),
			zap.Float64("manifestTotalAmount", totalAmount),
			zap.Float64("adjustmentTotalAmount", adjustmentTotalAmount),
			zap.Float64("finalTotalAmount", finalTotalAmount),
			zap.Int("itemsCount", len(billingItems)))
	}

	// 11. 更新账期批次统计信息
	if err := s.billingCycleRepo.UpdateBillingCycleStatistics(ctx, billingRecord.BillingCycleID, billingRecord.CustomerAccountID, 1, finalTotalAmount); err != nil {
		if logger != nil {
			logger.Warn("Failed to update billing cycle statistics",
				zap.Int64("billingCycleId", billingRecord.BillingCycleID),
				zap.Int64("customerId", billingRecord.CustomerAccountID),
				zap.Float64("amount", finalTotalAmount),
				zap.Error(err))
		}
		// 统计信息更新失败不影响账单生成的成功，只记录警告
	}

	return &GenerateBillingResponse{
		BillingRecordID: billingRecord.ID,
		BillNumber:      billNumber,
		TotalAmount:     finalTotalAmount,
		ItemsCount:      len(billingItems),
		Message: fmt.Sprintf("成功生成账单，包含 %d 条明细记录，运单费用: %.2f元，调整费用: %.2f元，总计: %.2f元",
			len(billingItems), totalAmount, adjustmentTotalAmount, finalTotalAmount),
	}, valueobject.SUCCESS, nil
}

// ListBillingRecords 分页查询账单记录
func (s *BillingServiceImpl) ListBillingRecords(ctx context.Context, req *ListBillingRecordsRequest) (*ListBillingRecordsResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证和时间解析
	filters, err := s.parseListBillingRecordsFilters(req)
	if err != nil {
		if logger != nil {
			logger.Warn("Invalid list billing records request parameters", zap.Error(err))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 查询账单记录总数
	total, err := s.billingRepo.CountBillingRecords(ctx, filters)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to count billing records", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账单记录总数失败")
	}

	// 3. 分页查询账单记录
	records, err := s.billingRepo.FindBillingRecords(ctx, filters, req.Page, req.PageSize)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find billing records", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账单记录失败")
	}

	// 4. 收集用户ID并批量查询用户信息
	userIDs := make([]int64, 0)
	userIDSet := make(map[int64]bool)

	for _, record := range records {
		// 客户账户ID
		if !userIDSet[record.CustomerAccountID] {
			userIDs = append(userIDs, record.CustomerAccountID)
			userIDSet[record.CustomerAccountID] = true
		}
		// 账单生成操作员ID
		if record.GeneratedByUserID != nil && !userIDSet[*record.GeneratedByUserID] {
			userIDs = append(userIDs, *record.GeneratedByUserID)
			userIDSet[*record.GeneratedByUserID] = true
		}
	}

	// 5. 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find users by IDs", zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 6. 构建响应DTO
	dtos := make([]*BillingRecordDTO, len(records))
	for i, record := range records {
		dto := &BillingRecordDTO{
			ID:                   record.ID,
			BillNumber:           record.BillNumber,
			CustomerAccountID:    record.CustomerAccountID,
			BillingCycleID:       record.BillingCycleID,
			BillDate:             record.BillDate.Format("2006-01-02"),
			BillingPeriodStart:   record.BillingPeriodStart.Format("2006-01-02 15:04:05"),
			BillingPeriodEnd:     record.BillingPeriodEnd.Format("2006-01-02 15:04:05"),
			TotalAmount:          record.TotalAmount,
			AmountPaid:           record.AmountPaid,
			BalanceDue:           record.BalanceDue,
			Currency:             record.Currency,
			Status:               string(record.Status),
			PaymentMethod:        record.PaymentMethod,
			PaymentTransactionID: record.PaymentTransactionID,
			Notes:                record.Notes,
			GeneratedByUserID:    record.GeneratedByUserID,
			CreateTime:           record.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime:           record.UpdateTime.Format("2006-01-02 15:04:05"),
		}

		// 设置付款截止日期
		if record.DueDate != nil {
			dueDateStr := record.DueDate.Format("2006-01-02")
			dto.DueDate = &dueDateStr
		}

		// 设置支付日期
		if record.PaymentDate != nil {
			paymentDateStr := record.PaymentDate.Format("2006-01-02 15:04:05")
			dto.PaymentDate = &paymentDateStr
		}

		// 设置客户信息
		if customer, exists := userMap[record.CustomerAccountID]; exists {
			dto.CustomerNickname = customer.Nickname
			dto.CustomerUsername = customer.Username
		}

		// 设置生成操作员昵称
		if record.GeneratedByUserID != nil {
			if operator, exists := userMap[*record.GeneratedByUserID]; exists {
				dto.GeneratedByNickname = &operator.Nickname
			}
		}

		dtos[i] = dto
	}

	if logger != nil {
		logger.Info("Successfully listed billing records",
			zap.Int("page", req.Page),
			zap.Int("pageSize", req.PageSize),
			zap.Int64("total", total),
			zap.Int("count", len(dtos)))
	}

	return &ListBillingRecordsResponse{
		Total: total,
		List:  dtos,
	}, valueobject.SUCCESS, nil
}

// BillingRecordFilters 账单记录查询过滤器
type BillingRecordFilters struct {
	CustomerAccountID  *int64     // 客户账户ID
	BillingCycleID     *int64     // 账期批次ID
	Status             *string    // 账单状态
	BillNumber         *string    // 账单编号（模糊查询）
	BillDateStart      *time.Time // 账单日期开始
	BillDateEnd        *time.Time // 账单日期结束
	BillingPeriodStart *time.Time // 账期开始日期
	BillingPeriodEnd   *time.Time // 账期结束日期
	MinAmount          *float64   // 最小金额
	MaxAmount          *float64   // 最大金额
	Currency           *string    // 货币单位
}

// parseListBillingRecordsFilters 解析查询过滤器参数
func (s *BillingServiceImpl) parseListBillingRecordsFilters(req *ListBillingRecordsRequest) (*BillingRecordFilters, error) {
	filters := &BillingRecordFilters{
		CustomerAccountID: req.CustomerAccountID,
		BillingCycleID:    req.BillingCycleID,
		Status:            req.Status,
		BillNumber:        req.BillNumber,
		MinAmount:         req.MinAmount,
		MaxAmount:         req.MaxAmount,
		Currency:          req.Currency,
	}

	// 解析账单日期开始
	if req.BillDateStart != nil && *req.BillDateStart != "" {
		startDate, err := time.ParseInLocation("2006-01-02", *req.BillDateStart, time.Local)
		if err != nil {
			return nil, errors.New("账单日期开始格式错误，请使用 yyyy-MM-dd 格式")
		}
		filters.BillDateStart = &startDate
	}

	// 解析账单日期结束
	if req.BillDateEnd != nil && *req.BillDateEnd != "" {
		endDate, err := time.ParseInLocation("2006-01-02", *req.BillDateEnd, time.Local)
		if err != nil {
			return nil, errors.New("账单日期结束格式错误，请使用 yyyy-MM-dd 格式")
		}
		filters.BillDateEnd = &endDate
	}

	// 解析账期开始日期
	if req.BillingPeriodStart != nil && *req.BillingPeriodStart != "" {
		startDate, err := time.ParseInLocation("2006-01-02", *req.BillingPeriodStart, time.Local)
		if err != nil {
			return nil, errors.New("账期开始日期格式错误，请使用 yyyy-MM-dd 格式")
		}
		filters.BillingPeriodStart = &startDate
	}

	// 解析账期结束日期
	if req.BillingPeriodEnd != nil && *req.BillingPeriodEnd != "" {
		endDate, err := time.ParseInLocation("2006-01-02", *req.BillingPeriodEnd, time.Local)
		if err != nil {
			return nil, errors.New("账期结束日期格式错误，请使用 yyyy-MM-dd 格式")
		}
		filters.BillingPeriodEnd = &endDate
	}

	// 验证日期范围
	if filters.BillDateStart != nil && filters.BillDateEnd != nil {
		if filters.BillDateEnd.Before(*filters.BillDateStart) {
			return nil, errors.New("账单日期结束时间必须晚于开始时间")
		}
	}

	if filters.BillingPeriodStart != nil && filters.BillingPeriodEnd != nil {
		if filters.BillingPeriodEnd.Before(*filters.BillingPeriodStart) {
			return nil, errors.New("账期结束日期必须晚于开始日期")
		}
	}

	// 验证金额范围
	if filters.MinAmount != nil && filters.MaxAmount != nil {
		if *filters.MaxAmount < *filters.MinAmount {
			return nil, errors.New("最大金额必须大于等于最小金额")
		}
	}

	return filters, nil
}

// GetUsersForBilling 查询在指定时间范围内有可生成账单运单或财务调整的用户列表
func (s *BillingServiceImpl) GetUsersForBilling(ctx context.Context, req *GetUsersForBillingRequest) (*GetUsersForBillingResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证和转换（固定使用发货时间）
	startTime, endTime, err := s.validateGetUsersForBillingRequest(req)
	if err != nil {
		if logger != nil {
			logger.Warn("Invalid get users for billing request parameters", zap.Error(err))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 查询有运单记录的用户统计（固定使用发货时间）
	manifestUsers, err := s.billingRepo.FindUsersWithManifestsForBilling(ctx, entity.BillingTimeTypeShipmentTime, startTime, endTime)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find users with manifests for billing", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询有运单记录的用户失败")
	}

	// 3. 查询有财务调整记录的用户统计
	adjustmentUsers, err := s.billingRepo.FindUsersWithAdjustmentsForBilling(ctx, startTime, endTime)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find users with adjustments for billing", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询有财务调整记录的用户失败")
	}

	// 4. 合并统计数据
	userStatsMap := make(map[int64]*BillingUserDTO)

	// 处理运单数据
	for _, user := range manifestUsers {
		if user.ManifestCount > 0 {
			userStatsMap[user.UserID] = &BillingUserDTO{
				UserID:          user.UserID,
				Nickname:        user.Nickname,
				Username:        user.Username,
				ManifestCount:   user.ManifestCount,
				AdjustmentCount: 0,
				HasManifests:    true,
				HasAdjustments:  false,
			}
		}
	}

	// 处理调整记录数据
	for _, user := range adjustmentUsers {
		if user.AdjustmentCount > 0 {
			if existingUser, exists := userStatsMap[user.UserID]; exists {
				// 用户已存在，更新调整记录数据
				existingUser.AdjustmentCount = user.AdjustmentCount
				existingUser.HasAdjustments = true
			} else {
				// 新用户，只有调整记录
				userStatsMap[user.UserID] = &BillingUserDTO{
					UserID:          user.UserID,
					Nickname:        user.Nickname,
					Username:        user.Username,
					ManifestCount:   0,
					AdjustmentCount: user.AdjustmentCount,
					HasManifests:    false,
					HasAdjustments:  true,
				}
			}
		}
	}

	// 5. 计算总数量并转换为切片
	userList := make([]*BillingUserDTO, 0, len(userStatsMap))
	for _, user := range userStatsMap {
		user.TotalCount = user.ManifestCount + user.AdjustmentCount
		userList = append(userList, user)
	}

	// 6. 按总数量倒序排序
	for i := 0; i < len(userList)-1; i++ {
		for j := i + 1; j < len(userList); j++ {
			if userList[i].TotalCount < userList[j].TotalCount {
				userList[i], userList[j] = userList[j], userList[i]
			}
		}
	}

	if logger != nil {
		logger.Info("Successfully found users for billing",
			zap.String("timeType", "SHIPMENT_TIME"),
			zap.Time("startTime", startTime),
			zap.Time("endTime", endTime),
			zap.Int("userCount", len(userList)))
	}

	return &GetUsersForBillingResponse{
		Total: int64(len(userList)),
		List:  userList,
	}, valueobject.SUCCESS, nil
}

// validateGetUsersForBillingRequest 验证查询可生成账单用户列表请求参数（固定使用发货时间）
func (s *BillingServiceImpl) validateGetUsersForBillingRequest(req *GetUsersForBillingRequest) (time.Time, time.Time, error) {
	// 解析开始时间
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("开始时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式")
	}

	// 解析结束时间
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		return time.Time{}, time.Time{}, errors.New("结束时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式")
	}

	// 验证时间范围
	if !endTime.After(startTime) {
		return time.Time{}, time.Time{}, errors.New("结束时间必须晚于开始时间")
	}

	return startTime, endTime, nil
}

// validateAndParseGenerateBillingRequest 验证和解析生成账单请求参数
func (s *BillingServiceImpl) validateAndParseGenerateBillingRequest(req *GenerateBillingRequest) (time.Time, time.Time, *time.Time, error) {
	// 解析开始时间
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		return time.Time{}, time.Time{}, nil, errors.New("开始时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式")
	}

	// 解析结束时间
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		return time.Time{}, time.Time{}, nil, errors.New("结束时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式")
	}

	// 验证时间范围
	if !endTime.After(startTime) {
		return time.Time{}, time.Time{}, nil, errors.New("结束时间必须晚于开始时间")
	}

	// 解析付款截止日期（可选）
	var dueDate *time.Time
	if req.DueDate != nil && *req.DueDate != "" {
		parsed, err := time.ParseInLocation("2006-01-02", *req.DueDate, time.Local)
		if err != nil {
			return time.Time{}, time.Time{}, nil, errors.New("付款截止日期格式错误，请使用 yyyy-MM-dd 格式")
		}
		dueDate = &parsed
	}

	return startTime, endTime, dueDate, nil
}

// FreightFees 运费计算结果
type FreightFees struct {
	BaseFreightFee     float64 // 基础运费
	FirstWeightFee     float64 // 首重费用
	ContinuedWeightFee float64 // 续重费用
	TotalAmount        float64 // 总费用
}

// generateBillingItems 生成账单明细
func (s *BillingServiceImpl) generateBillingItems(ctx context.Context, billingRecordID int64, manifests []*entity.Manifest, templates *entity.AppliedFreightTemplate) ([]*entity.BillingRecordItem, float64, error) {
	var items []*entity.BillingRecordItem
	var totalAmount float64

	for _, manifest := range manifests {
		// 根据运单信息判断货物类型
		cargoType := s.determineCargoType(manifest)

		// 根据货物类型选择对应的运费模板
		var template *entity.ShippingFeeTemplate
		switch cargoType {
		case entity.CargoTypeGeneral:
			template = templates.GeneralTemplate
		case entity.CargoTypeBattery:
			template = templates.BatteryTemplate
		case entity.CargoTypePostBox:
			template = templates.PostBoxTemplate
		default:
			template = templates.GeneralTemplate // 默认使用普货模板
		}

		// 如果没有对应的模板，跳过这个运单
		if template == nil {
			continue
		}

		// 根据模板重新计算体积重量
		dimensionalWeight := s.calculateDimensionalWeight(manifest, template)

		// 重新计算计费重量（实际重量和重新计算的体积重的较大值）
		chargeableWeight := s.calculateChargeableWeight(manifest.Weight, dimensionalWeight)

		// 计算费用（使用重新计算的体积重量和计费重量）
		fees := s.calculateFreightFeesWithRecalculatedWeight(manifest, template, dimensionalWeight, *chargeableWeight)

		// 创建账单明细项
		item := &entity.BillingRecordItem{
			BillingRecordID:           billingRecordID,
			ManifestID:                &manifest.ID,
			ExpressNumber:             &manifest.ExpressNumber,
			OrderNo:                   &manifest.OrderNo,
			TransferredTrackingNumber: &manifest.TransferredTrackingNumber,
			OrderNumber:               &manifest.OrderNumber,
			ManifestCreateTime:        &manifest.CreateTime,
			ShipmentTime:              s.convertPointerFormattedTimeToTimePointer(manifest.ShipmentTime),
			ReceiverName:              &manifest.ReceiverName,
			ItemDescription:           s.buildItemDescription(manifest.Items),
			CargoType:                 cargoType,
			Weight:                    &manifest.Weight,
			Length:                    &manifest.Length,
			Width:                     &manifest.Width,
			Height:                    &manifest.Height,
			SumOfSides:                s.calculateSumOfSides(manifest.Length, manifest.Width, manifest.Height),
			DimensionalWeight:         &dimensionalWeight, // 使用重新计算的体积重量
			ChargeableWeight:          chargeableWeight,   // 使用重新计算的计费重量
			BaseFreightFee:            &fees.BaseFreightFee,
			FirstWeightFee:            &fees.FirstWeightFee,
			ContinuedWeightFee:        &fees.ContinuedWeightFee,
			OverLengthSurcharge:       &manifest.OverLengthSurcharge,
			RemoteAreaSurcharge:       &manifest.RemoteAreaSurcharge,
			OverweightSurcharge:       &manifest.OtherCost,
			ItemTotalAmount:           fees.TotalAmount,
			CreateTime:                time.Now(),
			UpdateTime:                time.Now(),
		}

		items = append(items, item)
		totalAmount += fees.TotalAmount
	}

	return items, totalAmount, nil
}

// calculateDimensionalWeight 根据运费模板计算体积重量
func (s *BillingServiceImpl) calculateDimensionalWeight(manifest *entity.Manifest, template *entity.ShippingFeeTemplate) float64 {
	// 计算三边和
	threeSidesSum := manifest.Length + manifest.Width + manifest.Height

	// 初始化体积重量为0
	var dimensionalWeight float64 = 0

	// 只有当三边和超过模板设定的阈值时才计算体积重量
	if threeSidesSum > template.ThreeSidesStart {
		// 体积重 = 长*宽*高 / 轻抛系数
		dimensionalWeight = (manifest.Length * manifest.Width * manifest.Height) / float64(template.BulkCoefficient)
		// 保留两位小数
		dimensionalWeight = math.Round(dimensionalWeight*1000) / 1000
	}

	return dimensionalWeight
}

// calculateFreightFeesWithRecalculatedWeight 使用重新计算的重量信息计算运费
func (s *BillingServiceImpl) calculateFreightFeesWithRecalculatedWeight(manifest *entity.Manifest, template *entity.ShippingFeeTemplate, dimensionalWeight, chargeableWeight float64) *FreightFees {
	// 计算首重费用
	firstWeightFee := template.FirstWeightPrice

	// 计算续重费用
	var continuedWeightFee float64
	if chargeableWeight > template.FirstWeightRange {
		continuedWeight := chargeableWeight - template.FirstWeightRange
		continuedWeightSteps := int(continuedWeight/template.ContinuedWeightInterval) + 1
		if continuedWeight > float64(continuedWeightSteps-1)*template.ContinuedWeightInterval {
			// 不足一个重量段按一个重量段计费
		}
		continuedWeightFee = float64(continuedWeightSteps) * template.ContinuedWeightPrice
	}

	// 基础运费 = 首重费用 + 续重费用
	baseFreightFee := firstWeightFee + continuedWeightFee

	// 总费用 = 基础运费 + 超长费 + 偏远费 + 其他费用
	totalAmount := baseFreightFee + manifest.OverLengthSurcharge + manifest.RemoteAreaSurcharge + manifest.OtherCost

	return &FreightFees{
		BaseFreightFee:     baseFreightFee,
		FirstWeightFee:     firstWeightFee,
		ContinuedWeightFee: continuedWeightFee,
		TotalAmount:        totalAmount,
	}
}

// calculateFreightFees 计算运费（原方法，保持向后兼容）
func (s *BillingServiceImpl) calculateFreightFees(manifest *entity.Manifest, template *entity.ShippingFeeTemplate) *FreightFees {
	// 计算计费重量（实际重量和体积重的较大值）
	chargeableWeight := s.calculateChargeableWeight(manifest.Weight, manifest.DimensionalWeight)

	// 计算首重费用
	firstWeightFee := template.FirstWeightPrice

	// 计算续重费用
	var continuedWeightFee float64
	if *chargeableWeight > template.FirstWeightRange {
		continuedWeight := *chargeableWeight - template.FirstWeightRange
		continuedWeightSteps := int(*chargeableWeight/template.ContinuedWeightInterval) + 1
		if continuedWeight > float64(continuedWeightSteps-1)*template.ContinuedWeightInterval {
			// 不足一个重量段按一个重量段计费
		}
		continuedWeightFee = float64(continuedWeightSteps) * template.ContinuedWeightPrice
	}

	// 基础运费 = 首重费用 + 续重费用
	baseFreightFee := firstWeightFee + continuedWeightFee

	// 总费用 = 基础运费 + 超长费 + 偏远费 + 其他费用
	totalAmount := baseFreightFee + manifest.OverLengthSurcharge + manifest.RemoteAreaSurcharge + manifest.OtherCost

	return &FreightFees{
		BaseFreightFee:     baseFreightFee,
		FirstWeightFee:     firstWeightFee,
		ContinuedWeightFee: continuedWeightFee,
		TotalAmount:        totalAmount,
	}
}

// determineCargoType 根据运单信息判断货物类型
func (s *BillingServiceImpl) determineCargoType(manifest *entity.Manifest) entity.CargoType {
	// 根据运单的运费模板类型判断货物类型
	switch manifest.ShippingFeeTemplateType {
	case 1:
		return entity.CargoTypeGeneral // 普货
	case 2:
		return entity.CargoTypeBattery // 带电货物
	case 3:
		return entity.CargoTypePostBox // 投函货物
	default:
		return entity.CargoTypeGeneral // 默认为普货
	}
}

// buildItemDescription 构建物品描述
func (s *BillingServiceImpl) buildItemDescription(items []entity.ManifestItem) string {
	if len(items) == 0 {
		return ""
	}

	var descriptions []string
	for _, item := range items {
		descriptions = append(descriptions, item.Name)
	}

	return strings.Join(descriptions, ", ")
}

// calculateSumOfSides 计算三边和
func (s *BillingServiceImpl) calculateSumOfSides(length, width, height float64) *float64 {
	sum := length + width + height
	return &sum
}

// calculateChargeableWeight 计算计费重量
func (s *BillingServiceImpl) calculateChargeableWeight(actualWeight, dimensionalWeight float64) *float64 {
	var chargeableWeight float64
	if dimensionalWeight > actualWeight {
		chargeableWeight = dimensionalWeight
	} else {
		chargeableWeight = actualWeight
	}
	return &chargeableWeight
}

// generateFinancialAdjustmentSnapshots 生成财务调整快照
func (s *BillingServiceImpl) generateFinancialAdjustmentSnapshots(ctx context.Context, billingRecordID int64, userID int64, startDate, endDate time.Time) (float64, error) {
	// 查询财务调整记录
	adjustments, err := s.billingRepo.FindFinancialAdjustmentsForBilling(ctx, userID, startDate, endDate)
	if err != nil {
		return 0, fmt.Errorf("查询财务调整记录失败: %w", err)
	}

	if len(adjustments) == 0 {
		return 0, nil // 没有财务调整记录，直接返回
	}

	// 收集所有运单ID
	manifestIDs := make([]int64, 0, len(adjustments))
	for _, adjustment := range adjustments {
		manifestIDs = append(manifestIDs, adjustment.ManifestID)
	}

	// 批量查询运单信息
	manifests, err := s.billingRepo.FindManifestsByIDs(ctx, manifestIDs)
	if err != nil {
		return 0, fmt.Errorf("查询运单信息失败: %w", err)
	}

	// 构建运单ID到运单实体的映射
	manifestMap := make(map[int64]*entity.Manifest)
	for _, manifest := range manifests {
		manifestMap[manifest.ID] = manifest
	}

	// 生成快照
	var snapshots []*entity.BillingFinancialAdjustmentSnapshot
	var adjustmentTotalAmount float64
	for _, adjustment := range adjustments {
		var additionalDetails map[string]interface{}
		if adjustment.AdditionalDetails != nil {
			additionalDetails = *adjustment.AdditionalDetails
		}

		snapshot := &entity.BillingFinancialAdjustmentSnapshot{
			BillingRecordID:      billingRecordID,
			OriginalAdjustmentID: adjustment.ID,
			AdjustmentType:       adjustment.AdjustmentType,
			Description:          &adjustment.Description,
			AdditionalDetails:    additionalDetails,
			Amount:               adjustment.Amount,
			Currency:             adjustment.Currency,
			EffectiveDate:        adjustment.EffectiveDate,
			SnapshotTime:         time.Now(),
		}

		// 填充运单信息快照
		if manifest, exists := manifestMap[adjustment.ManifestID]; exists {
			snapshot.ManifestID = &manifest.ID
			snapshot.ManifestExpressNumber = &manifest.ExpressNumber
			snapshot.ManifestOrderNo = &manifest.OrderNo
			snapshot.ManifestTransferredTrackingNumber = &manifest.TransferredTrackingNumber
			snapshot.ManifestCustomerOrderNumber = &manifest.OrderNumber
			snapshot.ManifestCreateTime = &manifest.CreateTime
			snapshot.ManifestReceiverName = &manifest.ReceiverName

			// 设置发货时间
			if manifest.ShipmentTime.Time != nil {
				snapshot.ManifestShipmentTime = manifest.ShipmentTime.Time
			}

			// 构建物品描述
			itemDescription := s.buildItemDescription(manifest.Items)
			if itemDescription != "" {
				snapshot.ManifestItemDescription = &itemDescription
			}

			// 设置货物类型
			cargoType := s.determineCargoType(manifest)
			cargoTypeName := s.getCargoTypeName(cargoType)
			snapshot.ManifestCargoType = &cargoTypeName

			// 设置尺寸重量信息
			snapshot.ManifestWeight = &manifest.Weight
			snapshot.ManifestLength = &manifest.Length
			snapshot.ManifestWidth = &manifest.Width
			snapshot.ManifestHeight = &manifest.Height

			// 计算三边和
			sumOfSides := manifest.Length + manifest.Width + manifest.Height
			snapshot.ManifestSumOfSides = &sumOfSides

			snapshot.ManifestDimensionalWeight = &manifest.DimensionalWeight

			// 计算计费重量
			chargeableWeight := s.calculateChargeableWeight(manifest.Weight, manifest.DimensionalWeight)
			snapshot.ManifestChargeableWeight = chargeableWeight
		}

		snapshots = append(snapshots, snapshot)
		adjustmentTotalAmount += adjustment.Amount
	}

	// 保存快照
	if len(snapshots) > 0 {
		if err := s.billingRepo.SaveBillingFinancialAdjustmentSnapshots(ctx, snapshots); err != nil {
			return 0, fmt.Errorf("保存财务调整快照失败: %w", err)
		}
	}

	return adjustmentTotalAmount, nil
}

// convertPointerFormattedTimeToTimePointer 转换 util.PointerFormattedTime 为 *time.Time
func (s *BillingServiceImpl) convertPointerFormattedTimeToTimePointer(pft util.PointerFormattedTime) *time.Time {
	if pft.Time == nil {
		return nil
	}
	return pft.Time
}

// ListBillingRecordItems 根据账单记录ID查询账单明细列表
func (s *BillingServiceImpl) ListBillingRecordItems(ctx context.Context, req *ListBillingRecordItemsRequest) (*ListBillingRecordItemsResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.BillingRecordID <= 0 {
		if logger != nil {
			logger.Warn("Invalid billing record ID", zap.Int64("billingRecordId", req.BillingRecordID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("账单记录ID必须大于0")
	}

	// 2. 验证账单记录是否存在
	_, err := s.billingRepo.FindBillingRecordByID(ctx, req.BillingRecordID)
	if err != nil {
		if logger != nil {
			logger.Warn("Billing record not found",
				zap.Int64("billingRecordId", req.BillingRecordID),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("账单记录不存在")
	}

	// 3. 查询账单明细列表
	items, total, err := s.billingRepo.FindBillingRecordItemsByBillingRecordID(ctx, req.BillingRecordID, req.Page, req.PageSize)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find billing record items",
				zap.Int64("billingRecordId", req.BillingRecordID),
				zap.Int("page", req.Page),
				zap.Int("pageSize", req.PageSize),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账单明细失败")
	}

	// 4. 转换为DTO
	itemDTOs := make([]*BillingRecordItemDTO, len(items))
	for i, item := range items {
		dto := &BillingRecordItemDTO{
			ID:                        item.ID,
			BillingRecordID:           item.BillingRecordID,
			ManifestID:                item.ManifestID,
			ExpressNumber:             item.ExpressNumber,
			OrderNo:                   item.OrderNo,
			TransferredTrackingNumber: item.TransferredTrackingNumber,
			OrderNumber:               item.OrderNumber,
			ReceiverName:              item.ReceiverName,
			ItemDescription:           item.ItemDescription,
			CargoType:                 int(item.CargoType),
			CargoTypeName:             s.getCargoTypeName(item.CargoType),
			Weight:                    item.Weight,
			Length:                    item.Length,
			Width:                     item.Width,
			Height:                    item.Height,
			SumOfSides:                item.SumOfSides,
			DimensionalWeight:         item.DimensionalWeight,
			ChargeableWeight:          item.ChargeableWeight,
			BaseFreightFee:            item.BaseFreightFee,
			FirstWeightFee:            item.FirstWeightFee,
			ContinuedWeightFee:        item.ContinuedWeightFee,
			OverLengthSurcharge:       item.OverLengthSurcharge,
			RemoteAreaSurcharge:       item.RemoteAreaSurcharge,
			OverweightSurcharge:       item.OverweightSurcharge,
			ItemTotalAmount:           item.ItemTotalAmount,
			CreateTime:                item.CreateTime.Format("2006-01-02 15:04:05"),
			UpdateTime:                item.UpdateTime.Format("2006-01-02 15:04:05"),
		}

		// 转换时间字段
		if item.ManifestCreateTime != nil {
			manifestCreateTimeStr := item.ManifestCreateTime.Format("2006-01-02 15:04:05")
			dto.ManifestCreateTime = &manifestCreateTimeStr
		}
		if item.ShipmentTime != nil {
			shipmentTimeStr := item.ShipmentTime.Format("2006-01-02 15:04:05")
			dto.ShipmentTime = &shipmentTimeStr
		}

		itemDTOs[i] = dto
	}

	if logger != nil {
		logger.Info("Successfully listed billing record items",
			zap.Int64("billingRecordId", req.BillingRecordID),
			zap.Int("page", req.Page),
			zap.Int("pageSize", req.PageSize),
			zap.Int64("total", total),
			zap.Int("count", len(itemDTOs)))
	}

	return &ListBillingRecordItemsResponse{
		Total: total,
		List:  itemDTOs,
	}, valueobject.SUCCESS, nil
}

// getCargoTypeName 获取货物类型名称
func (s *BillingServiceImpl) getCargoTypeName(cargoType entity.CargoType) string {
	switch cargoType {
	case entity.CargoTypeGeneral:
		return "普通货物"
	case entity.CargoTypeBattery:
		return "带电货物"
	case entity.CargoTypePostBox:
		return "投函货物"
	default:
		return fmt.Sprintf("未知类型(%d)", int(cargoType))
	}
}

// GetBillingRecordDetail 根据账单记录ID获取账单记录详情
func (s *BillingServiceImpl) GetBillingRecordDetail(ctx context.Context, req *GetBillingRecordDetailRequest) (*GetBillingRecordDetailResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.BillingRecordID <= 0 {
		if logger != nil {
			logger.Warn("Invalid billing record ID", zap.Int64("billingRecordId", req.BillingRecordID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("账单记录ID必须大于0")
	}

	// 2. 查询账单记录
	record, err := s.billingRepo.FindBillingRecordByID(ctx, req.BillingRecordID)
	if err != nil {
		if logger != nil {
			logger.Warn("Billing record not found",
				zap.Int64("billingRecordId", req.BillingRecordID),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("账单记录不存在")
	}

	// 3. 查询相关用户信息
	userIDs := make([]int64, 0)
	userIDs = append(userIDs, record.CustomerAccountID)
	if record.GeneratedByUserID != nil {
		userIDs = append(userIDs, *record.GeneratedByUserID)
	}

	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find users by IDs", zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 4. 构建详情DTO
	dto := &BillingRecordDetailDTO{
		ID:                      record.ID,
		BillNumber:              record.BillNumber,
		CustomerAccountID:       record.CustomerAccountID,
		BillingCycleID:          record.BillingCycleID,
		BillDate:                record.BillDate.Format("2006-01-02"),
		BillingPeriodStart:      record.BillingPeriodStart.Format("2006-01-02 15:04:05"),
		BillingPeriodEnd:        record.BillingPeriodEnd.Format("2006-01-02 15:04:05"),
		AppliedFreightTemplates: record.AppliedFreightTemplates,
		FreightChargesTotal:     record.FreightChargesTotal,
		AdjustmentChargesTotal:  record.AdjustmentChargesTotal,
		TotalAmount:             record.TotalAmount,
		AmountPaid:              record.AmountPaid,
		BalanceDue:              record.BalanceDue,
		Currency:                record.Currency,
		Status:                  string(record.Status),
		StatusName:              s.getBillingStatusName(record.Status),
		PaymentMethod:           record.PaymentMethod,
		PaymentTransactionID:    record.PaymentTransactionID,
		Notes:                   record.Notes,
		GeneratedByUserID:       record.GeneratedByUserID,
		CreateTime:              record.CreateTime.Format("2006-01-02 15:04:05"),
		UpdateTime:              record.UpdateTime.Format("2006-01-02 15:04:05"),
	}

	// 设置付款截止日期
	if record.DueDate != nil {
		dueDateStr := record.DueDate.Format("2006-01-02")
		dto.DueDate = &dueDateStr
	}

	// 设置支付日期
	if record.PaymentDate != nil {
		paymentDateStr := record.PaymentDate.Format("2006-01-02 15:04:05")
		dto.PaymentDate = &paymentDateStr
	}

	// 设置客户信息
	if customer, exists := userMap[record.CustomerAccountID]; exists {
		dto.CustomerNickname = customer.Nickname
		dto.CustomerUsername = customer.Username
	}

	// 设置生成操作员信息
	if record.GeneratedByUserID != nil {
		if operator, exists := userMap[*record.GeneratedByUserID]; exists {
			dto.GeneratedByNickname = &operator.Nickname
		}
	}

	if logger != nil {
		logger.Info("Successfully retrieved billing record detail",
			zap.Int64("billingRecordId", req.BillingRecordID),
			zap.String("billNumber", record.BillNumber))
	}

	return &GetBillingRecordDetailResponse{
		BillingRecord: dto,
	}, valueobject.SUCCESS, nil
}

// getBillingStatusName 获取账单状态名称
func (s *BillingServiceImpl) getBillingStatusName(status entity.BillingStatus) string {
	switch status {
	case entity.BillingStatusUnpaid:
		return "未付款"
	case entity.BillingStatusPaid:
		return "已付款"
	case entity.BillingStatusPartiallyPaid:
		return "部分付款"
	case entity.BillingStatusOverdue:
		return "逾期"
	case entity.BillingStatusVoid:
		return "作废"
	case entity.BillingStatusError:
		return "异常"
	default:
		return fmt.Sprintf("未知状态(%s)", string(status))
	}
}

// ListBillingAdjustmentSnapshots 根据账单记录ID分页查询调整明细列表
func (s *BillingServiceImpl) ListBillingAdjustmentSnapshots(ctx context.Context, req *ListBillingAdjustmentSnapshotsRequest) (*ListBillingAdjustmentSnapshotsResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.BillingRecordID <= 0 {
		if logger != nil {
			logger.Warn("Invalid billing record ID", zap.Int64("billingRecordId", req.BillingRecordID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("账单记录ID必须大于0")
	}

	// 2. 验证账单记录是否存在
	_, err := s.billingRepo.FindBillingRecordByID(ctx, req.BillingRecordID)
	if err != nil {
		if logger != nil {
			logger.Warn("Billing record not found",
				zap.Int64("billingRecordId", req.BillingRecordID),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("账单记录不存在")
	}

	// 3. 查询账单调整明细列表
	snapshots, total, err := s.billingRepo.FindBillingFinancialAdjustmentSnapshotsByBillingRecordID(ctx, req.BillingRecordID, req.Page, req.PageSize)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find billing adjustment snapshots",
				zap.Int64("billingRecordId", req.BillingRecordID),
				zap.Int("page", req.Page),
				zap.Int("pageSize", req.PageSize),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账单调整明细失败")
	}

	// 4. 转换为DTO
	snapshotDTOs := make([]*BillingAdjustmentSnapshotDTO, len(snapshots))
	for i, snapshot := range snapshots {
		dto := &BillingAdjustmentSnapshotDTO{
			ID:                                snapshot.ID,
			BillingRecordID:                   snapshot.BillingRecordID,
			OriginalAdjustmentID:              snapshot.OriginalAdjustmentID,
			AdjustmentType:                    snapshot.AdjustmentType,
			AdjustmentTypeName:                s.getAdjustmentTypeName(snapshot.AdjustmentType),
			Description:                       snapshot.Description,
			AdditionalDetails:                 snapshot.AdditionalDetails,
			Amount:                            snapshot.Amount,
			Currency:                          snapshot.Currency,
			EffectiveDate:                     snapshot.EffectiveDate.Format("2006-01-02"),
			ManifestID:                        snapshot.ManifestID,
			ManifestExpressNumber:             snapshot.ManifestExpressNumber,
			ManifestOrderNo:                   snapshot.ManifestOrderNo,
			ManifestTransferredTrackingNumber: snapshot.ManifestTransferredTrackingNumber,
			ManifestCustomerOrderNumber:       snapshot.ManifestCustomerOrderNumber,
			ManifestReceiverName:              snapshot.ManifestReceiverName,
			ManifestItemDescription:           snapshot.ManifestItemDescription,
			ManifestCargoType:                 snapshot.ManifestCargoType,
			ManifestWeight:                    snapshot.ManifestWeight,
			ManifestLength:                    snapshot.ManifestLength,
			ManifestWidth:                     snapshot.ManifestWidth,
			ManifestHeight:                    snapshot.ManifestHeight,
			ManifestSumOfSides:                snapshot.ManifestSumOfSides,
			ManifestDimensionalWeight:         snapshot.ManifestDimensionalWeight,
			ManifestChargeableWeight:          snapshot.ManifestChargeableWeight,
			SnapshotTime:                      snapshot.SnapshotTime.Format("2006-01-02 15:04:05"),
		}

		// 转换时间字段
		if snapshot.ManifestCreateTime != nil {
			manifestCreateTimeStr := snapshot.ManifestCreateTime.Format("2006-01-02 15:04:05")
			dto.ManifestCreateTime = &manifestCreateTimeStr
		}
		if snapshot.ManifestShipmentTime != nil {
			shipmentTimeStr := snapshot.ManifestShipmentTime.Format("2006-01-02 15:04:05")
			dto.ManifestShipmentTime = &shipmentTimeStr
		}
		snapshotDTOs[i] = dto
	}

	if logger != nil {
		logger.Info("Successfully listed billing adjustment snapshots",
			zap.Int64("billingRecordId", req.BillingRecordID),
			zap.Int("page", req.Page),
			zap.Int("pageSize", req.PageSize),
			zap.Int64("total", total),
			zap.Int("count", len(snapshotDTOs)))
	}

	return &ListBillingAdjustmentSnapshotsResponse{
		Total: total,
		List:  snapshotDTOs,
	}, valueobject.SUCCESS, nil
}

// getAdjustmentTypeName 获取调整类型名称
func (s *BillingServiceImpl) getAdjustmentTypeName(adjustmentType string) string {
	switch adjustmentType {
	case "COMPENSATION":
		return "赔偿"
	case "REASSIGNMENT":
		return "改派"
	case "DESTRUCTION":
		return "销毁"
	case "RETURN":
		return "退回"
	case "FEE":
		return "费用"
	case "REBATE":
		return "返款"
	case "OTHER":
		return "其他"
	default:
		return fmt.Sprintf("未知类型(%s)", adjustmentType)
	}
}

// ListBillingGenerationTasks 根据账期批次和当前登录用户分页获取任务列表
func (s *BillingServiceImpl) ListBillingGenerationTasks(ctx context.Context, req *ListBillingGenerationTasksRequest) (*ListBillingGenerationTasksResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.BillingCycleID <= 0 {
		if logger != nil {
			logger.Warn("Invalid billing cycle ID", zap.Int64("billingCycleId", req.BillingCycleID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("账期批次ID必须大于0")
	}

	// 处理分页参数
	page := req.Page
	pageSize := req.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 2. 查询账单生成任务列表
	tasks, total, err := s.billingGenerationTaskRepo.FindTasksByBillingCycleAndUser(ctx, req.BillingCycleID, nil, page, pageSize)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find billing generation tasks",
				zap.Int64("billingCycleId", req.BillingCycleID),
				zap.Int("page", page),
				zap.Int("pageSize", pageSize),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账单生成任务失败")
	}

	// 3. 收集用户ID并批量查询用户信息
	userIDs := make([]int64, 0)
	userIDSet := make(map[int64]bool)

	for _, task := range tasks {
		if task.SubmittedByUserID != nil && !userIDSet[*task.SubmittedByUserID] {
			userIDs = append(userIDs, *task.SubmittedByUserID)
			userIDSet[*task.SubmittedByUserID] = true
		}
	}

	// 4. 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find users by IDs", zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 5. 转换为DTO
	taskDTOs := make([]*BillingGenerationTaskDTO, len(tasks))
	for i, task := range tasks {
		dto := &BillingGenerationTaskDTO{
			TaskID:              task.TaskID,
			BillingCycleID:      task.BillingCycleID,
			TargetCustomerIds:   task.TargetCustomerIds,
			Status:              string(task.Status),
			StatusName:          task.GetStatusName(),
			ProgressPercentage:  task.ProgressPercentage,
			TotalItemsToProcess: task.TotalItemsToProcess,
			ItemsProcessedCount: task.ItemsProcessedCount,
			ErrorMessage:        task.ErrorMessage,
			SubmittedByUserID:   task.SubmittedByUserID,
			SubmitTime:          task.SubmitTime.Format("2006-01-02 15:04:05"),
			Duration:            task.GetDuration(),
		}

		// 设置开始时间
		if task.StartTime != nil {
			startTimeStr := task.StartTime.Format("2006-01-02 15:04:05")
			dto.StartTime = &startTimeStr
		}

		// 设置结束时间
		if task.EndTime != nil {
			endTimeStr := task.EndTime.Format("2006-01-02 15:04:05")
			dto.EndTime = &endTimeStr
		}

		// 设置提交者昵称
		if task.SubmittedByUserID != nil {
			if user, exists := userMap[*task.SubmittedByUserID]; exists {
				dto.SubmittedByNickname = &user.Nickname
			}
		}

		taskDTOs[i] = dto
	}

	if logger != nil {
		logger.Info("Successfully listed billing generation tasks",
			zap.Int64("billingCycleId", req.BillingCycleID),
			zap.Int("page", page),
			zap.Int("pageSize", pageSize),
			zap.Int64("total", total),
			zap.Int("count", len(taskDTOs)))
	}

	return &ListBillingGenerationTasksResponse{
		Total: total,
		List:  taskDTOs,
	}, valueobject.SUCCESS, nil
}

// ListBillingGenerationTasksByUser 根据账期批次和用户ID分页获取任务列表
func (s *BillingServiceImpl) ListBillingGenerationTasksByUser(ctx context.Context, req *ListBillingGenerationTasksRequest, userID int64) (*ListBillingGenerationTasksResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.BillingCycleID <= 0 {
		if logger != nil {
			logger.Warn("Invalid billing cycle ID", zap.Int64("billingCycleId", req.BillingCycleID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("账期批次ID必须大于0")
	}

	// 处理分页参数
	page := req.Page
	pageSize := req.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	// 2. 查询账单生成任务列表
	tasks, total, err := s.billingGenerationTaskRepo.FindTasksByBillingCycleAndUser(ctx, req.BillingCycleID, &userID, page, pageSize)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to find billing generation tasks",
				zap.Int64("billingCycleId", req.BillingCycleID),
				zap.Int("page", page),
				zap.Int("pageSize", pageSize),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("查询账单生成任务失败")
	}

	// 3. 收集用户ID并批量查询用户信息
	userIDs := make([]int64, 0)
	userIDSet := make(map[int64]bool)

	for _, task := range tasks {
		if task.SubmittedByUserID != nil && !userIDSet[*task.SubmittedByUserID] {
			userIDs = append(userIDs, *task.SubmittedByUserID)
			userIDSet[*task.SubmittedByUserID] = true
		}
	}

	// 4. 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find users by IDs", zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 5. 转换为DTO
	taskDTOs := make([]*BillingGenerationTaskDTO, len(tasks))
	for i, task := range tasks {
		dto := &BillingGenerationTaskDTO{
			TaskID:              task.TaskID,
			BillingCycleID:      task.BillingCycleID,
			TargetCustomerIds:   task.TargetCustomerIds,
			Status:              string(task.Status),
			StatusName:          task.GetStatusName(),
			ProgressPercentage:  task.ProgressPercentage,
			TotalItemsToProcess: task.TotalItemsToProcess,
			ItemsProcessedCount: task.ItemsProcessedCount,
			ErrorMessage:        task.ErrorMessage,
			SubmittedByUserID:   task.SubmittedByUserID,
			SubmitTime:          task.SubmitTime.Format("2006-01-02 15:04:05"),
			Duration:            task.GetDuration(),
		}

		// 设置开始时间
		if task.StartTime != nil {
			startTimeStr := task.StartTime.Format("2006-01-02 15:04:05")
			dto.StartTime = &startTimeStr
		}

		// 设置结束时间
		if task.EndTime != nil {
			endTimeStr := task.EndTime.Format("2006-01-02 15:04:05")
			dto.EndTime = &endTimeStr
		}

		// 设置提交者昵称
		if task.SubmittedByUserID != nil {
			if user, exists := userMap[*task.SubmittedByUserID]; exists {
				dto.SubmittedByNickname = &user.Nickname
			}
		}

		taskDTOs[i] = dto
	}

	if logger != nil {
		logger.Info("Successfully listed billing generation tasks",
			zap.Int64("billingCycleId", req.BillingCycleID),
			zap.Int("page", page),
			zap.Int("pageSize", pageSize),
			zap.Int64("total", total),
			zap.Int("count", len(taskDTOs)))
	}

	return &ListBillingGenerationTasksResponse{
		Total: total,
		List:  taskDTOs,
	}, valueobject.SUCCESS, nil
}

// AsyncGenerateBillingRequest 异步生成账单请求
type AsyncGenerateBillingRequest struct {
	StartTime      string  `json:"startTime" binding:"required"`         // 开始时间，格式：yyyy-MM-dd HH:mm:ss
	EndTime        string  `json:"endTime" binding:"required"`           // 结束时间，格式：yyyy-MM-dd HH:mm:ss
	CustomerIDs    []int64 `json:"customerIds" binding:"required,min=1"` // 客户ID列表（必须至少包含一个）
	BillingCycleID int64   `json:"billingCycleId" binding:"required"`    // 所属账期批次ID
	DueDate        *string `json:"dueDate,omitempty"`                    // 付款截止日期，格式：yyyy-MM-dd
	Notes          *string `json:"notes,omitempty"`                      // 账单备注
	Currency       string  `json:"currency" binding:"required"`          // 货币单位，默认CNY

	// 用户特定模板列表（可选，如果不提供则使用系统默认模板）
	UserTemplates []AsyncUserTemplateConfig `json:"userTemplates,omitempty"` // 用户特定模板配置列表
}

// AsyncUserTemplateConfig 异步生成中的用户模板配置
type AsyncUserTemplateConfig struct {
	UserID          int64                       `json:"userId" binding:"required"` // 用户ID
	GeneralTemplate *entity.ShippingFeeTemplate `json:"generalTemplate,omitempty"` // 普货模板
	BatteryTemplate *entity.ShippingFeeTemplate `json:"batteryTemplate,omitempty"` // 带电模板
	PostBoxTemplate *entity.ShippingFeeTemplate `json:"postBoxTemplate,omitempty"` // 投函模板
}

// AsyncGenerateBillingResponse 异步生成账单响应
type AsyncGenerateBillingResponse struct {
	TaskID         string `json:"taskId"`         // 任务ID
	BillingCycleID int64  `json:"billingCycleId"` // 账期批次ID
	CustomerCount  int    `json:"customerCount"`  // 客户数量
	Status         string `json:"status"`         // 任务状态
	StatusName     string `json:"statusName"`     // 任务状态名称
	SubmitTime     string `json:"submitTime"`     // 提交时间
	Message        string `json:"message"`        // 响应消息
}

// AsyncGenerateBilling 异步生成账单
func (s *BillingServiceImpl) AsyncGenerateBilling(ctx context.Context, req *AsyncGenerateBillingRequest, submittedByUserID int64) (*AsyncGenerateBillingResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证和转换
	startTime, endTime, dueDate, err := s.validateAndParseAsyncGenerateBillingRequest(req)
	if err != nil {
		if logger != nil {
			logger.Warn("Invalid async billing request parameters", zap.Error(err))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, err
	}

	// 2. 生成任务ID
	taskID := uuid.New().String()

	// 3. 从参数中获取当前用户ID
	// submittedByUserID 参数已经从handler层传递过来

	// 4. 创建账单生成任务记录
	now := time.Now()
	task := &entity.BillingGenerationTask{
		TaskID:              taskID,
		BillingCycleID:      req.BillingCycleID,
		TargetCustomerIds:   s.convertCustomerIDsToString(req.CustomerIDs),
		Status:              entity.TaskStatusPending,
		ProgressPercentage:  0,
		TotalItemsToProcess: s.calculateTotalItemsToProcess(len(req.CustomerIDs)),
		ItemsProcessedCount: 0,
		ErrorMessage:        nil,
		SubmittedByUserID:   &submittedByUserID,
		SubmitTime:          now,
		StartTime:           nil,
		EndTime:             nil,
	}

	// 5. 保存任务记录
	if err := s.billingGenerationTaskRepo.SaveTask(ctx, task); err != nil {
		if logger != nil {
			logger.Error("Failed to save billing generation task", zap.Error(err))
		}
		return nil, valueobject.ERROR_UNKNOWN, errors.New("保存账单生成任务失败")
	}

	// 6. 构建消息队列消息
	message := &entity.BillingGenerationMessage{
		TaskID:            taskID,
		BillingCycleID:    req.BillingCycleID,
		CustomerIDs:       req.CustomerIDs,
		StartTime:         startTime,
		EndTime:           endTime,
		DueDate:           dueDate,
		Currency:          req.Currency,
		Notes:             req.Notes,
		GeneratedByUserID: &submittedByUserID,
		UserTemplates:     s.convertToEntityUserTemplates(req.UserTemplates),
		MessageID:         "", // 将在生产者中生成
		CreatedAt:         now,
		RetryCount:        0,
	}

	// 7. 发送消息到队列
	if s.billingProducer != nil {
		if err := s.billingProducer.PublishBillingTask(ctx, message); err != nil {
			if logger != nil {
				logger.Error("Failed to publish billing task to queue",
					zap.String("taskId", taskID),
					zap.Error(err))
			}
			// 更新任务状态为失败
			s.billingGenerationTaskRepo.UpdateTaskStatus(ctx, taskID, entity.TaskStatusFailed)
			return nil, valueobject.ERROR_UNKNOWN, errors.New("发送任务到消息队列失败")
		}
	} else {
		if logger != nil {
			logger.Warn("Billing producer is not available, task will not be processed")
		}
		// 如果没有消息队列，将任务标记为失败
		s.billingGenerationTaskRepo.UpdateTaskStatus(ctx, taskID, entity.TaskStatusFailed)
		return nil, valueobject.ERROR_UNKNOWN, errors.New("消息队列服务不可用")
	}

	if logger != nil {
		logger.Info("Successfully created async billing task",
			zap.String("taskId", taskID),
			zap.Int64("billingCycleId", req.BillingCycleID),
			zap.Int("customerCount", len(req.CustomerIDs)))
	}

	return &AsyncGenerateBillingResponse{
		TaskID:         taskID,
		BillingCycleID: req.BillingCycleID,
		CustomerCount:  len(req.CustomerIDs),
		Status:         string(entity.TaskStatusPending),
		StatusName:     "待处理",
		SubmitTime:     now.Format("2006-01-02 15:04:05"),
		Message:        fmt.Sprintf("异步账单生成任务已创建，将处理 %d 个客户的账单", len(req.CustomerIDs)),
	}, valueobject.SUCCESS, nil
}

// validateAndParseAsyncGenerateBillingRequest 验证和解析异步生成账单请求参数
func (s *BillingServiceImpl) validateAndParseAsyncGenerateBillingRequest(req *AsyncGenerateBillingRequest) (time.Time, time.Time, *time.Time, error) {
	// 解析开始时间
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.StartTime, time.Local)
	if err != nil {
		return time.Time{}, time.Time{}, nil, errors.New("开始时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式")
	}

	// 解析结束时间
	endTime, err := time.ParseInLocation("2006-01-02 15:04:05", req.EndTime, time.Local)
	if err != nil {
		return time.Time{}, time.Time{}, nil, errors.New("结束时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式")
	}

	// 验证时间范围
	if !endTime.After(startTime) {
		return time.Time{}, time.Time{}, nil, errors.New("结束时间必须晚于开始时间")
	}

	// 验证客户ID列表
	if len(req.CustomerIDs) == 0 {
		return time.Time{}, time.Time{}, nil, errors.New("客户ID列表不能为空")
	}

	// 验证客户ID的唯一性
	customerIDSet := make(map[int64]bool)
	for _, customerID := range req.CustomerIDs {
		if customerID <= 0 {
			return time.Time{}, time.Time{}, nil, errors.New("客户ID必须大于0")
		}
		if customerIDSet[customerID] {
			return time.Time{}, time.Time{}, nil, errors.New("客户ID列表中存在重复项")
		}
		customerIDSet[customerID] = true
	}

	// 解析付款截止日期（可选）
	var dueDate *time.Time
	if req.DueDate != nil && *req.DueDate != "" {
		parsed, err := time.ParseInLocation("2006-01-02", *req.DueDate, time.Local)
		if err != nil {
			return time.Time{}, time.Time{}, nil, errors.New("付款截止日期格式错误，请使用 yyyy-MM-dd 格式")
		}
		dueDate = &parsed
	}

	return startTime, endTime, dueDate, nil
}

// convertCustomerIDsToString 将客户ID列表转换为字符串
func (s *BillingServiceImpl) convertCustomerIDsToString(customerIDs []int64) *string {
	if len(customerIDs) == 0 {
		return nil
	}

	var idStrings []string
	for _, id := range customerIDs {
		idStrings = append(idStrings, fmt.Sprintf("%d", id))
	}
	result := strings.Join(idStrings, ",")
	return &result
}

// calculateTotalItemsToProcess 计算预计处理项数
func (s *BillingServiceImpl) calculateTotalItemsToProcess(customerCount int) *int {
	// 简单估算：每个客户可能产生的账单数量
	estimated := customerCount
	return &estimated
}

// convertToEntityUserTemplates 转换用户模板配置为实体对象
func (s *BillingServiceImpl) convertToEntityUserTemplates(configs []AsyncUserTemplateConfig) []entity.UserShippingTemplate {
	templates := make([]entity.UserShippingTemplate, len(configs))
	for i, config := range configs {
		templates[i] = entity.UserShippingTemplate{
			UserID:          config.UserID,
			GeneralTemplate: config.GeneralTemplate,
			BatteryTemplate: config.BatteryTemplate,
			PostBoxTemplate: config.PostBoxTemplate,
		}
	}
	return templates
}

// GetBillingGenerationTaskDetailRequest 获取账单生成任务详情请求
type GetBillingGenerationTaskDetailRequest struct {
	TaskID string `uri:"taskId" binding:"required"` // 任务ID
}

// BillingGenerationTaskDetailDTO 账单生成任务详情DTO
type BillingGenerationTaskDetailDTO struct {
	TaskID              string  `json:"taskId"`              // 任务UUID
	BillingCycleID      int64   `json:"billingCycleId"`      // 关联的账期批次ID
	TargetCustomerIds   *string `json:"targetCustomerIds"`   // 目标客户ID列表
	TargetCustomerList  []int64 `json:"targetCustomerList"`  // 解析后的目标客户ID列表
	Status              string  `json:"status"`              // 任务状态
	StatusName          string  `json:"statusName"`          // 任务状态名称
	ProgressPercentage  int     `json:"progressPercentage"`  // 进度百分比（0-100）
	TotalItemsToProcess *int    `json:"totalItemsToProcess"` // 预计总处理项数
	ItemsProcessedCount int     `json:"itemsProcessedCount"` // 已处理项数
	ErrorMessage        *string `json:"errorMessage"`        // 错误信息
	SubmittedByUserID   *int64  `json:"submittedByUserId"`   // 任务提交者ID
	SubmittedByNickname *string `json:"submittedByNickname"` // 任务提交者昵称
	SubmittedByUsername *string `json:"submittedByUsername"` // 任务提交者用户名
	SubmitTime          string  `json:"submitTime"`          // 提交时间（yyyy-MM-dd HH:mm:ss格式）
	StartTime           *string `json:"startTime"`           // 处理开始时间（yyyy-MM-dd HH:mm:ss格式）
	EndTime             *string `json:"endTime"`             // 处理结束时间（yyyy-MM-dd HH:mm:ss格式）
	Duration            *int64  `json:"duration"`            // 执行耗时（秒）

	// 扩展信息
	IsCompleted         bool   `json:"isCompleted"`         // 是否已完成
	IsFailed            bool   `json:"isFailed"`            // 是否失败
	IsProcessing        bool   `json:"isProcessing"`        // 是否正在处理
	IsPending           bool   `json:"isPending"`           // 是否待处理
	CustomerCount       int    `json:"customerCount"`       // 客户数量
	ProgressDescription string `json:"progressDescription"` // 进度描述
}

// GetBillingGenerationTaskDetailResponse 获取账单生成任务详情响应
type GetBillingGenerationTaskDetailResponse struct {
	Task *BillingGenerationTaskDetailDTO `json:"task"` // 任务详情
}

// GetBillingGenerationTaskDetail 根据任务ID获取任务详情
func (s *BillingServiceImpl) GetBillingGenerationTaskDetail(ctx context.Context, req *GetBillingGenerationTaskDetailRequest) (*GetBillingGenerationTaskDetailResponse, int, error) {
	logger := util.GetLoggerFromContext(ctx)

	// 1. 参数验证
	if req.TaskID == "" {
		if logger != nil {
			logger.Warn("Invalid task ID", zap.String("taskId", req.TaskID))
		}
		return nil, valueobject.ERROR_INVALID_PARAMETER, errors.New("任务ID不能为空")
	}

	// 2. 查询任务详情
	task, err := s.billingGenerationTaskRepo.FindTaskByID(ctx, req.TaskID)
	if err != nil {
		if logger != nil {
			logger.Warn("Billing generation task not found",
				zap.String("taskId", req.TaskID),
				zap.Error(err))
		}
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("账单生成任务不存在")
	}

	// 3. 查询提交者用户信息
	var submittedByUser *entity.User
	if task.SubmittedByUserID != nil {
		user, err := s.userRepo.FindByID(ctx, *task.SubmittedByUserID)
		if err != nil {
			if logger != nil {
				logger.Warn("Failed to find task submitter user",
					zap.Int64("userId", *task.SubmittedByUserID),
					zap.Error(err))
			}
			// 不因为查询用户信息失败而中断整个请求，只是用户昵称会为空
		} else {
			submittedByUser = user
		}
	}

	// 4. 解析目标客户ID列表
	customerList, err := s.parseCustomerIDsFromString(task.TargetCustomerIds)
	if err != nil {
		if logger != nil {
			logger.Warn("Failed to parse target customer IDs",
				zap.String("taskId", req.TaskID),
				zap.String("targetCustomerIds", *task.TargetCustomerIds),
				zap.Error(err))
		}
		customerList = []int64{} // 设置为空列表，不中断请求
	}

	// 5. 构建任务详情DTO
	dto := &BillingGenerationTaskDetailDTO{
		TaskID:              task.TaskID,
		BillingCycleID:      task.BillingCycleID,
		TargetCustomerIds:   task.TargetCustomerIds,
		TargetCustomerList:  customerList,
		Status:              string(task.Status),
		StatusName:          task.GetStatusName(),
		ProgressPercentage:  task.ProgressPercentage,
		TotalItemsToProcess: task.TotalItemsToProcess,
		ItemsProcessedCount: task.ItemsProcessedCount,
		ErrorMessage:        task.ErrorMessage,
		SubmittedByUserID:   task.SubmittedByUserID,
		SubmitTime:          task.SubmitTime.Format("2006-01-02 15:04:05"),
		Duration:            task.GetDuration(),

		// 状态判断
		IsCompleted:         task.IsCompleted(),
		IsFailed:            task.IsFailed(),
		IsProcessing:        task.IsProcessing(),
		IsPending:           task.IsPending(),
		CustomerCount:       len(customerList),
		ProgressDescription: s.buildProgressDescription(task),
	}

	// 设置开始时间
	if task.StartTime != nil {
		startTimeStr := task.StartTime.Format("2006-01-02 15:04:05")
		dto.StartTime = &startTimeStr
	}

	// 设置结束时间
	if task.EndTime != nil {
		endTimeStr := task.EndTime.Format("2006-01-02 15:04:05")
		dto.EndTime = &endTimeStr
	}

	// 设置提交者信息
	if submittedByUser != nil {
		dto.SubmittedByNickname = &submittedByUser.Nickname
		dto.SubmittedByUsername = &submittedByUser.Username
	}

	if logger != nil {
		logger.Info("Successfully retrieved billing generation task detail",
			zap.String("taskId", req.TaskID),
			zap.String("status", string(task.Status)),
			zap.Int("progressPercentage", task.ProgressPercentage))
	}

	return &GetBillingGenerationTaskDetailResponse{
		Task: dto,
	}, valueobject.SUCCESS, nil
}

// parseCustomerIDsFromString 解析客户ID字符串为列表
func (s *BillingServiceImpl) parseCustomerIDsFromString(customerIDsStr *string) ([]int64, error) {
	if customerIDsStr == nil || *customerIDsStr == "" {
		return []int64{}, nil
	}

	idStrings := strings.Split(*customerIDsStr, ",")
	customerIDs := make([]int64, 0, len(idStrings))

	for _, idStr := range idStrings {
		trimmed := strings.TrimSpace(idStr)
		if trimmed == "" {
			continue
		}

		id, err := strconv.ParseInt(trimmed, 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析客户ID失败: %s", trimmed)
		}
		customerIDs = append(customerIDs, id)
	}

	return customerIDs, nil
}

// buildProgressDescription 构建进度描述
func (s *BillingServiceImpl) buildProgressDescription(task *entity.BillingGenerationTask) string {
	switch task.Status {
	case entity.TaskStatusPending:
		return "任务已提交，等待处理"
	case entity.TaskStatusProcessing:
		if task.TotalItemsToProcess != nil && *task.TotalItemsToProcess > 0 {
			return fmt.Sprintf("正在处理中，已完成 %d/%d 项（%d%%）",
				task.ItemsProcessedCount, *task.TotalItemsToProcess, task.ProgressPercentage)
		}
		return fmt.Sprintf("正在处理中，进度 %d%%", task.ProgressPercentage)
	case entity.TaskStatusCompleted:
		duration := task.GetDuration()
		if duration != nil {
			return fmt.Sprintf("处理完成，共处理 %d 项，耗时 %d 秒", task.ItemsProcessedCount, *duration)
		}
		return fmt.Sprintf("处理完成，共处理 %d 项", task.ItemsProcessedCount)
	case entity.TaskStatusFailed:
		if task.ErrorMessage != nil {
			return fmt.Sprintf("处理失败：%s", *task.ErrorMessage)
		}
		return "处理失败"
	default:
		return "未知状态"
	}
}

// analyzeRequiredTemplateTypes 分析运单实际需要的模板类型
func (s *BillingServiceImpl) analyzeRequiredTemplateTypes(manifests []*entity.Manifest) []int {
	requiredTemplateTypes := make(map[int]bool)

	for _, manifest := range manifests {
		// 根据运单的运费模板类型判断需要哪种模板
		templateType := manifest.ShippingFeeTemplateType
		if templateType == 0 {
			templateType = 1 // 默认为普通货物
		}
		requiredTemplateTypes[templateType] = true
	}

	// 转换为切片并排序
	templateTypes := make([]int, 0, len(requiredTemplateTypes))
	for templateType := range requiredTemplateTypes {
		templateTypes = append(templateTypes, templateType)
	}

	// 排序以确保一致性
	sort.Ints(templateTypes)
	return templateTypes
}

// checkMissingTemplates 检查缺少的必需模板
func (s *BillingServiceImpl) checkMissingTemplates(templates *entity.AppliedFreightTemplate, requiredTypes []int) []string {
	missingTemplates := make([]string, 0)

	for _, templateType := range requiredTypes {
		switch templateType {
		case 1:
			if templates.GeneralTemplate == nil {
				missingTemplates = append(missingTemplates, "普货模板")
			}
		case 2:
			if templates.BatteryTemplate == nil {
				missingTemplates = append(missingTemplates, "带电模板")
			}
		case 3:
			if templates.PostBoxTemplate == nil {
				missingTemplates = append(missingTemplates, "投函模板")
			}
		}
	}

	return missingTemplates
}
