package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// ProblemTicketQueryParams 问题工单查询参数
type ProblemTicketQueryParams struct {
	Status           string   // 工单状态
	ProblemTypeCode  string   // 问题类型
	TrackingNumber   string   // 物流单号
	CustomerID       int64    // 客户ID
	AssignedUserID   int64    // 处理人ID
	StartTime        string   // 创建时间范围开始
	EndTime          string   // 创建时间范围结束
	ManifestID       int64    // 运单ID
	Priority         int      // 优先级
}

// ProblemTicketRepository 问题工单仓储接口
type ProblemTicketRepository interface {
	// FindPaginated 分页查询问题工单
	FindPaginated(ctx context.Context, queryParams *ProblemTicketQueryParams, page, pageSize int) ([]*entity.ProblemTicket, int64, error)
	
	// GetByID 根据ID获取问题工单
	GetByID(ctx context.Context, id int64) (*entity.ProblemTicket, error)
	
	// Save 保存问题工单
	Save(ctx context.Context, ticket *entity.ProblemTicket) error
	
	// Update 更新问题工单
	Update(ctx context.Context, ticket *entity.ProblemTicket) error

	// MarkAllAsResolved 将所有待处理的问题工单标记为已处理
	// 返回处理的工单数量和可能的错误
	MarkAllAsResolved(ctx context.Context, operatorID int64, remark string) (int64, error)

	// MarkMultipleAsResolved 将指定ID列表的问题工单标记为已处理
	// 返回成功处理的工单数量和可能的错误
	MarkMultipleAsResolved(ctx context.Context, ticketIDs []int64, operatorID int64, remark string) (int64, error)

	// MarkAsPending 将指定ID的问题工单重新标记为待处理
	// 返回是否成功修改
	MarkAsPending(ctx context.Context, ticketID int64, operatorID int64, remark string) error
	
	// MarkMultipleAsPending 将指定ID列表的问题工单重新标记为待处理
	// 返回成功处理的工单数量和可能的错误
	MarkMultipleAsPending(ctx context.Context, ticketIDs []int64, operatorID int64, remark string) (int64, error)
	
	// CountPendingProblemTickets 统计待处理的问题工单数量
	CountPendingProblemTickets(ctx context.Context) (int64, error)
} 