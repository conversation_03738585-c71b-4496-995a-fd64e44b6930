@echo off
REM Docker构建脚本 (Windows版本)
REM 用法: scripts\build-docker.cmd [all|api|consumer]

setlocal enabledelayedexpansion

REM 设置默认参数
set ACTION=%1
if "%ACTION%"=="" set ACTION=all

echo [INFO] Docker构建脚本启动...

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装或未启动
    exit /b 1
)

REM 根据参数执行构建
if "%ACTION%"=="api" (
    call :build_api_server
) else if "%ACTION%"=="consumer" (
    call :build_billing_consumer
) else if "%ACTION%"=="all" (
    call :build_api_server
    call :build_billing_consumer
) else (
    echo [ERROR] 未知参数: %ACTION%
    echo 用法: %0 [all^|api^|consumer]
    echo   all      - 构建所有镜像（默认）
    echo   api      - 仅构建API服务器镜像
    echo   consumer - 仅构建账单消费者镜像
    exit /b 1
)

echo [INFO] 查看构建的镜像：
docker images | findstr zebra-hub-system

echo [SUCCESS] 构建完成！
goto :eof

:build_api_server
echo [INFO] 构建API服务器镜像...
docker build --build-arg APP_NAME=api-server --build-arg BINARY_NAME=zebra-hub-system -t zebra-hub-system/api-server:latest -f Dockerfile .
if errorlevel 1 (
    echo [ERROR] API服务器镜像构建失败
    exit /b 1
)
echo [SUCCESS] API服务器镜像构建完成
goto :eof

:build_billing_consumer
echo [INFO] 构建账单消费者镜像...
docker build -t zebra-hub-system/billing-consumer:latest -f Dockerfile.consumer .
if errorlevel 1 (
    echo [ERROR] 账单消费者镜像构建失败
    exit /b 1
)
echo [SUCCESS] 账单消费者镜像构建完成
goto :eof 