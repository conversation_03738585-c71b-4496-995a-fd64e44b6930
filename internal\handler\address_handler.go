package handler

import (
	"net/http"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// AddressHandler 地址处理器
type AddressHandler struct {
	addressService service.AddressService
}

// NewAddressHandler 创建地址处理器
func NewAddressHandler(addressService service.AddressService) *AddressHandler {
	return &AddressHandler{
		addressService: addressService,
	}
}

// GetAddressInfo 根据邮编获取地址信息
// @Summary 根据邮编获取地址信息
// @Description 根据日本邮编获取详细的地址信息，包括都道府县、市区町村等
// @Tags Address
// @Accept json
// @Produce json
// @Param zipCode query string true "邮编"
// @Success 200 {object} util.Response{data=service.GetAddressInfoResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "邮编不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /address/info [get]
func (h *AddressHandler) GetAddressInfo(c *gin.Context) {
	var req service.GetAddressInfoRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请求参数错误", http.StatusBadRequest)
		return
	}

	resp, errorCode, err := h.addressService.GetAddressInfo(c, &req)
	if err != nil {
		switch errorCode {
		case valueobject.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID:
			util.ResponseError(c, errorCode, "邮编格式无效", http.StatusBadRequest)
		case valueobject.ERROR_MANIFEST_ZIPCODE_NOT_EXIST:
			util.ResponseError(c, errorCode, "该邮编不存在", http.StatusNotFound)
		default:
			util.ResponseError(c, valueobject.ERROR_UNKNOWN, "获取地址信息失败", http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, resp)
} 