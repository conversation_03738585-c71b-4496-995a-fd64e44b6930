package repository

import (
	"context"
	"time"
	"zebra-hub-system/internal/domain/entity"
)

// ManifestFinancialAdjustmentRepository 运单财务调整记录仓储接口
type ManifestFinancialAdjustmentRepository interface {
	// FindByManifestID 根据运单ID查询财务调整记录
	FindByManifestID(ctx context.Context, manifestID int64) ([]*entity.ManifestFinancialAdjustment, error)

	// FindByID 根据ID查询财务调整记录
	FindByID(ctx context.Context, id int64) (*entity.ManifestFinancialAdjustment, error)

	// FindByIDs 根据ID列表查询财务调整记录
	FindByIDs(ctx context.Context, ids []int64) ([]*entity.ManifestFinancialAdjustment, error)

	// CountByFilters 根据条件统计财务调整记录数量
	CountByFilters(
		ctx context.Context,
		manifestID *int64,
		adjustmentType *string,
		effectiveDateStart *time.Time,
		effectiveDateEnd *time.Time,
		customerAccountID *int64,
		trackingNumber *string,
	) (int64, error)

	// FindByFilters 根据条件查询财务调整记录
	FindByFilters(
		ctx context.Context,
		manifestID *int64,
		adjustmentType *string,
		effectiveDateStart *time.Time,
		effectiveDateEnd *time.Time,
		customerAccountID *int64,
		trackingNumber *string,
		orderBy string,
		isAsc bool,
		page, pageSize int,
	) ([]*entity.ManifestFinancialAdjustment, error)

	// Save 保存财务调整记录
	Save(ctx context.Context, adjustment *entity.ManifestFinancialAdjustment) error

	// Update 更新财务调整记录
	Update(ctx context.Context, adjustment *entity.ManifestFinancialAdjustment) error

	// Delete 删除财务调整记录
	Delete(ctx context.Context, id int64) error
}
