# 外部系统创建订单接口文档

## 接口信息

- **URL**: `POST /api/v1/external/orders`
- **功能**: 供外部系统对接的创建订单接口，支持订单号重复时的更新操作
- **认证**: 需要 Bearer Token 认证

## 请求参数

### 请求体 (JSON)

```json
{
  "merchantOrderNumber": "MERCHANT_ORDER_20241217001",
  "shipmentTypeId": 1,
  "receiverName": "田中太郎",
  "receiverPhone": "090-1234-5678",
  "receiverZipCode": "100-0001",
  "receiverAddress": "東京都千代田区千代田1-1",
  "items": [
    {
      "name": "手机壳",
      "quantity": 2
    },
    {
      "name": "数据线",
      "quantity": 1
    }
  ]
}
```

### 参数说明

| 字段名              | 类型   | 必填 | 说明                                            |
| ------------------- | ------ | ---- | ----------------------------------------------- |
| merchantOrderNumber | string | 是   | 商家订单号（支持重复提交更新）                  |
| shipmentTypeId      | int64  | 是   | 货物类型 ID (1-普货, 2-带电货物, 3-投函货物)    |
| receiverName        | string | 是   | 收件人姓名                                      |
| receiverPhone       | string | 是   | 收件人电话（日本手机号格式）                    |
| receiverZipCode     | string | 是   | 收件人邮编（日本邮编格式，7 位数字或 XXX-XXXX） |
| receiverAddress     | string | 是   | 收件人地址                                      |
| items               | array  | 是   | 物品列表                                        |
| items[].name        | string | 是   | 物品名称                                        |
| items[].quantity    | int    | 是   | 物品数量（最小值：1）                           |

## 响应参数

### 成功响应 (HTTP 200)

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:35:00Z",
  "data": {
    "trackingNumber": "JP123456789012",
    "merchantOrderNumber": "MERCHANT_ORDER_20241217001",
    "systemOrderNo": "SYS_1_JP123456789012"
  }
}
```

### 响应字段说明

| 字段名              | 类型   | 说明         |
| ------------------- | ------ | ------------ |
| trackingNumber      | string | 分配的运单号 |
| merchantOrderNumber | string | 商家订单号   |
| systemOrderNo       | string | 系统订单号   |

### 错误响应示例

#### 1. 参数错误 (HTTP 400)

```json
{
  "success": false,
  "errorCode": 100003,
  "errorMessage": "缺少必要参数",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:30:00Z",
  "data": null
}
```

#### 2. 订单不允许更新 (HTTP 409)

```json
{
  "success": false,
  "errorCode": 201012,
  "errorMessage": "订单号 MERCHANT_ORDER_001 已存在且已揽件，无法更新",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:30:00Z",
  "data": null
}
```

## 业务逻辑说明

### 订单号重复处理

**创建 vs 更新逻辑**：

- **首次提交**：创建新运单，分配新的运单号和系统订单号
- **重复提交**：
  - 如果运单状态 = 1（预报），执行更新操作，**保持原有运单号不变**
  - 如果运单状态 ≠ 1（已揽件/已发货/已签收），返回错误 201012

### 运单状态说明

| 状态值 | 状态名称 | 是否允许更新 | 说明             |
| ------ | -------- | ------------ | ---------------- |
| 1      | 预报     | ✅ 是        | 允许更新所有信息 |
| 2      | 已揽件   | ❌ 否        | 不允许更新       |
| 3      | 已发货   | ❌ 否        | 不允许更新       |
| 4      | 已签收   | ❌ 否        | 不允许更新       |

### 数据校验规则

1. **收件人电话**：必须符合日本手机号格式
2. **收件人邮编**：必须是有效的日本邮编（存在于邮编库中）
3. **物品数量**：每个物品数量必须 ≥ 1

### 费用计算

- **偏远费**：非投函货物 + 冲绳县邮编 = 自动加收 100 元偏远费
- **系统订单号规则**：`SYS_{货物类型ID}_{运单号}`

## 错误码对照表

| 错误码 | 说明                         | HTTP 状态码 |
| ------ | ---------------------------- | ----------- |
| 100000 | 操作成功                     | 200         |
| 100003 | 缺少必要参数                 | 400         |
| 100004 | 未授权/未登录                | 401         |
| 201010 | 电话号码格式无效             | 400         |
| 201011 | 邮编格式无效或不存在         | 400         |
| 201012 | 商家订单号已存在且不允许更新 | 409         |
| 202003 | 未找到匹配的运单号渠道       | 400         |
| 202004 | 运单号渠道未启用             | 400         |
| 202005 | 所选渠道可用单号不足         | 409         |

## 使用示例

### 示例 1：创建新订单

```bash
curl -X POST "https://api.example.com/api/v1/external/orders" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "merchantOrderNumber": "ORDER_20241217_001",
    "shipmentTypeId": 1,
    "receiverName": "田中太郎",
    "receiverPhone": "090-1234-5678",
    "receiverZipCode": "100-0001",
    "receiverAddress": "東京都千代田区千代田1-1",
    "items": [
      {
        "name": "手机壳",
        "quantity": 2
      }
    ]
  }'
```

### 示例 2：更新现有订单（相同订单号）

```bash
curl -X POST "https://api.example.com/api/v1/external/orders" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "merchantOrderNumber": "ORDER_20241217_001",
    "shipmentTypeId": 1,
    "receiverName": "田中花子",
    "receiverPhone": "080-9876-5432",
    "receiverZipCode": "900-0001",
    "receiverAddress": "沖縄県那覇市...",
    "items": [
      {
        "name": "数据线",
        "quantity": 3
      }
    ]
  }'
```

### 示例 3：冲绳县订单（自动加收偏远费）

```bash
curl -X POST "https://api.example.com/api/v1/external/orders" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "merchantOrderNumber": "OKINAWA_ORDER_001",
    "shipmentTypeId": 1,
    "receiverName": "比嘉太郎",
    "receiverPhone": "************",
    "receiverZipCode": "900-0001",
    "receiverAddress": "沖縄県那覇市港町1-1",
    "items": [
      {
        "name": "电子产品",
        "quantity": 1
      }
    ]
  }'
```

## 集成建议

### 幂等性处理

1. **推荐**：使用有意义的商家订单号，便于追踪和重试
2. **重试机制**：网络超时时可安全重试，相同订单号不会创建重复数据
3. **状态检查**：提交前可先查询订单状态，避免不必要的更新尝试

### 测试建议

1. **参数校验测试**：测试各种无效的电话号码和邮编格式
2. **重复订单测试**：测试相同订单号的多次提交
3. **状态变更测试**：测试运单状态变更后的更新限制
4. **偏远费测试**：使用冲绳县邮编测试偏远费计算

## 常见问题

**Q: 为什么更新时不分配新的运单号？**  
A: 为了保证业务连续性，更新操作保持原有运单号不变，避免下游系统混乱。

**Q: 如何判断是创建还是更新？**  
A: 系统根据商家订单号自动判断，首次提交创建，重复提交且状态为预报时更新。

**Q: 运单状态什么时候会变为"已揽件"？**  
A: 当运单被物流系统扫描揽收后，状态会自动变为"已揽件"，此时不允许再更新。

**Q: 冲绳县偏远费如何收取？**  
A: 系统根据邮编自动判断，冲绳县且非投函货物自动加收 100 元偏远费。
