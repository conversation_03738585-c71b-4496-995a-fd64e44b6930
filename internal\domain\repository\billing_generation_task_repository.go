package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// BillingGenerationTaskRepository 账单生成任务仓储接口
type BillingGenerationTaskRepository interface {
	// SaveTask 保存任务
	SaveTask(ctx context.Context, task *entity.BillingGenerationTask) error

	// FindTaskByID 根据任务ID查询任务
	FindTaskByID(ctx context.Context, taskID string) (*entity.BillingGenerationTask, error)

	// FindTasksByBillingCycleID 根据账期批次ID分页查询任务列表
	FindTasksByBillingCycleID(ctx context.Context, billingCycleID int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error)

	// FindTasksByUserID 根据用户ID分页查询用户提交的任务列表
	FindTasksByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error)

	// FindTasksByBillingCycleAndUser 根据账期批次ID和用户ID分页查询任务列表
	FindTasksByBillingCycleAndUser(ctx context.Context, billingCycleID int64, userID *int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error)

	// UpdateTaskStatus 更新任务状态
	UpdateTaskStatus(ctx context.Context, taskID string, status entity.TaskStatus) error

	// UpdateTaskProgress 更新任务进度
	UpdateTaskProgress(ctx context.Context, taskID string, progressPercentage int, itemsProcessedCount int) error

	// DeleteTask 删除任务
	DeleteTask(ctx context.Context, taskID string) error
} 