package service

import (
	"fmt"
	"zebra-hub-system/internal/domain/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// GetAddressInfoRequest 根据邮编获取地址信息请求
type GetAddressInfoRequest struct {
	ZipCode string `form:"zipCode" json:"zipCode" binding:"required"` // 邮编
}

// GetAddressInfoResponse 根据邮编获取地址信息响应
type GetAddressInfoResponse struct {
	*service.AddressInfo
}

// AddressService 地址服务接口
type AddressService interface {
	// GetAddressInfo 根据邮编获取地址信息
	GetAddressInfo(ginCtx *gin.Context, req *GetAddressInfoRequest) (*GetAddressInfoResponse, int, error)
}

// AddressServiceImpl 地址服务实现
type AddressServiceImpl struct {
	zipCodeService service.ZipCodeService
}

// NewAddressService 创建地址服务
func NewAddressService(zipCodeService service.ZipCodeService) AddressService {
	return &AddressServiceImpl{
		zipCodeService: zipCodeService,
	}
}

// GetAddressInfo 根据邮编获取地址信息
func (s *AddressServiceImpl) GetAddressInfo(ginCtx *gin.Context, req *GetAddressInfoRequest) (*GetAddressInfoResponse, int, error) {
	logger := ginCtx.MustGet("logger").(*zap.Logger)
	
	logger.Debug("Getting address info", zap.String("zipCode", req.ZipCode))
	
	// 检查邮编格式
	if !s.zipCodeService.IsValidFormat(req.ZipCode) {
		logger.Warn("Invalid ZIP code format", zap.String("zipCode", req.ZipCode))
		return nil, 201009, fmt.Errorf("邮编格式无效")
	}
	
	// 检查邮编是否存在
	normalizedZipCode := s.zipCodeService.NormalizeZipCode(req.ZipCode)
	if !s.zipCodeService.IsExist(normalizedZipCode) {
		logger.Warn("ZIP code does not exist", zap.String("zipCode", req.ZipCode), zap.String("normalized", normalizedZipCode))
		return nil, 201010, fmt.Errorf("该邮编不存在")
	}
	
	// 获取地址信息
	addressInfo, err := s.zipCodeService.GetAddressInfo(normalizedZipCode)
	if err != nil {
		logger.Error("Failed to get address info", zap.Error(err), zap.String("zipCode", normalizedZipCode))
		return nil, 100001, err
	}
	
	logger.Debug("Successfully retrieved address info", 
		zap.String("zipCode", normalizedZipCode),
		zap.String("fullAddressJA", addressInfo.FullAddressJA),
		zap.String("prefectureJA", addressInfo.PrefectureJA))
	
	return &GetAddressInfoResponse{
		AddressInfo: addressInfo,
	}, 100000, nil
} 