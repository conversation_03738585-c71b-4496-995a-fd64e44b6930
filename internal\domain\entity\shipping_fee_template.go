package entity

import (
	"zebra-hub-system/internal/util"
)

// ShippingFeeTemplate 运费模板实体
type ShippingFeeTemplate struct {
	ID                      int64     `json:"id" gorm:"primaryKey"`
	Name                    string    `json:"name" gorm:"column:name"`
	FirstWeightPrice        float64   `json:"firstWeightPrice" gorm:"column:first_weight_price"`
	FirstWeightRange        float64   `json:"firstWeightRange" gorm:"column:first_weight_range"`
	ContinuedWeightPrice    float64   `json:"continuedWeightPrice" gorm:"column:continued_weight_price"`
	ContinuedWeightInterval float64   `json:"continuedWeightInterval" gorm:"column:continued_weight_interval"`
	BulkCoefficient         int       `json:"bulkCoefficient" gorm:"column:bulk_coefficient"`
	ThreeSidesStart         float64   `json:"threeSidesStart" gorm:"column:three_sides_start"`

	CreateTime              util.FormattedTime `json:"createTime" gorm:"column:create_time"`
	UpdateTime              util.FormattedTime `json:"updateTime" gorm:"column:update_time"`
}

// TableName 指定表名
func (ShippingFeeTemplate) TableName() string {
	return "tb_shipping_fee_template"
}

// ShippingFeeTemplateUser 运费模板与用户关系实体
type ShippingFeeTemplateUser struct {
	ID         int64 `json:"id" gorm:"primaryKey"`
	UserID     int64 `json:"userId" gorm:"column:user_id"`
	TemplateID int64 `json:"templateId" gorm:"column:template_id"`
	Type       int   `json:"type" gorm:"column:type"` // 模板类型：1-普通模板，2-带电模板，3-投函模板，6-特殊模板
}

// TableName 指定表名
func (ShippingFeeTemplateUser) TableName() string {
	return "tb_shipping_fee_template_user"
} 