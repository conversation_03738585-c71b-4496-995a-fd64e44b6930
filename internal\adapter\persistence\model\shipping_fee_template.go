package model

import (
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/util"
)

// ShippingFeeTemplatePO 运费模板持久化对象
type ShippingFeeTemplatePO struct {
	ID                      int64     `gorm:"column:id;primaryKey;autoIncrement"`
	Name                    string    `gorm:"column:name"`
	FirstWeightPrice        float64   `gorm:"column:first_weight_price;type:decimal(19,2)"`
	FirstWeightRange        float64   `gorm:"column:first_weight_range;type:decimal(19,2)"`
	ContinuedWeightPrice    float64   `gorm:"column:continued_weight_price;type:decimal(19,2)"`
	ContinuedWeightInterval float64   `gorm:"column:continued_weight_interval;type:decimal(19,2)"`
	BulkCoefficient         int       `gorm:"column:bulk_coefficient"`
	ThreeSidesStart         float64   `gorm:"column:three_sides_start;type:decimal(19,2)"`

	CreateTime              time.Time `gorm:"column:create_time;autoCreateTime"`
	UpdateTime              time.Time `gorm:"column:update_time;autoUpdateTime"`
}

// TableName 指定表名
func (ShippingFeeTemplatePO) TableName() string {
	return "tb_shipping_fee_template"
}

// ToEntity 转换为实体
func (po *ShippingFeeTemplatePO) ToEntity() *entity.ShippingFeeTemplate {
	return &entity.ShippingFeeTemplate{
		ID:                      po.ID,
		Name:                    po.Name,
		FirstWeightPrice:        po.FirstWeightPrice,
		FirstWeightRange:        po.FirstWeightRange,
		ContinuedWeightPrice:    po.ContinuedWeightPrice,
		ContinuedWeightInterval: po.ContinuedWeightInterval,
		BulkCoefficient:         po.BulkCoefficient,
		ThreeSidesStart:         po.ThreeSidesStart,

		CreateTime:              util.NewFormattedTime(po.CreateTime),
		UpdateTime:              util.NewFormattedTime(po.UpdateTime),
	}
} 