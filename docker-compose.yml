version: "3.8"

services:
  # API服务器
  api-server:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_NAME: api-server
        BINARY_NAME: zebra-hub-system
    image: zebra-hub-system/api-server:latest
    container_name: zebra-api-server
    ports:
      - "8080:8080"
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - ./assets:/app/assets
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - zebra-network

  # 账单消费者
  billing-consumer:
    build:
      context: .
      dockerfile: Dockerfile.consumer
      args:
        APP_NAME: billing-consumer
    image: zebra-hub-system/billing-consumer:latest
    container_name: zebra-billing-consumer
    volumes:
      - ./configs:/app/configs
      - ./data:/app/data
      - ./assets:/app/assets
    environment:
      - TZ=Asia/Shanghai
    restart: unless-stopped
    networks:
      - zebra-network

networks:
  zebra-network:
    driver: bridge
