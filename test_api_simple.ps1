# Simple API test script

Write-Host "=== Testing File Upload API ===" -ForegroundColor Green

# Test server status
Write-Host "1. Checking server status..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/upload/info" -Method GET -ErrorAction Stop
    Write-Host "Server is running" -ForegroundColor Green
    Write-Host "Response status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    if ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "Server is running, requires JWT auth (401)" -ForegroundColor Yellow
    } elseif ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "Route not found (404)" -ForegroundColor Red
    } else {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test login
Write-Host "`n2. Testing login..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "admin"
        password = "123456"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/login" -Method POST -ContentType "application/json" -Body $loginBody -ErrorAction Stop
    Write-Host "Login response status: $($loginResponse.StatusCode)" -ForegroundColor Green
    
    if ($loginResponse.StatusCode -eq 200) {
        $loginData = $loginResponse.Content | ConvertFrom-Json
        if ($loginData.success) {
            $token = $loginData.data.token
            Write-Host "Login successful, token: $($token.Substring(0, 20))..." -ForegroundColor Green
            
            # Test upload config with token
            Write-Host "`n3. Testing upload config with token..." -ForegroundColor Yellow
            $headers = @{
                "Authorization" = "Bearer $token"
            }
            
            $configResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/upload/info" -Method GET -Headers $headers -ErrorAction Stop
            Write-Host "Upload config response status: $($configResponse.StatusCode)" -ForegroundColor Green
            
            $configData = $configResponse.Content | ConvertFrom-Json
            Write-Host "Upload config:" -ForegroundColor Cyan
            Write-Host ($configData | ConvertTo-Json -Depth 3) -ForegroundColor White
        } else {
            Write-Host "Login failed: $($loginData.errorMessage)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Login test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test completed ===" -ForegroundColor Green 