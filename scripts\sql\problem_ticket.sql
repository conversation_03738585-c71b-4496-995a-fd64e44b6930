-- 问题运单工单表
CREATE TABLE IF NOT EXISTS `problem_manifest_tickets` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工单主键ID',
  `manifest_id` bigint(20) NOT NULL COMMENT '关联的运单ID',
  `tracking_number` varchar(50) DEFAULT NULL COMMENT '关联运单的物流单号',
  `customer_account_id` bigint(20) DEFAULT NULL COMMENT '关联运单所属的客户ID',
  `problem_type_code` varchar(50) NOT NULL COMMENT '问题类型代码',
  `problem_description` text DEFAULT NULL COMMENT '问题描述',
  `status` varchar(20) NOT NULL COMMENT '当前工单处理状态',
  `priority` int(11) DEFAULT 0 COMMENT '问题优先级',
  `assigned_to_user_id` bigint(20) DEFAULT NULL COMMENT '处理人ID',
  `remarks` text DEFAULT NULL COMMENT '处理备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '工单创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '工单最后更新时间',
  `resolved_time` datetime DEFAULT NULL COMMENT '解决时间',
  PRIMARY KEY (`id`),
  INDEX `idx_manifest_id` (`manifest_id`),
  INDEX `idx_tracking_number` (`tracking_number`),
  INDEX `idx_customer_id` (`customer_account_id`),
  INDEX `idx_assigned_user` (`assigned_to_user_id`),
  INDEX `idx_problem_type` (`problem_type_code`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题运单工单表';

-- 问题类型字典表
CREATE TABLE IF NOT EXISTS `problem_type_dict` (
  `code` varchar(50) NOT NULL COMMENT '问题类型代码',
  `name` varchar(100) NOT NULL COMMENT '问题类型名称',
  `description` varchar(255) DEFAULT NULL COMMENT '问题类型描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问题类型字典表';

-- 插入一些默认的问题类型
INSERT INTO `problem_type_dict` (`code`, `name`, `description`, `sort_order`, `is_active`) VALUES
('ADDRESS_ERROR', '地址错误', '收货地址信息有误', 10, 1),
('PROHIBITED_ITEM', '违禁品', '包含禁运物品', 20, 1),
('WEIGHT_EXCEED', '超重', '包裹重量超过限制', 30, 1),
('SIZE_EXCEED', '超尺寸', '包裹尺寸超过限制', 40, 1),
('DECLARATION_ERROR', '申报错误', '商品申报信息有误', 50, 1),
('PAYMENT_ISSUE', '支付问题', '订单支付异常', 60, 1),
('CUSTOMS_HOLD', '海关扣留', '包裹被海关扣留', 70, 1),
('RETURN_REQUEST', '退货申请', '客户申请退货', 80, 1),
('OTHER', '其他问题', '其他未分类问题', 999, 1); 