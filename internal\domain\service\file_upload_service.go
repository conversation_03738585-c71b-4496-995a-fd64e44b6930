package service

import (
	"context"
	"io"
)

// UploadFileRequest 上传文件请求
type UploadFileRequest struct {
	FileName    string    `json:"fileName"`    // 原始文件名
	FileSize    int64     `json:"fileSize"`    // 文件大小（字节）
	ContentType string    `json:"contentType"` // 文件MIME类型
	FileData    io.Reader `json:"-"`           // 文件数据流
	BusinessType string   `json:"businessType"` // 业务类型，如"compensation_proof"
}

// UploadFileResponse 上传文件响应
type UploadFileResponse struct {
	FileUrl    string `json:"fileUrl"`    // 文件访问URL
	FileName   string `json:"fileName"`   // 存储的文件名
	FileSize   int64  `json:"fileSize"`   // 文件大小
	UploadTime string `json:"uploadTime"` // 上传时间
}

// FileUploadService 文件上传服务接口
type FileUploadService interface {
	// UploadImage 上传图片文件
	UploadImage(ctx context.Context, req *UploadFileRequest) (*UploadFileResponse, int, error)
	
	// DeleteFile 删除文件
	DeleteFile(ctx context.Context, fileUrl string) error
	
	// ValidateImageFile 验证图片文件
	ValidateImageFile(fileName string, fileSize int64, contentType string) error
} 