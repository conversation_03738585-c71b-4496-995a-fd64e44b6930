# 财务模块 - 账单导出接口改造修改说明

## 改造概述

本次改造将原有的运费账单导出接口进行了功能扩展，新增了根据账单记录 ID 导出 Excel 的功能。改造的核心目标是：

- 保持原有 Excel 样式不变
- 在运单明细末尾添加财务调整明细
- 根据账单记录 ID 查询对应的 billing_record_items 和 billing_financial_adjustment_snapshots

## 改造内容

### 1. 新增 DTO 结构

#### 文件：`internal/domain/dto/finance_dto.go`

新增了以下 DTO 结构：

```go
// BillingRecordExportRequest 根据账单记录ID导出Excel请求参数
type BillingRecordExportRequest struct {
	BillingRecordID int64 `json:"billingRecordId" binding:"required" example:"123"` // 账单记录ID
}

// BillingAdjustmentItem 账单调整项（用于Excel导出）
type BillingAdjustmentItem struct {
	AdjustmentType       string  `json:"adjustmentType"`       // 调整类型
	AdjustmentTypeName   string  `json:"adjustmentTypeName"`   // 调整类型名称
	Description          string  `json:"description"`          // 调整描述/原因
	Amount               float64 `json:"amount"`               // 调整金额
	Currency             string  `json:"currency"`             // 货币单位
	EffectiveDate        string  `json:"effectiveDate"`        // 费用实际发生/确认日期
	ManifestExpressNumber string `json:"manifestExpressNumber"` // 关联运单快递单号
	ManifestOrderNo      string  `json:"manifestOrderNo"`      // 关联运单系统单号
	ManifestReceiverName string  `json:"manifestReceiverName"` // 关联运单收件人
	OriginalCreateTime   string  `json:"originalCreateTime"`   // 原始记录创建时间
}
```

### 2. 扩展 FinanceService 接口

#### 文件：`internal/domain/service/finance_service.go`

新增接口方法：

```go
// GenerateBillingRecordExcel 根据账单记录ID生成Excel
GenerateBillingRecordExcel(ctx context.Context, req dto.BillingRecordExportRequest) ([]byte, string, error)
```

### 3. 实现新的服务方法

#### 文件：`internal/domain/service/impl/finance_service_impl.go`

#### 3.1 更新构造函数

- 添加了`billingService`依赖注入
- 更新了`NewFinanceService`构造函数签名

#### 3.2 新增核心方法

**主方法：`GenerateBillingRecordExcel`**

- 获取账单记录详情
- 分页获取所有账单明细数据
- 分页获取所有财务调整快照数据
- 数据转换和 Excel 生成

**辅助方法：**

- `convertBillingItemsToShippingBillItems`: 转换账单明细为运费账单项格式
- `convertAdjustmentSnapshotsToAdjustmentItems`: 转换财务调整快照为调整项格式
- `buildTemplateDetailsFromBillingRecord`: 从账单记录构建模板详情
- `generateBillingRecordExcel`: 生成账单记录 Excel 文件

### 4. 新增 Handler 方法

#### 文件：`internal/handler/finance_handler.go`

新增`ExportBillingRecordExcel`方法：

- 参数验证和绑定
- 错误处理和状态码映射
- 文件下载响应处理
- 中文文件名 URL 编码

### 5. 更新路由配置

#### 文件：`internal/router/router.go`

新增路由：

```go
financeRoutes.POST("/billing-record/export", financeHandler.ExportBillingRecordExcel)
```

### 6. 更新主程序依赖注入

#### 文件：`cmd/api-server/main.go`

更新 FinanceService 初始化：

```go
financeService := impl.NewFinanceService(manifestRepo, shippingFeeTemplateRepo, userRepo, billingService)
```

## 技术实现细节

### 1. 数据获取策略

- 使用分页方式获取所有账单明细和调整快照数据
- 每页 100 条记录，循环获取直到获取完所有数据
- 避免一次性加载大量数据导致内存问题

### 2. 数据转换处理

- 安全处理指针字段的空值检查
- 时间字段格式化处理
- 类型转换和字段映射

### 3. Excel 生成特性

- 保持与原有运费账单报表完全相同的样式
- 在运单明细后追加财务调整明细部分
- 包含完整的样式设置（标题、表头、内容、金额格式等）
- 支持中文文件名和 URL 编码

### 4. 错误处理

- 完整的错误码映射
- HTTP 状态码正确设置
- 详细的错误信息返回

## Excel 文件结构

生成的 Excel 文件包含以下部分：

1. **标题信息**

   - 公司名称和客户昵称
   - 账单基本信息（编号、账期、总金额、状态）
   - 运费模板信息

2. **运单明细部分**

   - 22 列详细的运单信息
   - 包含费用计算明细
   - 运单明细小计

3. **财务调整明细部分**（新增）

   - 10 列调整信息
   - 包含调整类型、金额、关联运单等
   - 财务调整小计

4. **汇总信息**
   - 账单总计
   - 报表生成时间

## 测试验证

### 编译验证

```bash
go build -v ./cmd/api-server
```

编译成功，无错误。

### 功能验证要点

1. 参数验证：账单记录 ID 必须大于 0
2. 数据获取：正确获取账单记录、明细和调整快照
3. 数据转换：正确转换为 Excel 所需格式
4. Excel 生成：保持原有样式，正确添加调整明细
5. 文件下载：正确设置响应头和文件名编码

## API 接口信息

- **接口路径**: `POST /api/v1/finance/billing-record/export`
- **请求参数**: `{"billingRecordId": 123}`
- **响应类型**: Excel 文件下载
- **文件名格式**: `{客户昵称}_账单_{账单编号}_{账单日期}.xlsx`

## 注意事项

1. **内存使用**: 大数据量时需注意 Excel 文件生成的内存占用
2. **性能考虑**: 分页获取数据避免一次性加载过多记录
3. **错误处理**: 完整的业务错误码和 HTTP 状态码映射
4. **文件编码**: 支持中文文件名的 URL 编码处理
5. **样式一致性**: 确保与原有 Excel 报表样式完全一致

## 相关文件清单

### 新增文件

- `docs/财务模块_根据账单记录ID导出Excel_API文档.md`

### 修改文件

- `internal/domain/dto/finance_dto.go`
- `internal/domain/service/finance_service.go`
- `internal/domain/service/impl/finance_service_impl.go`
- `internal/handler/finance_handler.go`
- `internal/router/router.go`
- `cmd/api-server/main.go`

## 更新日志

- **2024-01-15**: 完成账单导出接口改造，新增根据账单记录 ID 导出 Excel 功能
- 保持原有 Excel 样式不变
- 在运单明细末尾添加财务调整明细
- 完整的错误处理和文档支持
