package util

import (
	"context"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var Logger *zap.Logger

func InitLogger(mode string) {
	var config zap.Config
	if mode == "debug" {
		config = zap.NewDevelopmentConfig()
		config.EncoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder // 在开发模式下使用带颜色的级别输出
	} else {
		config = zap.NewProductionConfig()
	}

	// 可以根据需要自定义更多配置，例如输出到文件
	// config.OutputPaths = []string{"stdout", "./app.log"}
	// config.ErrorOutputPaths = []string{"stderr", "./app.error.log"}

	// 设置时间编码器，确保时间格式是我们期望的（如果需要与 DefaultTimeFormat 一致）
	config.EncoderConfig.EncodeTime = zapcore.TimeEncoderOfLayout(DefaultTimeFormat)
	config.EncoderConfig.TimeKey = "timestamp" // 将默认的 "ts" 改为 "timestamp"
	config.EncoderConfig.LevelKey = "level"
	config.EncoderConfig.NameKey = "logger"
	config.EncoderConfig.CallerKey = "caller"
	config.EncoderConfig.MessageKey = "message"
	config.EncoderConfig.StacktraceKey = "stacktrace"

	var err error
	Logger, err = config.Build()
	if err != nil {
		panic("failed to initialize zap logger: " + err.Error())
	}

	//替换zap包中全局的logger实例，后续在其他包中只需使用zap.L()调用即可
	zap.ReplaceGlobals(Logger)
}

// SyncLogger 刷新缓存区的日志 (在程序退出前调用)
func SyncLogger() {
	if Logger != nil {
		_ = Logger.Sync()
	}
} 

// GetLoggerFromContext извлекает логгер из контекста.
// Если логгер не найден в контексте, возвращает глобальный логгер zap.L().
func GetLoggerFromContext(ctx context.Context) *zap.Logger {
	if logger, ok := ctx.Value(LoggerContextKey).(*zap.Logger); ok && logger != nil {
		return logger
	}
	// Возвращаем глобальный логгер как запасной вариант
	return zap.L()
}