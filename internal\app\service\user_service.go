package service

import (
	"crypto/md5"
	"errors"
	"fmt"

	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"gorm.io/gorm"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token    string      `json:"token"`
	UserInfo *entity.User `json:"userInfo"`
}

// ListUsersRequest 获取用户列表请求
type ListUsersRequest struct {
	Keyword  string `form:"keyword"`                        // 关键字(用户名或昵称)
	Page     int    `form:"page,default=1" binding:"min=1"` // 页码
	PageSize int    `form:"pageSize,default=20" binding:"min=1,max=100"` // 每页数量
}

// UserOption 用户选项，用于下拉框
type UserOption struct {
	ID       int64  `json:"id"`       // 用户ID
	Nickname string `json:"nickname"` // 用户昵称
	Username string `json:"username"` // 用户名
}

// ListUsersResponse 获取用户列表响应
type ListUsersResponse struct {
	Total int64        `json:"total"` // 总数
	List  []UserOption `json:"list"`  // 用户列表
}

// UserService 用户服务接口
type UserService interface {
	// Login 用户登录 (注意：ginCtx 通常用于处理器层与服务层的边界，服务内部逻辑应优先使用 ctx context.Context)
	// 为了简单起见，我们这里先用 gin.Context 获取 logger，但理想情况下服务核心逻辑不应强依赖 gin.Context
	Login(ginCtx *gin.Context, req *LoginRequest) (*LoginResponse, int, error)
	
	// ListUsers 获取用户列表，用于前端下拉框
	ListUsers(ginCtx *gin.Context, req *ListUsersRequest) (*ListUsersResponse, int, error)
}

// UserServiceImpl 用户服务实现
type UserServiceImpl struct {
	userRepo repository.UserRepository
}

// NewUserService 创建用户服务
func NewUserService(userRepo repository.UserRepository) UserService {
	return &UserServiceImpl{
		userRepo: userRepo,
	}
}

// Login 用户登录
func (s *UserServiceImpl) Login(ginCtx *gin.Context, req *LoginRequest) (*LoginResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Attempting login", zap.String("username", req.Username))

	// 根据用户名查找用户
	user, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Debug("User not found by username, trying phone", zap.String("usernameAttempt", req.Username))
			// 用户不存在，尝试用手机号登录
			user, err = s.userRepo.FindByPhone(ctx, req.Username)
			if err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					logger.Warn("User not found by phone either", zap.String("phoneAttempt", req.Username))
					return nil, valueobject.ERROR_USER_NOT_FOUND, errors.New("用户不存在")
				}
				logger.Error("Database error while finding user by phone", zap.Error(err))
				return nil, valueobject.ERROR_UNKNOWN, err
			}
		} else {
			logger.Error("Database error while finding user by username", zap.Error(err))
			return nil, valueobject.ERROR_UNKNOWN, err
		}
	}

	// 检查账户状态
	if user.Status != 1 {
		logger.Warn("User account disabled or locked", zap.Int64("userId", user.ID), zap.Int("status", user.Status))
		return nil, valueobject.ERROR_USER_ACCOUNT_DISABLED, errors.New("账户已被禁用")
	}

	// 验证密码
	if !verifyPassword(req.Password, user.Password) {
		logger.Warn("Incorrect password attempt", zap.Int64("userId", user.ID))
		return nil, valueobject.ERROR_USER_PASSWORD_WRONG, errors.New("用户名或密码错误")
	}

	// 生成Token
	token, err := util.GenerateToken(user.ID, user.Username)
	if err != nil {
		logger.Error("Failed to generate token", zap.Error(err), zap.Int64("userId", user.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 更新登录信息
	clientIP := ginCtx.ClientIP() // 从 Gin 上下文获取IP
	if err := s.userRepo.UpdateLoginInfo(ctx, user.ID, clientIP); err != nil {
		// 这里只记录错误，不影响登录流程
		logger.Error("Failed to update login info", zap.Error(err), zap.Int64("userId", user.ID))
	}

	logger.Info("User login successful", zap.Int64("userId", user.ID), zap.String("username", user.Username))
	// 返回登录响应
	return &LoginResponse{
		Token:    token,
		UserInfo: user,
	}, valueobject.SUCCESS, nil
}

// verifyPassword 验证密码
func verifyPassword(inputPwd, dbPwd string) bool {
	// 对输入密码进行MD5加密
	md5Pwd := fmt.Sprintf("%x", md5.Sum([]byte(inputPwd)))
	return md5Pwd == dbPwd
}

// ListUsers 获取用户列表
func (s *UserServiceImpl) ListUsers(ginCtx *gin.Context, req *ListUsersRequest) (*ListUsersResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()
	
	logger.Debug("Getting user list", zap.String("keyword", req.Keyword), zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))
	
	// 查询用户列表
	users, total, err := s.userRepo.ListUsers(ctx, req.Keyword, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to get user list", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}
	
	// 转换为前端需要的格式
	userOptions := make([]UserOption, 0, len(users))
	for _, user := range users {
		userOptions = append(userOptions, UserOption{
			ID:       user.ID,
			Nickname: user.Nickname,
			Username: user.Username,
		})
	}
	
	logger.Debug("Successfully got user list", zap.Int64("total", total), zap.Int("listSize", len(userOptions)))
	return &ListUsersResponse{
		Total: total,
		List:  userOptions,
	}, valueobject.SUCCESS, nil
} 