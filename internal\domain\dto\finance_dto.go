package dto

import (
	"time"
)

// ShippingBillExportRequest 运费账单导出请求参数
type ShippingBillExportRequest struct {
	UserID         int64     `json:"userId" binding:"required" example:"123"` // 用户ID
	StartTime      time.Time `json:"startTime" binding:"required"`            // 开始时间
	EndTime        time.Time `json:"endTime" binding:"required"`              // 结束时间
	TimeFilterType int       `json:"timeFilterType" example:"1"`              // 时间筛选类型: 1-按发货时间(默认) 2-按预报时间
	NormalTemplateID   *int64 `json:"normalTemplateId"`   // 普通模板ID(templateType=1)，不传则使用用户默认配置
	ElectronicTemplateID *int64 `json:"electronicTemplateId"` // 带电模板ID(templateType=2)，不传则使用用户默认配置
	MailboxTemplateID *int64 `json:"mailboxTemplateId"` // 投函模板ID(templateType=3)，不传则使用用户默认配置
}

// BillingRecordExportRequest 根据账单记录ID导出Excel请求参数
type BillingRecordExportRequest struct {
	BillingRecordID int64 `json:"billingRecordId" binding:"required" example:"123"` // 账单记录ID
}

// BatchBillingRecordExportRequest 批量导出账单记录Excel请求参数
type BatchBillingRecordExportRequest struct {
	BillingRecordIDs []int64 `json:"billingRecordIds" binding:"required,min=1,max=100" example:"[123,124,125]"` // 账单记录ID列表（最多100个）
}

// ShippingBillItem 运费账单项
type ShippingBillItem struct {
	ManifestID         int64     `json:"manifestId"`         // 运单ID
	ExpressNumber      string    `json:"expressNumber"`      // 快递单号
	OrderNo               string    `json:"orderNo"`            // 系统单号
	TransferredTrackingNumber string `json:"transferredTrackingNumber"` // 转单号
	OrderNumber        string    `json:"orderNumber"`        // 商家订单号
	ShippedTime        time.Time `json:"shippedTime"`        // 发货时间
	CreateTime         time.Time `json:"createTime"`         // 预报时间
	ReceiverName       string    `json:"receiverName"`       // 收件人姓名
	ItemNames          string    `json:"itemNames"`          // 物品名称（多个物品用逗号分隔）
	CargoType          int       `json:"cargoType"`          // 货物类型(1-普通 2-带电 3-投函)
	Weight             float64   `json:"weight"`             // 重量(kg)
	Length             float64   `json:"length"`             // 长(cm)
	Width              float64   `json:"width"`              // 宽(cm)
	Height             float64   `json:"height"`             // 高(cm)
	ThreeSidesSum      float64   `json:"threeSidesSum"`      // 三边和(cm)
	VolumeWeight       float64   `json:"volumeWeight"`       // 体积重(kg)
	ChargeableWeight   float64   `json:"chargeableWeight"`   // 计费重量(kg)
	FirstWeightPrice   float64   `json:"firstWeightPrice"`   // 首重价格
	ContinuedPrice     float64   `json:"continuedPrice"`     // 续重价格
	OverLengthSurcharge float64  `json:"overLengthSurcharge"`// 超长费
	RemoteAreaSurcharge float64  `json:"remoteAreaSurcharge"`// 偏远费
	OtherCost          float64   `json:"otherCost"`          // 其他费用
	OtherCostName      string    `json:"otherCostName"`      // 其他费用名称
	TotalPrice         float64   `json:"totalPrice"`         // 总价
	ShippingFeeType    int       `json:"shippingFeeType"`    // 运费模板类型(1-普通 2-带电 3-投函)
}

// BillingAdjustmentItem 账单调整项（用于Excel导出）
type BillingAdjustmentItem struct {
	AdjustmentType                    string                 `json:"adjustmentType"`                    // 调整类型
	AdjustmentTypeName                string                 `json:"adjustmentTypeName"`                // 调整类型名称
	Description                       string                 `json:"description"`                       // 调整描述/原因
	Amount                            float64                `json:"amount"`                            // 调整金额
	Currency                          string                 `json:"currency"`                          // 货币单位
	EffectiveDate                     string                 `json:"effectiveDate"`                       // 费用实际发生/确认日期
	ManifestExpressNumber             string                 `json:"manifestExpressNumber"`                 // 关联运单快递单号
	ManifestOrderNo                   string                 `json:"manifestOrderNo"`                         // 关联运单系统单号
	ManifestTransferredTrackingNumber string                 `json:"manifestTransferredTrackingNumber"`         // 关联运单转单号
	ManifestCustomerOrderNumber       string                 `json:"manifestCustomerOrderNumber"`                 // 关联运单商家订单号
	ManifestCreateTime                string                 `json:"manifestCreateTime"`                            // 关联运单创建时间
	ManifestShipmentTime              string                 `json:"manifestShipmentTime"`                            // 关联运单发货时间
	ManifestReceiverName              string                 `json:"manifestReceiverName"`                            // 关联运单收件人
	ManifestItemDescription           string                 `json:"manifestItemDescription"`                             // 关联运单物品描述
	ManifestCargoType                 string                 `json:"manifestCargoType"`                                     // 关联运单货物类型
	ManifestWeight                    float64                `json:"manifestWeight"`                                        // 关联运单重量
	ManifestLength                    float64                `json:"manifestLength"`                                        // 关联运单长度
	ManifestWidth                     float64                `json:"manifestWidth"`                                         // 关联运单宽度
	ManifestHeight                    float64                `json:"manifestHeight"`                                        // 关联运单高度
	ManifestSumOfSides                float64                `json:"manifestSumOfSides"`                                        // 关联运单三边和
	ManifestDimensionalWeight         float64                `json:"manifestDimensionalWeight"`                                        // 关联运单体积重
	ManifestChargeableWeight          float64                `json:"manifestChargeableWeight"`                                        // 关联运单计费重量
	AdditionalDetails                 map[string]interface{} `json:"additionalDetails"`                                         // 附加详情
	OriginalCreateTime                string                 `json:"originalCreateTime"`                                        // 原始记录创建时间
}

// ShippingFeeTemplateInfo 运费模板信息
type ShippingFeeTemplateInfo struct {
	ID                      int64   `json:"id"`                    // 模板ID
	Name                    string  `json:"name"`                  // 模板名称
	FirstWeightPrice        float64 `json:"firstWeightPrice"`      // 首重价格
	FirstWeightRange        float64 `json:"firstWeightRange"`      // 首重范围
	ContinuedWeightPrice    float64 `json:"continuedWeightPrice"`  // 续重价格
	ContinuedWeightInterval float64 `json:"continuedWeightInterval"` // 续重区间大小
	BulkCoefficient         int     `json:"bulkCoefficient"`       // 轻抛系数
	ThreeSidesStart         float64 `json:"threeSidesStart"`       // 三边和超过多少开始计算体积重量
	Type                    int     `json:"type"`                  // 模板类型
} 