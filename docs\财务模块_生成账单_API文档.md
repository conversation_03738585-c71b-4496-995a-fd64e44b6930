# 生成账单 API 文档

## 接口概述

该接口用于根据时间范围（固定使用发货时间）、用户 ID 和运费模板信息生成账单记录及明细。系统会查询指定时间段内符合条件的运单，并根据提供的运费模板计算费用，生成完整的账单。

## 接口信息

- **请求方法**: POST
- **请求路径**: `/api/v1/finance/billing/generate`
- **Content-Type**: `application/json`
- **需要认证**: 是

## 请求参数

### 请求体 (Request Body)

| 参数名          | 类型                | 必填 | 说明                                |
| --------------- | ------------------- | ---- | ----------------------------------- |
| startTime       | string              | 是   | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime         | string              | 是   | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| userId          | int64               | 是   | 用户 ID                             |
| generalTemplate | ShippingFeeTemplate | 否   | 普货模板信息                        |
| batteryTemplate | ShippingFeeTemplate | 否   | 带电货物模板信息                    |
| postBoxTemplate | ShippingFeeTemplate | 否   | 投函货物模板信息                    |
| dueDate         | string              | 否   | 付款截止日期，格式：yyyy-MM-dd      |
| notes           | string              | 否   | 账单备注                            |
| currency        | string              | 是   | 货币单位，默认 CNY                  |

### ShippingFeeTemplate 运费模板结构

| 参数名                  | 类型    | 说明                                   |
| ----------------------- | ------- | -------------------------------------- |
| id                      | int64   | 模板 ID                                |
| name                    | string  | 模板名称                               |
| firstWeightPrice        | float64 | 首重价格（元）                         |
| firstWeightRange        | float64 | 首重范围（公斤）                       |
| continuedWeightPrice    | float64 | 续重价格（元）                         |
| continuedWeightInterval | float64 | 续重区间大小（公斤）                   |
| bulkCoefficient         | int     | 轻抛系数（用于计算体积重量）           |
| threeSidesStart         | float64 | 三边和超过多少开始计算体积重量（厘米） |
| type                    | int     | 模板类型：1-普通；2-带电；3-投函       |

## 业务逻辑说明

### 生成流程

1. **参数验证**:

   - 验证时间格式和时间范围的合理性
   - 验证用户 ID 的有效性
   - 验证至少提供一个运费模板

2. **运单查询**:

   - 固定使用发货时间(`shipment_time`)进行筛选
   - 查询条件：`status >= 3` 且 `is_delete = 0`
   - 时间范围：`startTime` 到 `endTime`

3. **账单生成**:

   - 生成唯一的账单编号
   - 创建账单主记录
   - 根据运费模板计算每个运单的费用
   - 生成账单明细项

4. **费用计算**:

   - 根据货物类型选择对应的运费模板
   - 计算基础运费（首重 + 续重）
   - 加上附加费用（超长费、偏远费、其他费用）

5. **财务调整快照**:
   - 查询相关的财务调整记录
   - 为账单明细生成财务调整快照

### 模板选择逻辑

| 货物类型 | 对应模板        | 说明         |
| -------- | --------------- | ------------ |
| 1        | generalTemplate | 普通货物模板 |
| 2        | batteryTemplate | 带电货物模板 |
| 3        | postBoxTemplate | 投函货物模板 |

## 请求示例

### 基本生成账单请求

```json
{
  "startTime": "2024-11-01 00:00:00",
  "endTime": "2024-11-30 23:59:59",
  "userId": 1001,
  "generalTemplate": {
    "id": 1,
    "name": "标准普通货物模板",
    "firstWeightPrice": 8.0,
    "firstWeightRange": 0.5,
    "continuedWeightPrice": 3.5,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 5000,
    "threeSidesStart": 60.0,
    "type": 1
  },
  "batteryTemplate": {
    "id": 2,
    "name": "带电货物专用模板",
    "firstWeightPrice": 12.0,
    "firstWeightRange": 0.5,
    "continuedWeightPrice": 5.0,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 5000,
    "threeSidesStart": 60.0,
    "type": 2
  },
  "dueDate": "2024-12-31",
  "notes": "2024年11月份运费账单",
  "currency": "CNY"
}
```

### 仅普通货物模板的请求

```json
{
  "startTime": "2024-12-01 00:00:00",
  "endTime": "2024-12-07 23:59:59",
  "userId": 1002,
  "generalTemplate": {
    "id": 1,
    "name": "标准普通货物模板",
    "firstWeightPrice": 8.0,
    "firstWeightRange": 0.5,
    "continuedWeightPrice": 3.5,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 5000,
    "threeSidesStart": 60.0,
    "type": 1
  },
  "currency": "CNY"
}
```

## 响应结果

### 成功响应

**HTTP 状态码**: 200

**响应体结构**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:35:00Z",
  "data": {
    "billingRecordId": 1001,
    "billNumber": "BILL202412010001",
    "totalAmount": 1256.8,
    "itemsCount": 45,
    "message": "成功生成账单，包含 45 条明细记录"
  }
}
```

### 错误响应

**HTTP 状态码**: 400/500

**响应体结构**:

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-12-01T10:30:00Z",
  "data": null
}
```

## 使用示例

### 示例 1: 生成 11 月份账单

```bash
curl -X POST "http://localhost:8080/api/v1/finance/billing/generate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "startTime": "2024-11-01 00:00:00",
    "endTime": "2024-11-30 23:59:59",
    "userId": 1001,
    "generalTemplate": {
      "id": 1,
      "name": "标准普通货物模板",
      "firstWeightPrice": 8.00,
      "firstWeightRange": 0.5,
      "continuedWeightPrice": 3.50,
      "continuedWeightInterval": 0.5,
      "bulkCoefficient": 5000,
      "threeSidesStart": 60.0,
      "type": 1
    },
    "currency": "CNY"
  }'
```

### 示例 2: 生成指定周期账单并设置付款期限

```bash
curl -X POST "http://localhost:8080/api/v1/finance/billing/generate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "startTime": "2024-12-01 08:00:00",
    "endTime": "2024-12-07 18:00:00",
    "userId": 1002,
    "generalTemplate": {
      "id": 1,
      "name": "标准普通货物模板",
      "firstWeightPrice": 8.00,
      "firstWeightRange": 0.5,
      "continuedWeightPrice": 3.50,
      "continuedWeightInterval": 0.5,
      "bulkCoefficient": 5000,
      "threeSidesStart": 60.0,
      "type": 1
    },
    "batteryTemplate": {
      "id": 2,
      "name": "带电货物专用模板",
      "firstWeightPrice": 12.00,
      "firstWeightRange": 0.5,
      "continuedWeightPrice": 5.00,
      "continuedWeightInterval": 0.5,
      "bulkCoefficient": 5000,
      "threeSidesStart": 60.0,
      "type": 2
    },
    "dueDate": "2024-12-31",
    "notes": "2024年12月第一周运费账单",
    "currency": "CNY"
  }'
```

## 响应字段说明

### GenerateBillingResponse 字段说明

| 字段名          | 类型    | 说明             |
| --------------- | ------- | ---------------- |
| billingRecordId | int64   | 生成的账单 ID    |
| billNumber      | string  | 账单编号         |
| totalAmount     | float64 | 账单总金额（元） |
| itemsCount      | int     | 明细项数量       |
| message         | string  | 响应消息         |

## 业务场景

### 适用场景

1. **月度结算**: 财务人员按月生成用户的运费账单
2. **定期账单**: 根据合同约定的账期生成账单
3. **临时结算**: 需要提前结算某个时间段的费用
4. **分类计费**: 针对不同货物类型使用不同的运费模板

### 使用建议

1. **模板准备**: 在生成账单前，建议先调用查询用户运费模板接口获取用户配置
2. **时间选择**: 建议使用完整的时间段（如月初到月末）进行账单生成
3. **模板完整性**: 确保提供用户可能涉及的所有货物类型的运费模板
4. **幂等性**: 相同条件下重复调用会产生新的账单，请谨慎操作

## 费用计算说明

### 基础运费计算

1. **首重费用**: 根据模板的首重价格和首重范围计算
2. **续重费用**: 超出首重部分按续重区间和续重价格计算
3. **体积重量**: 当三边和超过阈值时，使用轻抛系数计算体积重
4. **计费重量**: 实际重量与体积重量的较大值

### 附加费用

- **超长费**: 运单中的超长附加费
- **偏远费**: 偏远地区附加费
- **其他费用**: 运单中的其他费用

### 计算公式

```
基础运费 = 首重费用 + 续重费用
续重费用 = ceiling((计费重量 - 首重范围) / 续重区间) × 续重价格
总费用 = 基础运费 + 超长费 + 偏远费 + 其他费用
```

## 错误码说明

| 错误码 | 说明                             |
| ------ | -------------------------------- |
| 100000 | 操作成功                         |
| 100002 | 无效参数（时间格式、用户 ID 等） |
| 100003 | 缺少必要参数                     |
| 100004 | 未授权/未登录                    |
| 100006 | 请求的资源未找到                 |
| 100001 | 未知错误/系统繁忙                |

## 常见错误及解决方案

### 时间相关错误

```json
{
  "errorCode": 100002,
  "errorMessage": "结束时间必须晚于开始时间"
}
```

**解决方案**: 检查并修正时间范围

### 模板缺失错误

```json
{
  "errorCode": 100002,
  "errorMessage": "指定条件下没有找到符合的运单记录"
}
```

**解决方案**: 确认时间范围内有符合条件的运单

### 用户不存在错误

```json
{
  "errorCode": 100006,
  "errorMessage": "用户不存在"
}
```

**解决方案**: 确认用户 ID 的正确性

## 注意事项

1. **时间格式**: 严格使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **时间维度**: 系统固定使用发货时间进行筛选，无法更改
3. **模板完整性**: 建议提供所有可能用到的货物类型模板
4. **重复生成**: 接口不具备幂等性，相同参数会生成新账单
5. **数据量**: 时间范围过大可能导致处理时间较长
6. **权限验证**: 确保当前用户有生成指定用户账单的权限

## 数据库影响

### 创建的记录

1. **账单主记录**: `tb_billing_record` 表
2. **账单明细**: `tb_billing_record_item` 表
3. **财务调整快照**: `tb_billing_financial_adjustment_snapshot` 表

### 查询的数据

1. **运单数据**: `tb_manifest` 表（status >= 3, is_delete = 0）
2. **财务调整**: `manifest_financial_adjustments` 表
3. **用户信息**: `tb_user` 表

## 版本历史

| 版本 | 日期       | 变更说明                             |
| ---- | ---------- | ------------------------------------ |
| v1.0 | 2024-12-01 | 初始版本，支持基于发货时间的账单生成 |
| v1.1 | 2024-12-01 | 固定使用发货时间，删除 timeType 参数 |

## 相关接口

- [查询可生成账单用户列表接口](/docs/财务模块_查询可生成账单用户列表_API文档.md)
- [查询用户运费模板接口](/docs/财务模块_查询用户运费模板_API文档.md)
- [分页查询账单记录接口](/docs/财务模块_分页查询账单记录_API文档.md)
- [导出运费账单报表接口](/docs/财务模块_导出运费账单报表_API文档.md)
