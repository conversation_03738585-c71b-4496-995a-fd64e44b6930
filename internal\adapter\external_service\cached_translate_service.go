package external_service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"zebra-hub-system/internal/domain/service"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	// 翻译缓存的默认过期时间（30天）
	defaultTranslateCacheTTL = 30 * 24 * time.Hour
	
	// 翻译缓存的Redis键前缀
	translateCacheKeyPrefix = "translate:"
)

// CachedTranslateService 带缓存的翻译服务
type CachedTranslateService struct {
	delegate service.TranslateService // 实际的翻译服务实现
	redis    *redis.Client           // Redis客户端
	logger   *zap.Logger             // 日志
	ttl      time.Duration           // 缓存过期时间
}

// NewCachedTranslateService 创建带缓存的翻译服务
func NewCachedTranslateService(delegate service.TranslateService, redisClient *redis.Client, logger *zap.Logger) service.TranslateService {
	return &CachedTranslateService{
		delegate: delegate,
		redis:    redisClient,
		logger:   logger,
		ttl:      defaultTranslateCacheTTL,
	}
}

// 生成缓存键
func (s *CachedTranslateService) generateCacheKey(text, sourceLanguage, targetLanguage string) string {
	// 将参数规范化并拼接成缓存键
	normText := strings.TrimSpace(text)
	if sourceLanguage == "" {
		sourceLanguage = "auto" // 自动检测语言的情况
	}
	return fmt.Sprintf("%s%s:%s:%s", translateCacheKeyPrefix, sourceLanguage, targetLanguage, normText)
}

// TranslateText 将文本从源语言翻译到目标语言（使用缓存）
func (s *CachedTranslateService) TranslateText(ctx context.Context, text string, sourceLanguage, targetLanguage string) (string, error) {
	if s.delegate == nil {
		return "", fmt.Errorf("没有配置翻译服务")
	}
	
	if text == "" {
		return "", nil
	}
	
	// 检查缓存
	cacheKey := s.generateCacheKey(text, sourceLanguage, targetLanguage)
	
	// 尝试从Redis获取翻译结果
	if s.redis != nil {
		cachedResult, err := s.redis.Get(ctx, cacheKey).Result()
		if err == nil && cachedResult != "" {
			s.logger.Debug("翻译缓存命中", 
				zap.String("text", text), 
				zap.String("source", sourceLanguage), 
				zap.String("target", targetLanguage))
			return cachedResult, nil
		}
		
		if err != nil && err != redis.Nil {
			// 只记录日志，不中断流程
			s.logger.Warn("获取翻译缓存失败", zap.Error(err), zap.String("key", cacheKey))
		}
	}
	
	// 缓存未命中，调用实际翻译服务
	translatedText, err := s.delegate.TranslateText(ctx, text, sourceLanguage, targetLanguage)
	if err != nil {
		return "", err
	}
	
	// 将结果存入缓存
	if s.redis != nil && translatedText != "" {
		err := s.redis.Set(ctx, cacheKey, translatedText, s.ttl).Err()
		if err != nil {
			// 只记录日志，不中断流程
			s.logger.Warn("保存翻译缓存失败", zap.Error(err), zap.String("key", cacheKey))
		} else {
			s.logger.Debug("翻译结果已缓存", 
				zap.String("text", text), 
				zap.String("translated", translatedText))
		}
	}
	
	return translatedText, nil
}

// TranslateTexts 批量翻译文本（使用缓存）
func (s *CachedTranslateService) TranslateTexts(ctx context.Context, texts []string, sourceLanguage, targetLanguage string) ([]string, error) {
	if s.delegate == nil {
		return nil, fmt.Errorf("没有配置翻译服务")
	}
	
	if len(texts) == 0 {
		return []string{}, nil
	}
	
	results := make([]string, len(texts))
	pendingTexts := make([]string, 0, len(texts))
	pendingIndices := make([]int, 0, len(texts))
	
	// 1. 首先检查缓存中是否有已有的翻译
	if s.redis != nil {
		pipeline := s.redis.Pipeline()
		commands := make([]*redis.StringCmd, len(texts))
		
		// 创建批量查询命令
		for i, text := range texts {
			if text == "" {
				continue // 忽略空文本
			}
			cacheKey := s.generateCacheKey(text, sourceLanguage, targetLanguage)
			commands[i] = pipeline.Get(ctx, cacheKey)
		}
		
		// 执行管道命令
		_, err := pipeline.Exec(ctx)
		if err != nil && err != redis.Nil {
			s.logger.Warn("批量获取翻译缓存失败", zap.Error(err))
			// 这里不中断，继续处理
		}
		
		// 处理结果
		for i, text := range texts {
			if text == "" {
				results[i] = "" // 空文本直接返回空
				continue
			}
			
			// 如果有缓存命中
			if commands[i] != nil {
				cachedResult, err := commands[i].Result()
				if err == nil && cachedResult != "" {
					results[i] = cachedResult
					continue
				}
			}
			
			// 缓存未命中，加入待翻译列表
			pendingTexts = append(pendingTexts, text)
			pendingIndices = append(pendingIndices, i)
		}
	} else {
		// 如果没有Redis，所有文本都需要翻译
		pendingTexts = make([]string, 0, len(texts))
		pendingIndices = make([]int, 0, len(texts))
		
		for i, text := range texts {
			if text == "" {
				results[i] = "" // 空文本直接返回空
				continue
			}
			pendingTexts = append(pendingTexts, text)
			pendingIndices = append(pendingIndices, i)
		}
	}
	
	// 2. 如果还有未翻译的文本，调用翻译API
	if len(pendingTexts) > 0 {
		s.logger.Info("发起批量翻译请求", 
			zap.Int("total", len(texts)), 
			zap.Int("cached", len(texts)-len(pendingTexts)),
			zap.Int("pending", len(pendingTexts)))
			
		translatedTexts, err := s.delegate.TranslateTexts(ctx, pendingTexts, sourceLanguage, targetLanguage)
		if err != nil {
			return nil, err
		}
		
		// 填充翻译结果和更新缓存
		if s.redis != nil {
			pipeline := s.redis.Pipeline()
			
			for i, translatedText := range translatedTexts {
				if i >= len(pendingTexts) || i >= len(pendingIndices) {
					break
				}
				
				// 更新结果数组
				originalIndex := pendingIndices[i]
				if originalIndex < len(results) {
					results[originalIndex] = translatedText
				}
				
				// 将结果存入缓存
				if translatedText != "" {
					originalText := pendingTexts[i]
					cacheKey := s.generateCacheKey(originalText, sourceLanguage, targetLanguage)
					pipeline.Set(ctx, cacheKey, translatedText, s.ttl)
				}
			}
			
			// 执行管道命令
			_, err := pipeline.Exec(ctx)
			if err != nil {
				s.logger.Warn("批量保存翻译缓存失败", zap.Error(err))
				// 不中断流程
			}
		} else {
			// 不使用缓存，直接更新结果
			for i, translatedText := range translatedTexts {
				if i >= len(pendingIndices) {
					break
				}
				originalIndex := pendingIndices[i]
				if originalIndex < len(results) {
					results[originalIndex] = translatedText
				}
			}
		}
	} else {
		s.logger.Info("所有文本已从缓存获取，无需调用翻译API", zap.Int("total", len(texts)))
	}
	
	return results, nil
} 