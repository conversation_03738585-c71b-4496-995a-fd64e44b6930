package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"zebra-hub-system/internal/adapter/message_queue"
	"zebra-hub-system/internal/adapter/persistence"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/config"
	"zebra-hub-system/internal/util"

	"go.uber.org/zap"
)

func main() {
	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 加载配置
	cfg, err := config.LoadConfig("./configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化Logger
	util.InitLogger(cfg.Server.Mode)
	defer util.SyncLogger()

	util.Logger.Info("Starting billing consumer service...")

	// 初始化数据库
	db, err := config.InitDB(&cfg.Database)
	if err != nil {
		util.Logger.Fatal("Failed to connect database", zap.Error(err))
	}

	// 初始化RabbitMQ连接
	rabbitMQConn, err := config.InitRabbitMQ(&cfg.RabbitMQ, util.Logger)
	if err != nil {
		util.Logger.Fatal("Failed to initialize RabbitMQ connection", zap.Error(err))
	}
	defer func() {
		if err := rabbitMQConn.Close(); err != nil {
			util.Logger.Warn("Error closing RabbitMQ connection", zap.Error(err))
		}
	}()

	// 初始化仓储
	userRepo := persistence.NewUserRepository(db)
	billingRepo := persistence.NewBillingRepository(db)
	manifestFinancialAdjustmentRepo := persistence.NewManifestFinancialAdjustmentRepository(db)
	shippingFeeTemplateRepo := persistence.NewShippingFeeTemplateRepository(db)
	billingGenerationTaskRepo := persistence.NewBillingGenerationTaskRepository(db)
	billingCycleRepo := persistence.NewBillingCycleRepository(db)

	// 初始化任务处理器服务
	taskProcessor := service.NewBillingTaskProcessor(
		billingRepo,
		manifestFinancialAdjustmentRepo,
		userRepo,
		shippingFeeTemplateRepo,
		billingGenerationTaskRepo,
		billingCycleRepo,
		util.Logger,
	)

	// 初始化账单消费者
	consumer := message_queue.NewBillingConsumer(
		rabbitMQConn,
		taskProcessor,
		util.Logger,
	)

	// 设置消费者配置
	consumer.SetWorkers(2)         // 设置2个工作者
	consumer.SetMaxRetries(3)      // 最大重试3次
	consumer.SetPrefetchCount(1)   // 每次预获取1条消息

	util.Logger.Info("All components initialized successfully")

	// 启动消费者
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()
		if err := consumer.Start(ctx); err != nil {
			util.Logger.Error("Consumer failed", zap.Error(err))
			cancel() // 取消上下文，触发优雅关闭
		}
	}()

	util.Logger.Info("Billing consumer service started successfully")

	// 等待中断信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case sig := <-sigChan:
		util.Logger.Info("Received shutdown signal", zap.String("signal", sig.String()))
	case <-ctx.Done():
		util.Logger.Info("Context cancelled, shutting down")
	}

	util.Logger.Info("Shutting down gracefully...")

	// 给消费者一些时间完成当前处理的任务
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 取消主上下文，通知消费者停止
	cancel()

	// 等待消费者完成，或超时
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		util.Logger.Info("Consumer stopped gracefully")
	case <-shutdownCtx.Done():
		util.Logger.Warn("Consumer shutdown timeout, forcing exit")
	}

	util.Logger.Info("Billing consumer service stopped")
} 