package entity

import "time"

// MasterBill 主提单实体
type MasterBill struct {
	ID              int64      `json:"id"`              // 主提单ID
	MasterBillNumber string     `json:"masterBillNumber"` // 提单号/航班号
	DepartureDate   *time.Time `json:"departureDate"`   // 起飞日期
	ArrivalDate     *time.Time `json:"arrivalDate"`     // 到达日期
	Origin          string     `json:"origin"`          // 始发地
	Destination     string     `json:"destination"`     // 目的地
	CarrierCode     string     `json:"carrierCode"`     // 承运商代码
	Status          int        `json:"status"`          // 提单状态
	TotalWeight     float64    `json:"totalWeight"`     // 总重量
	TotalVolume     float64    `json:"totalVolume"`     // 总体积
	WaybillCount    int        `json:"waybillCount"`    // 运单数量
	Remark          string     `json:"remark"`          // 备注
	CreateTime      time.Time  `json:"createTime"`      // 创建时间
	UpdateTime      time.Time  `json:"updateTime"`      // 更新时间
	CreatorID       int64      `json:"creatorId"`       // 创建者ID
	IsDeleted       int        `json:"isDeleted"`       // 是否删除
} 