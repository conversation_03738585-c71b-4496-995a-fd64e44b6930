package external_service

import (
	"context"
	"fmt"

	"zebra-hub-system/internal/domain/service"

	translate "cloud.google.com/go/translate/apiv3"
	"cloud.google.com/go/translate/apiv3/translatepb"
	"google.golang.org/api/option"
)

// GoogleTranslateService 谷歌翻译服务实现
type GoogleTranslateService struct {
	client  *translate.TranslationClient
	project string // Google Cloud项目ID
}

// NewGoogleTranslateService 创建谷歌翻译服务
// apiKey: Google Cloud API密钥（可选，如果提供credentialFile则忽略）
// project: Google Cloud项目ID
// credentialFile: JSON密钥文件路径（推荐，优先使用）
func NewGoogleTranslateService(ctx context.Context, apiKey, project, credentialFile string) (service.TranslateService, error) {
	var clientOption option.ClientOption
	
	// 优先使用JSON密钥文件进行身份验证
	if credentialFile != "" {
		clientOption = option.WithCredentialsFile(credentialFile)
	} else if apiKey != "" {
		// 如果没有提供JSON密钥文件，则使用API Key
		clientOption = option.WithAPIKey(apiKey)
	} else {
		return nil, fmt.Errorf("必须提供JSON密钥文件路径或API密钥")
	}
	
	client, err := translate.NewTranslationClient(ctx, clientOption)
	if err != nil {
		return nil, fmt.Errorf("初始化Google翻译客户端失败: %v", err)
	}
	
	return &GoogleTranslateService{
		client:  client,
		project: project,
	}, nil
}

// TranslateText 将文本从源语言翻译到目标语言
func (s *GoogleTranslateService) TranslateText(ctx context.Context, text string, sourceLanguage, targetLanguage string) (string, error) {
	if text == "" {
		return "", nil
	}
	
	// 构建请求
	req := &translatepb.TranslateTextRequest{
		Parent:             fmt.Sprintf("projects/%s/locations/global", s.project),
		MimeType:           "text/plain",
		Contents:           []string{text},
		TargetLanguageCode: targetLanguage,
	}
	
	// 如果指定了源语言，则设置
	if sourceLanguage != "" {
		req.SourceLanguageCode = sourceLanguage
	}
	
	// 发送请求
	resp, err := s.client.TranslateText(ctx, req)
	if err != nil {
		return "", fmt.Errorf("翻译文本失败: %v", err)
	}
	
	// 检查响应
	if len(resp.GetTranslations()) == 0 {
		return "", fmt.Errorf("翻译结果为空")
	}
	
	return resp.GetTranslations()[0].GetTranslatedText(), nil
}

// TranslateTexts 批量翻译文本
func (s *GoogleTranslateService) TranslateTexts(ctx context.Context, texts []string, sourceLanguage, targetLanguage string) ([]string, error) {
	if len(texts) == 0 {
		return []string{}, nil
	}
	
	// 过滤空文本
	var validTexts []string
	for _, text := range texts {
		if text != "" {
			validTexts = append(validTexts, text)
		}
	}
	
	if len(validTexts) == 0 {
		return make([]string, len(texts)), nil
	}
	
	// 构建请求
	req := &translatepb.TranslateTextRequest{
		Parent:             fmt.Sprintf("projects/%s/locations/global", s.project),
		MimeType:           "text/plain",
		Contents:           validTexts,
		TargetLanguageCode: targetLanguage,
	}
	
	// 如果指定了源语言，则设置
	if sourceLanguage != "" {
		req.SourceLanguageCode = sourceLanguage
	}
	
	// 发送请求
	resp, err := s.client.TranslateText(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("批量翻译文本失败: %v", err)
	}
	
	// 检查响应
	translations := resp.GetTranslations()
	if len(translations) != len(validTexts) {
		return nil, fmt.Errorf("翻译结果数量不匹配: 预期 %d, 实际 %d", len(validTexts), len(translations))
	}
	
	// 构建结果
	result := make([]string, len(texts))
	validIndex := 0
	
	for i, text := range texts {
		if text != "" {
			result[i] = translations[validIndex].GetTranslatedText()
			validIndex++
		}
	}
	
	return result, nil
}

// Close 关闭客户端连接
func (s *GoogleTranslateService) Close() error {
	return s.client.Close()
} 