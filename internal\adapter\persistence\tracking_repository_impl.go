package persistence

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// TrackingRepositoryImpl 轨迹仓储实现
type TrackingRepositoryImpl struct {
	db *gorm.DB
}

// NewTrackingRepository 创建轨迹仓储实现
func NewTrackingRepository(db *gorm.DB) repository.TrackingRepository {
	return &TrackingRepositoryImpl{
		db: db,
	}
}

// FindByManifestID 根据运单ID查询轨迹列表
func (r *TrackingRepositoryImpl) FindByManifestID(ctx context.Context, manifestID int64) ([]*entity.Tracking, error) {
	var trackings []*entity.Tracking
	
	err := r.db.WithContext(ctx).
		Where("manifest_id = ?", manifestID).
		Order("time DESC, id DESC"). // 按时间降序排列，同时间按ID降序
		Find(&trackings).
		Error
	
	if err != nil {
		return nil, err
	}
	
	return trackings, nil
} 