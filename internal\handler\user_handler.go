package handler

import (
	"net/http"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户相关处理器
type UserHandler struct {
	userService service.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// Login 用户登录
func (h *UserHandler) Login(c *gin.Context) {
	var req service.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 调用服务层登录方法，传递 Gin 上下文 c
	resp, code, err := h.userService.Login(c, &req)
	if err != nil {
		util.ResponseError(c, code, err.Error(), http.StatusBadRequest)
		return
	}

	// 返回成功响应
	util.ResponseSuccess(c, resp)
}

// ListUsers godoc
// @Summary 获取用户列表
// @Description 获取用户列表数据，用于前端下拉框
// @Tags Users
// @Accept json
// @Produce json
// @Param keyword query string false "关键字(用户名或昵称)"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(20)
// @Success 200 {object} util.Response{data=service.ListUsersResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /users/options [get]
// @Security ApiKeyAuth
func (h *UserHandler) ListUsers(c *gin.Context) {
	var req service.ListUsersRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数错误: "+err.Error(), http.StatusBadRequest)
		return
	}
	
	// 调用服务层方法
	resp, code, err := h.userService.ListUsers(c, &req)
	if err != nil {
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}
	
	// 返回成功响应
	util.ResponseSuccess(c, resp)
} 