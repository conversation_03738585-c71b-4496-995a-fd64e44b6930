package router

import (
	"zebra-hub-system/internal/handler"
	"zebra-hub-system/internal/middleware"

	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(
	userHandler *handler.UserHandler,
	manifestHandler *handler.ManifestHandler,
	problemTicketHandler *handler.ProblemTicketHandler,
	trackingHandler *handler.TrackingHandler,
	financeHandler *handler.FinanceHandler,
	masterBillHandler *handler.MasterBillHandler,
	financialAdjustmentHandler *handler.FinancialAdjustmentHandler,
	configHandler *handler.ConfigHandler,
	billingCycleHandler *handler.BillingCycleHandler,
	fileUploadHandler *handler.FileUploadHandler,
	addressHandler *handler.AddressHandler,
	shipmentTypeHandler *handler.ShipmentTypeHandler,
	shippingFeeTemplateHandler *handler.ShippingFeeTemplateHandler,
) *gin.Engine {
	r := gin.Default()

	// 全局中间件
	r.Use(middleware.RequestID()) // 在所有路由之前应用 RequestID 中间件
	r.Use(middleware.CORS())      // 添加跨域处理中间件
	// r.Use(middleware.Logger()) // 如果有统一的请求日志中间件，也在这里添加

	// 公共路由组，不需要认证
	publicGroup := r.Group("/api/v1")
	{
		// 用户登录
		publicGroup.POST("/login", userHandler.Login)
	}

	// 需要认证的路由组
	authGroup := r.Group("/api/v1")
	authGroup.Use(middleware.JWTAuth())
	{
		// 用户相关路由
		userRoutes := authGroup.Group("/users")
		{
			userRoutes.GET("/options", userHandler.ListUsers) // 获取用户列表，用于下拉框
		}

		// 运单相关路由
		manifestRoutes := authGroup.Group("/manifests")
		{
			manifestRoutes.GET("/pending-review", manifestHandler.ListPendingReview)              // 分页查询待审核运单
			manifestRoutes.GET("/pending-count", manifestHandler.GetPendingCount)                 // 获取待审核运单数量
			manifestRoutes.POST("/update", manifestHandler.UpdateManifest)                        // 更新运单信息
			manifestRoutes.POST("/approve", manifestHandler.ApproveManifest)                      // 审核通过运单
			manifestRoutes.GET("/:id", manifestHandler.GetManifestByID)                           // 根据ID获取运单详情
			manifestRoutes.GET("/:id/trackings", trackingHandler.GetTrackingsByManifestID)        // 根据运单ID查询轨迹
			manifestRoutes.GET("/search", manifestHandler.SearchManifests)                        // 多条件查询运单
			manifestRoutes.GET("/by-express-number", manifestHandler.GetManifestsByExpressNumber) // 根据快递单号模糊查询运单
			manifestRoutes.POST("/labels", manifestHandler.GenerateLabels)
		}

		// 外部系统对接路由
		externalRoutes := authGroup.Group("/external")
		{
			externalRoutes.POST("/orders", manifestHandler.CreateOrder) // 外部系统创建订单
		}

		// 地址相关路由
		addressRoutes := authGroup.Group("/address")
		{
			addressRoutes.GET("/info", addressHandler.GetAddressInfo) // 根据邮编获取地址信息
		}

		// 问题工单相关路由
		problemTicketRoutes := authGroup.Group("/problem-tickets")
		{
			problemTicketRoutes.GET("", problemTicketHandler.GetProblemTickets)                                      // 分页查询问题工单列表
			problemTicketRoutes.GET("/:id", problemTicketHandler.GetProblemTicketByID)                               // 获取问题工单详情
			problemTicketRoutes.PUT("/:id/resolve", problemTicketHandler.ResolveProblemTicket)                       // 处理问题工单
			problemTicketRoutes.PUT("/:id/remark", problemTicketHandler.AddProblemTicketRemark)                      // 添加/更新问题工单备注
			problemTicketRoutes.POST("/resolve-all", problemTicketHandler.MarkAllProblemTicketsAsResolved)           // 批量处理所有待处理问题工单
			problemTicketRoutes.POST("/resolve-specific", problemTicketHandler.MarkSpecificProblemTicketsAsResolved) // 批量处理指定问题工单
			problemTicketRoutes.PUT("/:id/mark-pending", problemTicketHandler.MarkProblemTicketAsPending)            // 将单个工单标记为待处理
			problemTicketRoutes.POST("/mark-pending", problemTicketHandler.MarkSpecificProblemTicketsAsPending)      // 批量将工单标记为待处理
			problemTicketRoutes.GET("/users-with-problems", problemTicketHandler.GetUsersWithProblemManifests)       // 获取拥有问题运单的用户列表及统计
			problemTicketRoutes.GET("/problem-count", problemTicketHandler.GetProblemManifestCount)                  // 获取问题运单数量
		}

		// 财务模块相关路由
		financeRoutes := authGroup.Group("/finance")
		{
			financeRoutes.POST("/shipping-bill/export", financeHandler.ExportShippingBill)                                             // 导出运费账单报表
			financeRoutes.POST("/billing-record/export", financeHandler.ExportBillingRecordExcel)                                      // 根据账单记录ID导出Excel
			financeRoutes.POST("/billing/generate", financeHandler.GenerateBilling)                                                    // 生成账单
			financeRoutes.POST("/billing/async-generate", financeHandler.AsyncGenerateBilling)                                         // 异步生成账单
			financeRoutes.GET("/billing/records", financeHandler.ListBillingRecords)                                                   // 分页查询账单记录
			financeRoutes.GET("/billing/records/:billingRecordId", financeHandler.GetBillingRecordDetail)                              // 根据账单记录ID获取账单记录详情
			financeRoutes.GET("/billing/records/:billingRecordId/items", financeHandler.ListBillingRecordItems)                        // 根据账单记录ID查询账单明细列表
			financeRoutes.GET("/billing/records/:billingRecordId/adjustment-snapshots", financeHandler.ListBillingAdjustmentSnapshots) // 根据账单记录ID分页查询调整明细列表
			financeRoutes.GET("/billing/users", financeHandler.GetUsersForBilling)                                                     // 查询可生成账单的用户列表
			financeRoutes.GET("/billing/generation-tasks", financeHandler.ListBillingGenerationTasks)                                  // 查询账单生成任务列表
			financeRoutes.GET("/billing/generation-tasks/:taskId", financeHandler.GetBillingGenerationTaskDetail)                      // 根据任务ID获取任务详情
		}

		// 账期批次管理相关路由
		billingCycleRoutes := authGroup.Group("/billing/cycles")
		{
			billingCycleRoutes.POST("", billingCycleHandler.CreateBillingCycle)                   // 创建账期批次
			billingCycleRoutes.GET("", billingCycleHandler.ListBillingCycles)                     // 分页查询账期批次列表
			billingCycleRoutes.GET("/:billingCycleId", billingCycleHandler.GetBillingCycleDetail) // 获取账期批次详情
		}

		// 配置管理相关路由
		configRoutes := authGroup.Group("/config")
		{
			configRoutes.GET("/shipping-fee-templates/user/:userId", configHandler.GetUserShippingFeeTemplates) // 根据用户ID查询运费模板
		}

		// 财务调整记录相关路由
		financialAdjustmentRoutes := authGroup.Group("/financial-adjustments")
		{
			financialAdjustmentRoutes.GET("", financialAdjustmentHandler.ListFinancialAdjustments)                    // 查询财务调整记录列表
			financialAdjustmentRoutes.POST("/void", financialAdjustmentHandler.VoidFinancialAdjustment)               // 作废财务调整记录
			financialAdjustmentRoutes.POST("/compensations", financialAdjustmentHandler.CreateCompensationAdjustment) // 创建赔偿类财务调整记录
			financialAdjustmentRoutes.POST("/reassignments", financialAdjustmentHandler.CreateReassignmentAdjustment) // 创建改派类财务调整记录
			financialAdjustmentRoutes.POST("/destructions", financialAdjustmentHandler.CreateDestructionAdjustment)   // 创建销毁类财务调整记录
			financialAdjustmentRoutes.POST("/returns", financialAdjustmentHandler.CreateReturnAdjustment)             // 创建退回类财务调整记录
		}

		// 主提单相关路由
		masterBillRoutes := authGroup.Group("/master-bills")
		{
			masterBillRoutes.GET("/options", masterBillHandler.ListMasterBills) // 获取主提单列表，用于下拉框
		}

		// 文件上传相关路由
		uploadRoutes := authGroup.Group("/upload")
		{
			uploadRoutes.POST("/image", fileUploadHandler.UploadImage)   // 上传图片
			uploadRoutes.DELETE("/image", fileUploadHandler.DeleteImage) // 删除图片
			uploadRoutes.GET("/info", fileUploadHandler.GetUploadInfo)   // 获取上传配置信息
		}

		// 货物类型相关路由
		shipmentTypeRoutes := authGroup.Group("/shipment-types")
		{
			shipmentTypeRoutes.GET("", shipmentTypeHandler.GetAllShipmentTypes)           // 获取所有货物类型
			shipmentTypeRoutes.GET("/active", shipmentTypeHandler.GetActiveShipmentTypes) // 获取所有启用的货物类型
		}

		// 运费模板相关路由
		shippingFeeTemplateRoutes := authGroup.Group("/shipping-fee-templates")
		{
			shippingFeeTemplateRoutes.GET("", shippingFeeTemplateHandler.GetTemplatesWithSearch)           // 查询运费模板
			shippingFeeTemplateRoutes.GET("/user/:userId", shippingFeeTemplateHandler.GetUserConfiguredTemplates) // 根据用户ID获取其已配置的运费模板
			shippingFeeTemplateRoutes.PUT("/user/:userId/configurations", shippingFeeTemplateHandler.UpdateUserTemplateConfigurations) // 更新用户的模板配置
			shippingFeeTemplateRoutes.GET("/:id", shippingFeeTemplateHandler.GetTemplateByID)       // 根据ID获取运费模板
			shippingFeeTemplateRoutes.POST("", shippingFeeTemplateHandler.CreateTemplate)           // 创建运费模板
			shippingFeeTemplateRoutes.PUT("", shippingFeeTemplateHandler.UpdateTemplate)            // 更新运费模板
			shippingFeeTemplateRoutes.DELETE("/:id", shippingFeeTemplateHandler.DeleteTemplate)     // 删除运费模板
		}
	}

	return r
}
