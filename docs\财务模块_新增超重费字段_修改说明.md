# 财务模块 - 新增超重费字段修改说明

## 修改概述

根据数据库表结构变更，新增了以下字段：

- `billing_record_items.overweight_surcharge` - 账单明细中的超重费字段
- `billing_financial_adjustment_snapshots.manifest_overweight_surcharge` - 财务调整快照中的运单超重费字段

**重要说明**：这两个字段都对应 `tb_manifest.other_cost`（其他费用）字段。`tb_manifest` 表本身没有 `overweight_surcharge` 字段。

## 修改内容

### 1. 实体层修改

#### 1.1 BillingRecordItem 实体

**文件**: `internal/domain/entity/billing_record.go`

新增字段：

```go
OverweightSurcharge *float64 `json:"overweightSurcharge,omitempty"` // 超重费
```

#### 1.2 BillingFinancialAdjustmentSnapshot 实体

**文件**: `internal/domain/entity/billing_record.go`

新增字段：

```go
ManifestOverweightSurcharge *float64 `json:"manifestOverweightSurcharge,omitempty"`
```

**注意**：`Manifest` 实体本身没有修改，因为 `tb_manifest` 表没有 `overweight_surcharge` 字段，只有 `other_cost` 字段。

### 2. 数据库模型层修改

#### 2.1 BillingRecordItemPO 模型

**文件**: `internal/adapter/persistence/model/billing_model.go`

新增字段：

```go
OverweightSurcharge *float64 `gorm:"column:overweight_surcharge;type:decimal(19,2)"`
```

#### 2.2 BillingFinancialAdjustmentSnapshotPO 模型

**文件**: `internal/adapter/persistence/model/billing_model.go`

新增字段：

```go
ManifestOverweightSurcharge *float64 `gorm:"column:manifest_overweight_surcharge;type:decimal(19,2)"`
```

**注意**：`ManifestPO` 模型没有修改，因为 `tb_manifest` 表本身没有 `overweight_surcharge` 字段。

### 3. 服务层修改

#### 3.1 BillingService DTO

**文件**: `internal/app/service/billing_service.go`

**BillingRecordItemDTO** 新增字段：

```go
OverweightSurcharge *float64 `json:"overweightSurcharge,omitempty"` // 超重费
```

**BillingAdjustmentSnapshotDTO** 新增字段：

```go
ManifestOverweightSurcharge *float64 `json:"manifestOverweightSurcharge,omitempty"`
```

#### 3.2 业务逻辑修改

**文件**: `internal/app/service/billing_service.go`

1. **账单明细生成逻辑**：
   - 在 `generateBillingItems` 方法中，将 `manifest.OtherCost` 映射到 `OverweightSurcharge` 字段
2. **费用计算逻辑**：
   - 在 `calculateFreightFees` 方法中，总费用计算不再重复计算 `manifest.OtherCost`
3. **财务调整快照生成**：
   - 在 `generateFinancialAdjustmentSnapshots` 方法中，将 `manifest.OtherCost` 映射到 `ManifestOverweightSurcharge` 字段
   - 原始总费用计算不再重复计算 `manifest.OtherCost`
4. **DTO 转换逻辑**：
   - 在 `ListBillingRecordItems` 和 `ListBillingAdjustmentSnapshots` 方法中添加新字段的映射

### 4. 仓储层修改

#### 4.1 BillingRepository 实现

**文件**: `internal/adapter/persistence/billing_repository_impl.go`

1. **SaveBillingRecordItems** 方法：

   - 添加 `OverweightSurcharge` 字段的保存逻辑

2. **SaveBillingFinancialAdjustmentSnapshots** 方法：

   - 添加 `ManifestOverweightSurcharge` 字段的保存逻辑

3. **FindBillingRecordItemsByBillingRecordID** 方法：

   - 添加 `OverweightSurcharge` 字段的查询和转换逻辑

4. **FindBillingFinancialAdjustmentSnapshotsByBillingRecordID** 方法：
   - 添加 `ManifestOverweightSurcharge` 字段的查询和转换逻辑

### 5. Excel 导出功能修改

#### 5.1 FinanceService 实现

**文件**: `internal/domain/service/impl/finance_service_impl.go`

1. **表头修改**：

   - 将原来的"其他费用(元)"改为"超重费(元)"

2. **数据转换逻辑**：
   - 在 `convertBillingItemsToShippingBillItems` 方法中，将账单明细的 `OverweightSurcharge` 映射到 `ShippingBillItem.OtherCost` 字段
   - 设置 `OtherCostName` 为"超重费"

## 字段映射关系

| 数据库表                               | 数据库字段                    | 实体字段                    | DTO 字段                    | Excel 列名 | 对应原始字段           |
| -------------------------------------- | ----------------------------- | --------------------------- | --------------------------- | ---------- | ---------------------- |
| billing_record_items                   | overweight_surcharge          | OverweightSurcharge         | overweightSurcharge         | 超重费(元) | tb_manifest.other_cost |
| billing_financial_adjustment_snapshots | manifest_overweight_surcharge | ManifestOverweightSurcharge | manifestOverweightSurcharge | -          | tb_manifest.other_cost |
| tb_manifest                            | other_cost                    | OtherCost                   | -                           | -          | 原始字段（无变更）     |

## 核心设计理念

1. **数据源统一**：新增的两个 `overweight_surcharge` 字段都从 `tb_manifest.other_cost` 获取数据
2. **语义明确**：在账单和财务调整中，将 `other_cost` 明确标识为"超重费"
3. **数据一致性**：确保账单明细和财务调整快照中的超重费数据与运单原始数据一致
4. **向后兼容**：不修改原有的 `tb_manifest` 表结构和 `Manifest` 实体

## 注意事项

1. **数据一致性**：新增的 `overweight_surcharge` 字段实际存储的是运单的 `other_cost` 值
2. **向后兼容**：所有新增字段都使用指针类型，支持 NULL 值，确保向后兼容
3. **Excel 导出**：保持原有 Excel 样式，只是将列名从"其他费用"改为"超重费"
4. **费用计算**：避免重复计算，确保总费用计算的准确性
5. **表结构**：`tb_manifest` 表本身没有新增字段，只是在账单相关表中新增了对应字段

## 测试建议

1. **数据库迁移测试**：确保新字段能正确创建和使用
2. **账单生成测试**：验证新字段在账单生成过程中的正确映射
3. **Excel 导出测试**：验证 Excel 文件中超重费列的正确显示
4. **API 接口测试**：验证相关 API 返回的 JSON 中包含新字段
5. **数据一致性测试**：验证 `overweight_surcharge` 与 `tb_manifest.other_cost` 的一致性

## 影响范围

- ✅ 账单明细查询接口
- ✅ 财务调整快照查询接口
- ✅ 账单记录导出 Excel 功能
- ✅ 运费账单导出 Excel 功能
- ✅ 账单生成功能
- ✅ 财务调整快照生成功能

所有相关功能都已更新以支持新的超重费字段。
