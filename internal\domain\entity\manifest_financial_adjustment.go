package entity

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// AdditionalDetails 特定调整类型的附加详情 (JSON格式)
type AdditionalDetails map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AdditionalDetails) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AdditionalDetails) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}
	b, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(b, a)
}

// ManifestFinancialAdjustment 运单财务调整记录实体
type ManifestFinancialAdjustment struct {
	ID                 int64              `gorm:"primaryKey;autoIncrement" json:"id"` // 主键ID
	ManifestID         int64              `gorm:"not null;index:idx_adj_manifest_id" json:"manifestId"` // 关联的运单ID (FK -> tb_manifest.id)
	AdjustmentType     string             `gorm:"type:varchar(50);not null;index:idx_adj_type" json:"adjustmentType"` // 调整类型
	Description        string             `gorm:"type:text" json:"description"` // 调整描述/原因
	AdditionalDetails  *AdditionalDetails `gorm:"type:json" json:"additionalDetails"` // 特定调整类型的附加详情 (JSON格式)
	Amount             float64            `gorm:"type:decimal(19,2);not null" json:"amount"` // 调整金额 (正数收入, 负数支出/赔偿)
	Currency           string             `gorm:"type:varchar(10);not null" json:"currency"` // 货币单位
	EffectiveDate      time.Time          `gorm:"type:date;not null;index:idx_adj_effective_date" json:"effectiveDate"` // 费用实际发生/确认日期
	CustomerAccountID  int64              `gorm:"not null;index:idx_adj_customer_id" json:"customerAccountId"` // 客户id
	IsVoid             bool               `gorm:"type:tinyint(1);not null;default:0;index:idx_adj_is_void" json:"isVoid"` // 是否已作废 (TRUE: 作废, FALSE: 生效)
	VoidReason         string             `gorm:"type:text" json:"voidReason"` // 作废原因 (如果 is_void 为 TRUE)
	VoidedBy           *int64             `gorm:"index" json:"voidedBy"` // 作废操作员ID (FK -> internal_users.id, 可选)
	VoidedTime         *time.Time         `gorm:"index" json:"voidedTime"` // 作废时间
	CreatorID          *int64             `gorm:"index" json:"creatorId"` // 创建者ID (FK -> internal_users.id)
	CreateTime         time.Time          `gorm:"autoCreateTime;not null;default:CURRENT_TIMESTAMP" json:"createTime"`
	UpdateTime         time.Time          `gorm:"autoUpdateTime;not null;default:CURRENT_TIMESTAMP" json:"updateTime"`
}

// TableName 指定表名
func (ManifestFinancialAdjustment) TableName() string {
	return "manifest_financial_adjustments"
}
