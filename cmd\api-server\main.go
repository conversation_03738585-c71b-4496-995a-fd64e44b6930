package main

import (
	"context"
	"fmt"
	"log"
	"zebra-hub-system/internal/adapter/external_service"
	"zebra-hub-system/internal/adapter/message_queue"
	"zebra-hub-system/internal/adapter/persistence"
	appservice "zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/config"
	domainservice "zebra-hub-system/internal/domain/service"
	"zebra-hub-system/internal/domain/service/impl"
	"zebra-hub-system/internal/handler"
	"zebra-hub-system/internal/router"
	"zebra-hub-system/internal/util"

	"go.uber.org/zap"
)

func main() {
	// 创建上下文
	ctx := context.Background()
	
	// 加载配置
	cfg, err := config.LoadConfig("./configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化 Logger
	util.InitLogger(cfg.Server.Mode)
	defer util.SyncLogger()
	
	// 初始化Redis客户端
	redisClient, err := config.InitRedisClient(&cfg.Redis, util.Logger)
	if err != nil {
		util.Logger.Warn("Failed to initialize Redis client, proceeding without caching", zap.Error(err))
		// 不因为Redis连接失败而中断程序启动
	}
	// 如果Redis客户端初始化成功，确保程序结束时关闭连接
	if redisClient != nil {
		defer func() {
			if err := redisClient.Close(); err != nil {
				util.Logger.Warn("Error closing Redis connection", zap.Error(err))
			}
		}()
	}

	// 初始化数据库
	db, err := config.InitDB(&cfg.Database)
	if err != nil {
		log.Fatalf("Failed to connect database: %v", err)
	}

	// 初始化RabbitMQ连接
	rabbitMQConn, err := config.InitRabbitMQ(&cfg.RabbitMQ, util.Logger)
	if err != nil {
		util.Logger.Warn("Failed to initialize RabbitMQ connection, proceeding without message queue", zap.Error(err))
		// 不因为RabbitMQ连接失败而中断程序启动
	}
	// 如果RabbitMQ连接初始化成功，确保程序结束时关闭连接
	if rabbitMQConn != nil {
		defer func() {
			if err := rabbitMQConn.Close(); err != nil {
				util.Logger.Warn("Error closing RabbitMQ connection", zap.Error(err))
			}
		}()
	}

	// 初始化仓储
	userRepo := persistence.NewUserRepository(db)
	manifestRepo := persistence.NewManifestRepository(db)
	problemTicketRepo := persistence.NewProblemTicketRepository(db)
	trackingRepo := persistence.NewTrackingRepository(db)
	trackingNumberRepo := persistence.NewTrackingNumberRepository(db)
	shippingFeeTemplateRepo := persistence.NewShippingFeeTemplateRepository(db)
	shipmentTypeRepo := persistence.NewShipmentTypeRepository(db)
	masterBillRepo := persistence.NewMasterBillRepository(db)
	manifestFinancialAdjustmentRepo := persistence.NewManifestFinancialAdjustmentRepository(db)
	billingRepo := persistence.NewBillingRepository(db)
	billingCycleRepo := persistence.NewBillingCycleRepository(db)
	billingGenerationTaskRepo := persistence.NewBillingGenerationTaskRepository(db)

	// 初始化领域服务
	zipCodeService := domainservice.NewJapaneseZipCodeService(util.Logger)
	
	// 初始化文件上传服务
	fileUploadService, err := impl.NewFileUploadService(
		cfg.OSS.AccessKeyID,
		cfg.OSS.AccessKeySecret,
		cfg.OSS.Endpoint,
		cfg.OSS.BucketName,
		util.Logger,
	)
	if err != nil {
		util.Logger.Fatal("Failed to initialize file upload service", zap.Error(err))
	}
	
	// 初始化Google翻译服务
	var translateService domainservice.TranslateService
	googleTranslate, err := config.InitGoogleTranslateService(ctx, &cfg.GoogleAPIs.Translate, util.Logger)
	if err != nil {
		util.Logger.Warn("Failed to initialize Google Translate service, continuing without translation", zap.Error(err))
		// 不因为翻译服务初始化失败而中断程序启动
	} else if googleTranslate != nil {
		// 如果Redis可用，使用缓存包装翻译服务
		if redisClient != nil {
			util.Logger.Info("Creating cached translation service with Redis")
			translateService = external_service.NewCachedTranslateService(googleTranslate, redisClient, util.Logger)
		} else {
			translateService = googleTranslate
		}
	}
	
	// 初始化应用服务
	pdfService := appservice.NewPDFService("assets/fonts/ipaexg.ttf")
	userService := appservice.NewUserService(userRepo)
	manifestService := appservice.NewManifestService(manifestRepo, userRepo, zipCodeService, translateService, manifestFinancialAdjustmentRepo, trackingNumberRepo, pdfService)
	problemTicketService := appservice.NewProblemTicketService(problemTicketRepo, userRepo, manifestRepo)
	trackingService := appservice.NewTrackingService(trackingRepo, manifestRepo)
	masterBillService := appservice.NewMasterBillService(masterBillRepo)
	financialAdjustmentService := appservice.NewFinancialAdjustmentService(manifestFinancialAdjustmentRepo, manifestRepo, userRepo)
	addressService := appservice.NewAddressService(zipCodeService)
	
	// 初始化消息生产者
	var billingProducer *message_queue.BillingProducer
	if rabbitMQConn != nil {
		billingProducer = message_queue.NewBillingProducer(rabbitMQConn, util.Logger)
	}
	
	billingService := appservice.NewBillingService(billingRepo, manifestFinancialAdjustmentRepo, userRepo, shippingFeeTemplateRepo, billingGenerationTaskRepo, billingCycleRepo, billingProducer)
	configService := appservice.NewConfigService(shippingFeeTemplateRepo)
	financeService := impl.NewFinanceService(manifestRepo, shippingFeeTemplateRepo, userRepo, billingService)
	billingCycleService := appservice.NewBillingCycleService(billingCycleRepo, userRepo)
	shipmentTypeService := appservice.NewShipmentTypeService(shipmentTypeRepo)
	shippingFeeTemplateService := appservice.NewShippingFeeTemplateService(shippingFeeTemplateRepo)

	// 初始化处理器
	userHandler := handler.NewUserHandler(userService)
	manifestHandler := handler.NewManifestHandler(manifestService)
	problemTicketHandler := handler.NewProblemTicketHandler(problemTicketService)
	trackingHandler := handler.NewTrackingHandler(trackingService)
	financeHandler := handler.NewFinanceHandler(financeService, billingService)
	masterBillHandler := handler.NewMasterBillHandler(masterBillService)
	financialAdjustmentHandler := handler.NewFinancialAdjustmentHandler(financialAdjustmentService)
	configHandler := handler.NewConfigHandler(configService)
	billingCycleHandler := handler.NewBillingCycleHandler(billingCycleService)
	fileUploadHandler := handler.NewFileUploadHandler(fileUploadService, util.Logger)
	addressHandler := handler.NewAddressHandler(addressService)
	shipmentTypeHandler := handler.NewShipmentTypeHandler(shipmentTypeService, util.Logger)
	shippingFeeTemplateHandler := handler.NewShippingFeeTemplateHandler(shippingFeeTemplateService, util.Logger)

	// 设置路由
	r := router.SetupRouter(userHandler, manifestHandler, problemTicketHandler, trackingHandler, financeHandler, masterBillHandler, financialAdjustmentHandler, configHandler, billingCycleHandler, fileUploadHandler, addressHandler, shipmentTypeHandler, shippingFeeTemplateHandler)

	// 启动服务
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	util.Logger.Info("Server is running", zap.String("address", addr))
	if err := r.Run(addr); err != nil {
		util.Logger.Fatal("Failed to start server", zap.Error(err))
	}
} 