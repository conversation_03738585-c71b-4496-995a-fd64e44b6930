package middleware

import (
	"zebra-hub-system/internal/util" // 导入 util 包以使用 context keys

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap" // 导入 zap
	// "go.uber.org/zap" // 如果要在这里创建带 requestId 的 logger，则需要导入
)

const (
	RequestIDHeaderKey = "X-Request-ID"
	// RequestIDContextKey 和 LoggerContextKey 现在从 util 包获取
)

// RequestID 中间件，用于生成和传递请求ID，并创建带请求ID的Logger
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 尝试从请求头获取 requestId，如果客户端传递了
		requestID := c.GetHeader(RequestIDHeaderKey)
		if requestID == "" {
			requestID = uuid.New().String()
		}

		// 设置到 Gin 上下文，供后续处理器和服务使用
		c.Set(util.RequestIDContextKey, requestID) // 使用 util.RequestIDContextKey
		c.Header(RequestIDHeaderKey, requestID)

		// 为这个请求创建一个特定的 logger，包含 requestId，并存入上下文
		// 确保 zap.L() 已经初始化 (通常在 main.go 中完成)
		// 如果 zap.L() 返回的是 nil (例如在测试中没有初始化全局 logger)，这里会 panic
		// 在实际应用中，需要确保 zap.L() 的可用性或进行更安全的检查
		reqLogger := zap.L().With(zap.String(util.RequestIDContextKey, requestID)) // 使用 util.RequestIDContextKey
		c.Set(util.LoggerContextKey, reqLogger)    // 使用 util.LoggerContextKey

		/*  // 可选：为这个请求创建一个特定的 logger，并存入上下文
		   // 需要确保 zap.L() 已经初始化
		   if zap.L() != nil { // 简单检查 logger 是否已初始化
		       reqLogger := zap.L().With(zap.String(RequestIDContextKey, requestID))
		       c.Set(LoggerContextKey, reqLogger)
		   }
		*/

		c.Next()
	}
} 