package entity

import "time"

// TrackingNumberChannel 运单号渠道实体
type TrackingNumberChannel struct {
	ID             int64     `gorm:"primaryKey;autoIncrement" json:"id"`
	CarrierID      int64     `gorm:"not null" json:"carrierId"`      // 承运商ID
	LocationID     int64     `gorm:"not null" json:"locationId"`     // 地点ID
	ShipmentTypeID int64     `gorm:"not null" json:"shipmentTypeId"` // 货物类型ID
	ChannelCode    string    `gorm:"type:varchar(64);not null;uniqueIndex" json:"channelCode"` // 渠道代码
	ChannelName    string    `gorm:"type:varchar(150);not null" json:"channelName"` // 渠道名称
	Description    string    `gorm:"type:text" json:"description"`                  // 渠道描述
	IsActive       bool      `gorm:"default:true" json:"isActive"`                  // 是否启用
	CreatorID      *int64    `json:"creatorId"`                                     // 创建者ID
	UpdaterID      *int64    `json:"updaterId"`                                     // 更新者ID
	CreateTime     time.Time `gorm:"default:CURRENT_TIMESTAMP" json:"createTime"`
	UpdateTime     time.Time `gorm:"default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"updateTime"`
}

// TableName 指定表名
func (TrackingNumberChannel) TableName() string {
	return "tracking_number_channels"
} 