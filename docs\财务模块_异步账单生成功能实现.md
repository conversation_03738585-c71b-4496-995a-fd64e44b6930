# 财务模块 - 异步账单生成功能实现

## 概述

根据用户需求，新增了异步账单生成功能，相比原有的同步生成接口，具有以下特点：

1. **异步处理**：生成账单任务提交到 RabbitMQ 队列，由后台消费者处理
2. **支持多客户**：可同时为多个客户生成账单
3. **灵活的模板配置**：支持用户特定的运费模板设置
4. **任务追踪**：提供完整的任务状态管理和进度跟踪

## 系统架构

### 消息队列架构

```
前端请求 -> API接口 -> 创建任务 -> 发送到RabbitMQ -> 后台消费者处理 -> 更新任务状态
```

### RabbitMQ 配置

```yaml
rabbitmq:
  host: *************
  port: 5673
  virtual-host: /
  username: zebra
  password: banma1346797
```

### 队列配置

- **交换机**: `billing.generation.exchange` (direct 类型)
- **队列**: `billing.generation`
- **路由键**: `billing.generation.task`

## 实现详情

### 1. 领域层扩展

#### 新增实体

**`BillingGenerationMessage`** - 消息队列消息体

```go
type BillingGenerationMessage struct {
    TaskID             string                   `json:"taskId"`             // 任务ID
    BillingCycleID     int64                    `json:"billingCycleId"`     // 账期批次ID
    CustomerIDs        []int64                  `json:"customerIds"`        // 客户ID列表
    StartTime          time.Time                `json:"startTime"`          // 开始时间
    EndTime            time.Time                `json:"endTime"`            // 结束时间
    DueDate            *time.Time               `json:"dueDate,omitempty"`  // 付款截止日期
    Currency           string                   `json:"currency"`           // 货币单位
    Notes              *string                  `json:"notes,omitempty"`    // 备注
    GeneratedByUserID  *int64                   `json:"generatedByUserId,omitempty"` // 生成操作员ID

    // 模板配置
    DefaultTemplates   *AppliedFreightTemplate `json:"defaultTemplates"`   // 默认模板
    UserTemplates      []UserShippingTemplate  `json:"userTemplates"`      // 用户特定模板列表

    // 消息元信息
    MessageID          string                   `json:"messageId"`          // 消息ID
    CreatedAt          time.Time                `json:"createdAt"`          // 消息创建时间
    RetryCount         int                      `json:"retryCount"`         // 重试次数
}
```

**`UserShippingTemplate`** - 用户特定模板配置

```go
type UserShippingTemplate struct {
    UserID          int64                 `json:"userId"`          // 用户ID
    GeneralTemplate *ShippingFeeTemplate  `json:"generalTemplate"` // 普货模板
    BatteryTemplate *ShippingFeeTemplate  `json:"batteryTemplate"` // 带电模板
    PostBoxTemplate *ShippingFeeTemplate  `json:"postBoxTemplate"` // 投函模板
}
```

### 2. 基础设施层

#### 消息队列生产者

**`BillingProducer`** - 账单生成消息生产者

- 负责将账单生成任务发布到 RabbitMQ
- 自动创建交换机、队列和绑定关系
- 支持消息持久化和重试机制
- 提供详细的日志记录

主要方法：

```go
func (p *BillingProducer) PublishBillingTask(ctx context.Context, message *entity.BillingGenerationMessage) error
```

### 3. 应用层

#### 请求/响应结构

**异步生成请求**

```go
type AsyncGenerateBillingRequest struct {
    StartTime         string                       `json:"startTime" binding:"required"` // 开始时间
    EndTime           string                       `json:"endTime" binding:"required"`   // 结束时间
    CustomerIDs       []int64                      `json:"customerIds" binding:"required,min=1"` // 客户ID列表
    BillingCycleID    int64                        `json:"billingCycleId" binding:"required"` // 账期批次ID
    DueDate           *string                      `json:"dueDate,omitempty"`            // 付款截止日期
    Notes             *string                      `json:"notes,omitempty"`              // 备注
    Currency          string                       `json:"currency" binding:"required"`  // 货币单位
    DefaultTemplates  *entity.AppliedFreightTemplate `json:"defaultTemplates,omitempty"`    // 默认模板
    UserTemplates     []AsyncUserTemplateConfig     `json:"userTemplates,omitempty"`       // 用户特定模板
}
```

**异步生成响应**

```go
type AsyncGenerateBillingResponse struct {
    TaskID          string `json:"taskId"`          // 任务ID
    BillingCycleID  int64  `json:"billingCycleId"`  // 账期批次ID
    CustomerCount   int    `json:"customerCount"`   // 客户数量
    Status          string `json:"status"`          // 任务状态
    StatusName      string `json:"statusName"`      // 任务状态名称
    SubmitTime      string `json:"submitTime"`      // 提交时间
    Message         string `json:"message"`         // 响应消息
}
```

#### 服务方法

```go
func (s *BillingServiceImpl) AsyncGenerateBilling(ctx context.Context, req *AsyncGenerateBillingRequest, submittedByUserID *int64) (*AsyncGenerateBillingResponse, int, error)
```

**主要流程：**

1. 参数验证和转换
2. 生成任务 ID (UUID)
3. 使用传入的 submittedByUserID 作为任务提交者
4. 创建账单生成任务记录
5. 保存任务到数据库
6. 构建消息队列消息
7. 发送消息到 RabbitMQ
8. 返回任务信息

**用户 ID 处理：**

- Handler 层从 JWT 令牌中获取当前登录用户 ID
- 通过参数传递给 Service 层，避免 Service 层直接依赖 HTTP 上下文
- 确保任务记录中正确记录提交者信息

### 4. 表现层

#### API 接口

**请求方式：** `POST`

**请求路径：** `/api/v1/finance/billing/async-generate`

**请求头：**

```
Authorization: Bearer {JWT_TOKEN}
Content-Type: application/json
```

**请求示例：**

```json
{
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-01-31 23:59:59",
  "customerIds": [1, 2, 3, 5],
  "billingCycleId": 1,
  "dueDate": "2024-02-28",
  "currency": "CNY",
  "notes": "2024年1月账单",
  "userTemplates": [
    {
      "userId": 1,
      "generalTemplate": {
        /* 用户1的特定普货模板 */
      },
      "batteryTemplate": {
        /* 用户1的特定带电模板 */
      }
    },
    {
      "userId": 2,
      "postBoxTemplate": {
        /* 用户2的特定投函模板 */
      }
    }
  ]
}
```

**响应示例：**

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "billingCycleId": 1,
    "customerCount": 4,
    "status": "PENDING",
    "statusName": "待处理",
    "submitTime": "2024-01-15 10:30:00",
    "message": "异步账单生成任务已创建，将处理 4 个客户的账单"
  },
  "requestId": "req-12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 核心特性

### 1. 多客户支持

- 支持在单个请求中指定多个客户 ID
- 自动验证客户 ID 的有效性和唯一性
- 批量处理提高效率

### 2. 灵活的模板配置

**模板优先级：**

1. 用户特定模板（如果在`userTemplates`中指定）
2. 默认模板（`defaultTemplates`）
3. 系统配置的用户模板

**配置示例：**

- 用户 A：使用特定的普货和带电模板，投函模板使用默认
- 用户 B：使用特定的投函模板，其他使用默认
- 用户 C：全部使用默认模板

### 3. 异步处理优势

- **响应速度快**：立即返回任务 ID，不需要等待处理完成
- **系统稳定性**：长时间运行的任务不会阻塞 API 接口
- **可扩展性**：支持多个消费者并行处理
- **容错能力**：任务失败可以重试

### 4. 任务状态管理

- **PENDING**：待处理
- **PROCESSING**：处理中
- **COMPLETED**：已完成
- **FAILED**：失败

可通过现有的任务查询接口监控任务状态。

## 配置和部署

### 1. 配置文件更新

在 `configs/config.yaml` 中添加：

```yaml
rabbitmq:
  host: *************
  port: 5673
  virtual-host: /
  username: zebra
  password: banma1346797
```

### 2. 依赖包

需要添加以下 Go 模块依赖：

```go
github.com/streadway/amqp  // RabbitMQ客户端
github.com/google/uuid     // UUID生成
```

### 3. 消费者程序

需要单独实现一个消费者程序来处理队列中的任务：

```go
// 示例消费者逻辑
func ProcessBillingTask(message *entity.BillingGenerationMessage) error {
    // 1. 更新任务状态为PROCESSING
    // 2. 遍历每个客户ID
    // 3. 为每个客户生成账单（使用现有的同步逻辑）
    // 4. 更新进度
    // 5. 完成后更新状态为COMPLETED或FAILED
}
```

## 错误处理

### 1. 参数验证

- 时间格式验证
- 客户 ID 列表验证（非空、去重、有效性）
- 账期批次 ID 验证
- 模板配置验证

### 2. 系统容错

- RabbitMQ 连接失败时的降级处理
- 任务保存失败的回滚机制
- 消息发送失败的状态更新

### 3. 错误响应

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "客户ID列表不能为空",
  "requestId": "req-12345",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 监控和运维

### 1. 日志记录

- 任务创建日志
- 消息发送日志
- 错误处理日志
- 性能指标记录

### 2. 任务监控

通过现有的任务查询接口：

- 查看任务状态和进度
- 监控处理速度
- 识别失败任务

### 3. 队列监控

建议监控 RabbitMQ 的：

- 队列长度
- 消息处理速度
- 错误率

## 与现有功能的对比

| 特性     | 同步生成         | 异步生成           |
| -------- | ---------------- | ------------------ |
| 响应时间 | 慢（需等待完成） | 快（立即返回）     |
| 客户数量 | 单个             | 多个               |
| 模板配置 | 固定三种模板     | 灵活的用户特定配置 |
| 系统负载 | 高（阻塞式）     | 低（后台处理）     |
| 错误恢复 | 困难             | 支持重试           |
| 监控能力 | 有限             | 完整的任务跟踪     |

## 后续扩展建议

1. **消费者实现**：开发专门的消费者程序
2. **任务重试机制**：支持失败任务的自动重试
3. **任务取消功能**：支持取消正在等待的任务
4. **批量操作优化**：优化大批量客户的处理性能
5. **实时通知**：集成 WebSocket 推送任务状态变更
6. **任务优先级**：支持紧急任务的优先处理
7. **统计报表**：任务执行效率和成功率统计

## 使用建议

1. **小批量客户**：使用同步接口，响应更直接
2. **大批量客户**：使用异步接口，避免超时
3. **特殊模板需求**：使用异步接口的用户特定模板功能
4. **生产环境**：建议部署多个消费者实例提高处理能力
