package repository

import (
	"context"
	"time"
	"zebra-hub-system/internal/domain/entity"
)

// ManifestRepository 运单仓储接口
type ManifestRepository interface {
	// FindPendingReview 分页查询待审核运单 (status=0)
	// 返回运单列表、总数以及错误
	FindPendingReview(ctx context.Context, page, pageSize int) ([]*entity.Manifest, int64, error)

	// CountPendingReview 统计待审核运单数量
	CountPendingReview(ctx context.Context) (int64, error)

	// GetManifestItemsByManifestID 根据运单ID获取物品列表
	GetManifestItemsByManifestID(ctx context.Context, manifestID int64) ([]*entity.ManifestItem, error)

	// GetManifestItemsByManifestIDs 根据多个运单ID批量获取物品列表
	GetManifestItemsByManifestIDs(ctx context.Context, manifestIDs []int64) ([]*entity.ManifestItem, error)

	// GetManifestByID 根据ID获取运单
	GetManifestByID(ctx context.Context, id int64) (*entity.Manifest, error)

	// GetManifestByOrderNumber 根据商家订单号获取运单
	GetManifestByOrderNumber(ctx context.Context, orderNumber string) (*entity.Manifest, error)

	// GetManifestsByIDs 根据多个ID批量获取运单（不包含物品列表）
	// 用于提高查询性能，避免N+1查询问题
	GetManifestsByIDs(ctx context.Context, ids []int64) ([]*entity.Manifest, error)

	// UpdateManifest 更新运单基本信息
	UpdateManifest(ctx context.Context, manifest *entity.Manifest) error

	// UpdateManifestItems 更新运单物品信息（先删除，再插入）
	UpdateManifestItems(ctx context.Context, manifestID int64, items []*entity.ManifestItem) error

	// UpdateManifestWithItems 在一个事务中同时更新运单基本信息和物品信息
	UpdateManifestWithItems(ctx context.Context, manifest *entity.Manifest, items []*entity.ManifestItem) error

	// UpdateManifestStatus 更新运单状态
	UpdateManifestStatus(ctx context.Context, manifestID int64, status int) error

	// CountProblemManifests 统计问题运单数量
	CountProblemManifests(ctx context.Context) (int64, error)

	// CountPendingProblemTickets 统计待处理的问题工单数量
	CountPendingProblemTickets(ctx context.Context) (int64, error)

	// FindUsersWithProblemManifests 查询拥有问题运单的用户列表
	// 返回用户列表（带待处理问题运单数量）、总用户数和可能的错误
	FindUsersWithProblemManifests(ctx context.Context, page, pageSize int) ([]*entity.UserWithProblemCount, int64, error)

	// GetUserManifestsByTimeRange 获取用户在指定发货时间范围内的运单
	GetUserManifestsByTimeRange(ctx context.Context, userID int64, startTime, endTime time.Time) ([]entity.Manifest, error)

	// GetUserManifestsByCreateTimeRange 获取用户在指定预报时间范围内的运单
	GetUserManifestsByCreateTimeRange(ctx context.Context, userID int64, startTime, endTime time.Time) ([]entity.Manifest, error)

	// SearchManifests 多条件查询运单
	// 参数说明:
	// query: 单号搜索(运单号、转单号、系统订单号、商家订单号)
	// createTimeStart, createTimeEnd: 创建时间范围
	// pickUpTimeStart, pickUpTimeEnd: 揽件时间范围
	// shipmentTimeStart, shipmentTimeEnd: 发货时间范围
	// deliveredTimeStart, deliveredTimeEnd: 签收时间范围
	// status: 运单状态
	// userId: 用户ID
	// masterBillId: 提单ID
	// page, pageSize: 分页参数
	// 返回运单列表、总数以及可能的错误
	SearchManifests(ctx context.Context, query, createTimeStart, createTimeEnd, pickUpTimeStart, pickUpTimeEnd, shipmentTimeStart, shipmentTimeEnd, deliveredTimeStart, deliveredTimeEnd string, status *int, userID *int64, masterBillID *int64, page, pageSize int) ([]*entity.Manifest, int64, error)

	// FindManifestsByExpressNumber 根据快递单号模糊查询运单
	// 参数说明:
	// expressNumber: 快递单号（模糊匹配）
	// page, pageSize: 分页参数
	// 返回运单列表、总数以及可能的错误
	FindManifestsByExpressNumber(ctx context.Context, expressNumber string, page, pageSize int) ([]*entity.Manifest, int64, error)
	
	// CreateManifest 创建运单
	CreateManifest(ctx context.Context, manifest *entity.Manifest) (*entity.Manifest, error)
	
	// CreateManifestItem 创建运单物品
	CreateManifestItem(ctx context.Context, item *entity.ManifestItem) error
	
	// WithTransaction 在事务中执行操作
	WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error

	// GetManifestsByTrackingNumbers 根据一批运单号获取运单列表
	GetManifestsByTrackingNumbers(ctx context.Context, trackingNumbers []string) ([]*entity.Manifest, error)
} 