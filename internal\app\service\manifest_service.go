package service

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	// ManifestDTO uses entity.User indirectly via LoginResponse in other files, ensure this is fine
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util" // 确保导入 util 包

	"github.com/gin-gonic/gin" // 导入 gin
	"github.com/nyaruka/phonenumbers"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

var (
	// 提取电话号码中的数字的正则表达式
	phoneDigitsOnlyRegex = regexp.MustCompile(`\d+`)
)

// const TimeFormat = "2006-01-02 15:04:05" // 这个常量现在定义在 util 包中，这里可以移除

// ListPendingReviewManifestsRequest 分页查询待审核运单请求
type ListPendingReviewManifestsRequest struct {
	Page     int `form:"page,default=1" binding:"min=1"`      // 页码
	PageSize int `form:"pageSize,default=10" binding:"min=1"` // 每页数量
}

// ManifestItemDTO 运单物品信息 (用于响应)
type ManifestItemDTO struct {
	Weight   float64 `json:"weight"`   // 重量
	Quantity int     `json:"quantity"` // 数量
	Price    float64 `json:"price"`    // 单价 (原为 Value 申报价格)
	Name     string  `json:"name"`     // 物品名称
	Value    float64 `json:"value"`    // 申报价值
}

// ManifestDTO 运单信息 (用于响应)
type ManifestDTO struct {
	ID                        int64                     `json:"id"`
	ExpressNumber             string                    `json:"expressNumber"`             // 运单号 (原 express_number)
	OrderNumber               string                    `json:"orderNumber"`               // 商家订单号 (原 order_number)
	OrderNo                   string                    `json:"orderNo"`                   // 系统订单号
	TransferredTrackingNumber string                    `json:"transferredTrackingNumber"` // 转单号
	ReceiverZipCode           string                    `json:"receiverZipCode"`           // 收件人邮编 (原 receiver_zip_code)
	ReceiverName              string                    `json:"receiverName"`              // 收件人姓名 (原 receiver_name)
	ReceiverAddress           string                    `json:"receiverAddress"`           // 收件人地址 (原 receiver_address)
	ReceiverPhone             string                    `json:"receiverPhone"`             // 收件人电话 (原 receiver_phone)
	CreateTime                util.PointerFormattedTime `json:"createTime"`                // 创建时间 (yyyy-MM-dd HH:mm:ss)
	PickUpTime                util.PointerFormattedTime `json:"pickUpTime"`                // 揽件时间 (yyyy-MM-dd HH:mm:ss)
	ShipmentTime              util.PointerFormattedTime `json:"shipmentTime"`              // 发货时间 (yyyy-MM-dd HH:mm:ss)
	DeliveredTime             util.PointerFormattedTime `json:"deliveredTime"`             // 签收时间 (yyyy-MM-dd HH:mm:ss)
	UpdateTime                util.PointerFormattedTime `json:"updateTime"`                // 更新时间 (yyyy-MM-dd HH:mm:ss)
	Status                    int                       `json:"status"`                    // 运单状态
	UserId                    int64                     `json:"userId"`                    // 客户ID
	UserNickname              string                    `json:"userNickname"`              // 客户昵称
	PreRegistrationBatchID    string                    `json:"preRegistrationBatchId"`    // 预报批次ID
	MasterBillID              int64                     `json:"masterBillId"`              // 提单ID
	Remark                    string                    `json:"remark"`                    // 备注
	Items                     []*ManifestItemDTO        `json:"items"`                     // 物品列表
}

// ListPendingReviewManifestsResponse 分页查询待审核运单响应
type ListPendingReviewManifestsResponse struct {
	Total int64          `json:"total"` // 总数
	List  []*ManifestDTO `json:"list"`  // 运单列表
}

// UpdateManifestRequest 更新运单信息请求
type UpdateManifestRequest struct {
	ID              int64              `json:"id" binding:"required"`              // 运单ID
	ReceiverName    string             `json:"receiverName" binding:"required"`    // 收件人姓名
	ReceiverPhone   string             `json:"receiverPhone" binding:"required"`   // 收件人电话
	ReceiverZipCode string             `json:"receiverZipCode" binding:"required"` // 收件人邮编
	ReceiverAddress string             `json:"receiverAddress" binding:"required"` // 收件人地址
	Items           []*ManifestItemDTO `json:"items" binding:"required"`           // 物品列表
}

// UpdateManifestResponse 更新运单信息响应
type UpdateManifestResponse struct {
	ID int64 `json:"id"` // 更新的运单ID
}

// ManifestApproveRequest 运单审核通过请求
type ManifestApproveRequest struct {
	ID int64 `json:"id" binding:"required"` // 运单ID
}

// ManifestApproveResponse 运单审核通过响应
type ManifestApproveResponse struct {
	ID int64 `json:"id"` // 运单ID
}

// ManifestPendingCountResponse 待审核运单数量响应
type ManifestPendingCountResponse struct {
	Count int64 `json:"count"` // 待审核运单数量
}

// ProblemManifestCountResponse 获取问题运单数量的响应
type ProblemManifestCountResponse struct {
	Count int64 `json:"count"` // 问题运单数量
}

// UsersWithProblemManifestsRequest 获取拥有问题运单的用户列表请求
type UsersWithProblemManifestsRequest struct {
	Page     int `form:"page,default=1" binding:"min=1"`      // 页码
	PageSize int `form:"pageSize,default=10" binding:"min=1"` // 每页数量
}

// UserWithProblemManifestsDTO 用户及其问题运单数量DTO
type UserWithProblemManifestsDTO struct {
	UserID       int64  `json:"userId"`       // 用户ID
	Nickname     string `json:"nickname"`     // 用户昵称
	PendingCount int64  `json:"pendingCount"` // 待处理问题运单数量
}

// UsersWithProblemManifestsResponse 拥有问题运单的用户列表响应
type UsersWithProblemManifestsResponse struct {
	Total int64                          `json:"total"` // 总用户数
	List  []*UserWithProblemManifestsDTO `json:"list"`  // 用户列表
}

// ManifestFinancialAdjustmentDTO 运单财务调整记录DTO
type ManifestFinancialAdjustmentDTO struct {
	ID                int64                  `json:"id"`                // 主键ID
	AdjustmentType    string                 `json:"adjustmentType"`    // 调整类型
	Description       string                 `json:"description"`       // 调整描述/原因
	AdditionalDetails map[string]interface{} `json:"additionalDetails"` // 特定调整类型的附加详情 (JSON格式)
	Amount            float64                `json:"amount"`            // 调整金额 (正数收入, 负数支出/赔偿)
	Currency          string                 `json:"currency"`          // 货币单位
	EffectiveDate     util.FormattedTime     `json:"effectiveDate"`     // 费用实际发生/确认日期
	CustomerAccountID int64                  `json:"customerAccountId"` // 客户id
	IsVoid            bool                   `json:"isVoid"`            // 是否已作废 (TRUE: 作废, FALSE: 生效)
	VoidReason        string                 `json:"voidReason"`        // 作废原因 (如果 is_void 为 TRUE)
	VoidedBy          int64                  `json:"voidedBy"`          // 作废操作员ID
	VoidedTime        *time.Time             `json:"voidedTime"`        // 作废时间
	VoidedByNickname  string                 `json:"voidedByNickname"`  // 作废操作员昵称
	AttachmentPaths   string                 `json:"attachmentPaths"`   // 附件路径列表
	CreatorID         int64                  `json:"creatorId"`         // 创建者ID
	CreatorNickname   string                 `json:"creatorNickname"`   // 创建者昵称
}

// ManifestDetailDTO 运单详情信息 (用于响应)
// 包含基本信息、包裹信息和费用信息
type ManifestDetailDTO struct {
	ID                        int64                             `json:"id"`
	ExpressNumber             string                            `json:"expressNumber"`             // 运单号
	OrderNumber               string                            `json:"orderNumber"`               // 商家订单号
	OrderNo                   string                            `json:"orderNo"`                   // 系统订单号
	TransferredTrackingNumber string                            `json:"transferredTrackingNumber"` // 转单号
	ReceiverZipCode           string                            `json:"receiverZipCode"`           // 收件人邮编
	ReceiverName              string                            `json:"receiverName"`              // 收件人姓名
	ReceiverAddress           string                            `json:"receiverAddress"`           // 收件人地址
	ReceiverPhone             string                            `json:"receiverPhone"`             // 收件人电话
	CreateTime                util.PointerFormattedTime         `json:"createTime"`                // 创建时间
	PickUpTime                util.PointerFormattedTime         `json:"pickUpTime"`                // 揽件时间
	ShipmentTime              util.PointerFormattedTime         `json:"shipmentTime"`              // 发货时间
	DeliveredTime             util.PointerFormattedTime         `json:"deliveredTime"`             // 签收时间
	UpdateTime                util.PointerFormattedTime         `json:"updateTime"`                // 更新时间
	Status                    int                               `json:"status"`                    // 运单状态
	UserId                    int64                             `json:"userId"`                    // 客户ID
	UserNickname              string                            `json:"userNickname"`              // 客户昵称
	PreRegistrationBatchID    string                            `json:"preRegistrationBatchId"`    // 预报批次ID
	MasterBillID              int64                             `json:"masterBillId"`              // 提单ID
	Remark                    string                            `json:"remark"`                    // 备注
	Items                     []*ManifestItemDTO                `json:"items"`                     // 物品列表
	FinancialAdjustments      []*ManifestFinancialAdjustmentDTO `json:"financialAdjustments"`      // 财务调整记录

	// 包裹信息
	Weight            float64 `json:"weight"`            // 重量
	Length            float64 `json:"length"`            // 长
	Width             float64 `json:"width"`             // 宽
	Height            float64 `json:"height"`            // 高
	DimensionalWeight float64 `json:"dimensionalWeight"` // 体积重量

	// 费用信息
	Cost                float64 `json:"cost"`                // 基本费用
	OverLengthSurcharge float64 `json:"overLengthSurcharge"` // 超长费
	RemoteAreaSurcharge float64 `json:"remoteAreaSurcharge"` // 偏远费
	OtherCostName       string  `json:"otherCostName"`       // 其他费用名称
	OtherCost           float64 `json:"otherCost"`           // 其他费用
	TotalFee            float64 `json:"totalFee"`            // 总费用
}

// GetManifestByIDRequest 获取运单详情请求
type GetManifestByIDRequest struct {
	ID int64 `uri:"id" binding:"required"` // 运单ID
}

// GetManifestByIDResponse 获取运单详情响应
type GetManifestByIDResponse struct {
	*ManifestDetailDTO // 运单详情
}

// SearchManifestsRequest 多条件查询运单请求
type SearchManifestsRequest struct {
	Query              string `form:"query" json:"query"`                                    // 单号搜索(运单号、转单号、系统订单号、商家订单号)
	CreateTimeStart    string `form:"createTimeStart" json:"createTimeStart"`                // 创建开始时间
	CreateTimeEnd      string `form:"createTimeEnd" json:"createTimeEnd"`                    // 创建结束时间
	PickUpTimeStart    string `form:"pickUpTimeStart" json:"pickUpTimeStart"`                // 揽件开始时间
	PickUpTimeEnd      string `form:"pickUpTimeEnd" json:"pickUpTimeEnd"`                    // 揽件结束时间
	ShipmentTimeStart  string `form:"shipmentTimeStart" json:"shipmentTimeStart"`            // 发货开始时间
	ShipmentTimeEnd    string `form:"shipmentTimeEnd" json:"shipmentTimeEnd"`                // 发货结束时间
	DeliveredTimeStart string `form:"deliveredTimeStart" json:"deliveredTimeStart"`          // 签收开始时间
	DeliveredTimeEnd   string `form:"deliveredTimeEnd" json:"deliveredTimeEnd"`              // 签收结束时间
	Status             *int   `form:"status" json:"status"`                                  // 运单状态
	UserID             *int64 `form:"userId" json:"userId"`                                  // 用户ID
	MasterBillID       *int64 `form:"masterBillId" json:"masterBillId"`                      // 提单ID
	Page               int    `form:"page" json:"page" binding:"min=1" default:"1"`          // 页码
	PageSize           int    `form:"pageSize" json:"pageSize" binding:"min=1" default:"10"` // 每页数量
}

// SearchManifestsResponse 多条件查询运单响应
type SearchManifestsResponse struct {
	Total int64          `json:"total"` // 总数
	List  []*ManifestDTO `json:"list"`  // 运单列表
}

// GetManifestsByExpressNumberRequest 根据快递单号模糊查询运单请求
type GetManifestsByExpressNumberRequest struct {
	ExpressNumber string `form:"expressNumber" json:"expressNumber" binding:"required"` // 快递单号（支持模糊查询）
	Page          int    `form:"page" json:"page" binding:"min=1" default:"1"`          // 页码
	PageSize      int    `form:"pageSize" json:"pageSize" binding:"min=1" default:"10"` // 每页数量
}

// GetManifestsByExpressNumberResponse 根据快递单号模糊查询运单响应
type GetManifestsByExpressNumberResponse struct {
	Total int64          `json:"total"` // 总数
	List  []*ManifestDTO `json:"list"`  // 运单列表
}

// CreateOrderItemRequest 创建订单物品请求
type CreateOrderItemRequest struct {
	Name     string `json:"name" binding:"required"`           // 物品名称
	Quantity int    `json:"quantity" binding:"required,min=1"` // 物品数量
}

// CreateOrderRequest 外部系统创建订单请求
type CreateOrderRequest struct {
	MerchantOrderNumber string                    `json:"merchantOrderNumber" binding:"required"` // 商家订单号
	ShipmentTypeID      int64                     `json:"shipmentTypeId" binding:"required"`      // 货物类型ID
	ReceiverName        string                    `json:"receiverName" binding:"required"`        // 收件人姓名
	ReceiverPhone       string                    `json:"receiverPhone" binding:"required"`       // 收件人电话
	ReceiverZipCode     string                    `json:"receiverZipCode" binding:"required"`     // 收件人邮编
	ReceiverAddress     string                    `json:"receiverAddress" binding:"required"`     // 收件人地址
	Items               []*CreateOrderItemRequest `json:"items" binding:"required,dive"`          // 物品列表
}

// CreateOrderResponse 外部系统创建订单响应
type CreateOrderResponse struct {
	TrackingNumber      string `json:"trackingNumber"`      // 运单号
	MerchantOrderNumber string `json:"merchantOrderNumber"` // 商家订单号
	SystemOrderNo       string `json:"systemOrderNo"`       // 系统订单号
}

// GenerateLabelsRequest 生成面单请求
type GenerateLabelsRequest struct {
	TrackingNumbers []string `json:"trackingNumbers" binding:"required,min=1"`
}

// ManifestService 运单服务接口
type ManifestService interface {
	ListPendingReview(ginCtx *gin.Context, req *ListPendingReviewManifestsRequest) (*ListPendingReviewManifestsResponse, int, error)
	UpdateManifest(ginCtx *gin.Context, req *UpdateManifestRequest) (*UpdateManifestResponse, int, error)
	ApproveManifest(ginCtx *gin.Context, req *ManifestApproveRequest) (*ManifestApproveResponse, int, error)
	GetPendingCount(ginCtx *gin.Context) (*ManifestPendingCountResponse, int, error)
	GetManifestByID(ginCtx *gin.Context, req *GetManifestByIDRequest) (*GetManifestByIDResponse, int, error)
	GetProblemManifestCount(ginCtx *gin.Context) (*ProblemManifestCountResponse, int, error)
	GetUsersWithProblemManifests(ginCtx *gin.Context, req *UsersWithProblemManifestsRequest) (*UsersWithProblemManifestsResponse, int, error)
	SearchManifests(ginCtx *gin.Context, req *SearchManifestsRequest) (*SearchManifestsResponse, int, error)
	GetManifestsByExpressNumber(ginCtx *gin.Context, req *GetManifestsByExpressNumberRequest) (*GetManifestsByExpressNumberResponse, int, error)
	CreateOrder(ginCtx *gin.Context, req *CreateOrderRequest) (*CreateOrderResponse, int, error)
	GenerateLabels(ginCtx *gin.Context, req *GenerateLabelsRequest) ([]byte, error)
}

// ManifestServiceImpl 运单服务实现
type ManifestServiceImpl struct {
	manifestRepo                    repository.ManifestRepository
	userRepo                        repository.UserRepository
	zipCodeService                  service.ZipCodeService                           // 邮编服务
	translateService                service.TranslateService                         // 翻译服务
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository // 运单财务调整记录仓储
	trackingNumberRepo              repository.TrackingNumberRepository              // 运单号仓储
	pdfService                      PDFService // 新增PDF服务
}

// NewManifestService 创建运单服务
func NewManifestService(
	manifestRepo repository.ManifestRepository,
	userRepo repository.UserRepository,
	zipCodeService service.ZipCodeService,
	translateService service.TranslateService,
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository,
	trackingNumberRepo repository.TrackingNumberRepository,
	pdfService PDFService, // 新增PDF服务
) ManifestService {
	return &ManifestServiceImpl{
		manifestRepo:                    manifestRepo,
		userRepo:                        userRepo,
		zipCodeService:                  zipCodeService,
		translateService:                translateService,
		manifestFinancialAdjustmentRepo: manifestFinancialAdjustmentRepo,
		trackingNumberRepo:              trackingNumberRepo,
		pdfService:                      pdfService, // 新增PDF服务
	}
}

// ListPendingReview 分页查询待审核运单
func (s *ManifestServiceImpl) ListPendingReview(ginCtx *gin.Context, req *ListPendingReviewManifestsRequest) (*ListPendingReviewManifestsResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Listing pending review manifests", zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))

	manifests, total, err := s.manifestRepo.FindPendingReview(ctx, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to find pending review manifests from repository", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 收集所有用户ID
	userIDs := make([]int64, 0, len(manifests))
	userIDMap := make(map[int64]bool) // 用于去重
	for _, m := range manifests {
		if m.UserID > 0 && !userIDMap[m.UserID] {
			userIDs = append(userIDs, m.UserID)
			userIDMap[m.UserID] = true
		}
	}

	// 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			logger.Error("Failed to get users by IDs", zap.Error(err), zap.Int64s("userIDs", userIDs))
			// 这里选择继续，不因为用户信息查询失败而中断整个请求
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	manifestDTOs := make([]*ManifestDTO, 0, len(manifests))
	for _, m := range manifests {
		// 获取用户昵称
		var userNickname string
		if user, exists := userMap[m.UserID]; exists && user != nil {
			userNickname = user.Nickname
		}

		// 将电话号码和邮编格式化为展示格式
		formattedPhone := formatPhoneNumber(m.ReceiverPhone)
		formattedZipCode := s.zipCodeService.FormatZipCode(m.ReceiverZipCode)

		manifestDTOs = append(manifestDTOs, &ManifestDTO{
			ID:                        m.ID,
			ExpressNumber:             m.ExpressNumber,
			OrderNumber:               m.OrderNumber,
			OrderNo:                   m.OrderNo,
			TransferredTrackingNumber: m.TransferredTrackingNumber,
			ReceiverZipCode:           formattedZipCode,
			ReceiverName:              m.ReceiverName,
			ReceiverAddress:           m.ReceiverAddress,
			ReceiverPhone:             formattedPhone,
			CreateTime:                util.NewPointerFormattedTime(&m.CreateTime),
			PickUpTime:                m.PickUpTime,
			ShipmentTime:              m.ShipmentTime,
			DeliveredTime:             m.DeliveredTime,
			UpdateTime:                util.NewPointerFormattedTime(&m.UpdateTime),
			Status:                    m.Status,
			UserId:                    m.UserID,
			UserNickname:              userNickname,
			PreRegistrationBatchID:    m.PreRegistrationBatchID,
			MasterBillID:              m.MasterBillID,
			Remark:                    m.Remark,
			Items:                     make([]*ManifestItemDTO, 0, len(m.Items)),
		})

		// 转换物品列表
		for _, item := range m.Items {
			manifestDTOs[len(manifestDTOs)-1].Items = append(manifestDTOs[len(manifestDTOs)-1].Items, &ManifestItemDTO{
				Name:     item.Name,
				Weight:   item.Weight,
				Quantity: item.Quantity,
				Price:    item.Price,
				Value:    item.Value,
			})
		}
	}

	logger.Debug("Successfully listed pending review manifests", zap.Int64("totalCount", total), zap.Int("returnedCount", len(manifestDTOs)))
	return &ListPendingReviewManifestsResponse{
		Total: total,
		List:  manifestDTOs,
	}, valueobject.SUCCESS, nil
}

// normalizePhoneNumber 标准化电话号码(只保留数字)
func normalizePhoneNumber(phone string) string {
	digits := phoneDigitsOnlyRegex.FindAllString(phone, -1)
	return strings.Join(digits, "")
}

// formatPhoneNumber 格式化电话号码(用于展示)
func formatPhoneNumber(phone string) string {
	// 只包含数字的情况下进行格式化
	digits := normalizePhoneNumber(phone)

	// 为不同长度的电话号码添加不同的格式
	switch len(digits) {
	case 10: // 固定电话 03XXXXXXXX
		return digits[0:2] + "-" + digits[2:6] + "-" + digits[6:10]
	case 11: // 手机号码 090XXXXXXXX
		return digits[0:3] + "-" + digits[3:7] + "-" + digits[7:11]
	default:
		return phone // 无法识别的格式，保持原样
	}
}

// validateJapanesePhoneNumber 验证日本电话号码
func validateJapanesePhoneNumber(phone string) error {
	// 只保留数字
	normalizedPhone := normalizePhoneNumber(phone)

	// 检查长度 (日本电话号码通常是10-11位)
	if len(normalizedPhone) < 10 || len(normalizedPhone) > 11 {
		return errors.New("日本电话号码长度应为10-11位数字")
	}

	// 创建标准格式的电话号码
	var phoneWithCountryCode string
	if strings.HasPrefix(normalizedPhone, "0") {
		// 如果以0开头，去掉0，加上日本国家代码+81
		phoneWithCountryCode = "+81" + normalizedPhone[1:]
	} else {
		// 否则直接加上国家代码
		phoneWithCountryCode = "+81" + normalizedPhone
	}

	// 使用日本地区代码 "JP" 解析
	num, err := phonenumbers.Parse(phoneWithCountryCode, "JP")
	if err != nil {
		return fmt.Errorf("无效的电话号码格式: %v", err)
	}

	// 验证是否是有效的日本手机号码或固定电话
	if !phonenumbers.IsValidNumberForRegion(num, "JP") {
		return errors.New("无效的日本电话号码")
	}

	return nil
}

// isOkinawaZipCode 判断是否为冲绳县邮编
func (s *ManifestServiceImpl) isOkinawaZipCode(zipCode string) bool {
	// 优先使用ZipCodeService的判断方法
	return s.zipCodeService.IsOkinawaPrefecture(zipCode)
}

// isPostBoxShipmentType 判断是否为投函货物类型 (假设类型3为投函)
func isPostBoxShipmentType(shipmentTypeID int64) bool {
	return shipmentTypeID == 3
}

// generateSystemOrderNumber 生成系统订单号
func generateSystemOrderNumber(shipmentTypeID int64, trackingNumber string) (string, error) {
	now := time.Now()

	var prefix string
	if isPostBoxShipmentType(shipmentTypeID) {
		prefix = "SF"
	} else {
		// 普通货物或带电货物
		prefix = "BM" + fmt.Sprintf("%02d", now.Day())
	}

	return prefix + trackingNumber, nil
}

// UpdateManifest 更新运单信息
func (s *ManifestServiceImpl) UpdateManifest(ginCtx *gin.Context, req *UpdateManifestRequest) (*UpdateManifestResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Updating manifest", zap.Int64("manifestId", req.ID))

	// 标准化邮编(只保留数字)
	normalizedZipCode := s.zipCodeService.NormalizeZipCode(req.ReceiverZipCode)

	// 校验日本邮编格式
	if !s.zipCodeService.IsValidFormat(req.ReceiverZipCode) {
		logger.Warn("Invalid Japanese ZIP code format", zap.String("zipCode", req.ReceiverZipCode), zap.String("normalized", normalizedZipCode))
		return nil, valueobject.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID, errors.New("邮编格式错误，请输入7位数字或XXX-XXXX格式")
	}

	// 校验日本邮编是否存在
	if !s.zipCodeService.IsExist(normalizedZipCode) {
		logger.Warn("Japanese ZIP code does not exist", zap.String("zipCode", req.ReceiverZipCode), zap.String("normalized", normalizedZipCode))
		return nil, valueobject.ERROR_MANIFEST_ZIPCODE_NOT_EXIST, errors.New("该邮编不存在于日本邮编库中，请核对后重新输入")
	}

	// 标准化电话号码(只保留数字)
	normalizedPhone := normalizePhoneNumber(req.ReceiverPhone)

	// 校验日本电话号码
	if err := validateJapanesePhoneNumber(normalizedPhone); err != nil {
		logger.Warn("Invalid Japanese phone number", zap.String("phone", req.ReceiverPhone), zap.String("normalized", normalizedPhone), zap.Error(err))
		return nil, valueobject.ERROR_MANIFEST_PHONE_FORMAT_INVALID, fmt.Errorf("电话号码格式错误: %v", err)
	}

	// 1. 检查运单是否存在
	manifest, err := s.manifestRepo.GetManifestByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warn("Manifest not found", zap.Int64("manifestId", req.ID))
			return nil, valueobject.ERROR_MANIFEST_NOT_FOUND, errors.New("运单不存在")
		}
		logger.Error("Failed to get manifest by ID", zap.Error(err), zap.Int64("manifestId", req.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 更新运单基本信息 (存储标准化后的数据)
	manifest.ReceiverName = req.ReceiverName
	manifest.ReceiverPhone = normalizedPhone
	manifest.ReceiverZipCode = normalizedZipCode
	manifest.ReceiverAddress = req.ReceiverAddress

	// 3. 校验通过，设置ValidationStatus为1，清空ValidationError
	manifest.ValidationStatus = 1
	manifest.ValidationError = ""

	logger.Debug("Setting validation status to 1 and clearing validation error",
		zap.Int64("manifestId", manifest.ID),
		zap.Int("validationStatus", manifest.ValidationStatus))

	// 4. 准备物品信息
	items := make([]*entity.ManifestItem, 0, len(req.Items))
	for _, itemDTO := range req.Items {
		// 使用原始文本，不进行清理
		item := &entity.ManifestItem{
			ManifestID: req.ID,
			Name:       itemDTO.Name, // 使用原始名称，不清理
			Weight:     itemDTO.Weight,
			Quantity:   itemDTO.Quantity,
			Price:      itemDTO.Price,
			Value:      itemDTO.Value, // 直接使用前端传递的值，不进行计算
		}
		items = append(items, item)
	}

	// 5. 在一个事务中同时更新运单信息和物品信息
	if err := s.manifestRepo.UpdateManifestWithItems(ctx, manifest, items); err != nil {
		logger.Error("Failed to update manifest with items", zap.Error(err), zap.Int64("manifestId", req.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	logger.Info("Successfully updated manifest", zap.Int64("manifestId", req.ID))
	return &UpdateManifestResponse{
		ID: req.ID,
	}, valueobject.SUCCESS, nil
}

// ApproveManifest 审核通过运单
func (s *ManifestServiceImpl) ApproveManifest(ginCtx *gin.Context, req *ManifestApproveRequest) (*ManifestApproveResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Approving manifest", zap.Int64("manifestId", req.ID))

	// 1. 检查运单是否存在
	manifest, err := s.manifestRepo.GetManifestByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warn("Manifest not found", zap.Int64("manifestId", req.ID))
			return nil, valueobject.ERROR_MANIFEST_NOT_FOUND, errors.New("运单不存在")
		}
		logger.Error("Failed to get manifest by ID", zap.Error(err), zap.Int64("manifestId", req.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 2. 获取运单物品列表
	items, err := s.manifestRepo.GetManifestItemsByManifestID(ctx, req.ID)
	if err != nil {
		logger.Error("Failed to get manifest items", zap.Error(err), zap.Int64("manifestId", req.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 3. 如果有翻译服务可用，翻译物品名称
	if s.translateService != nil && len(items) > 0 {
		// 清理并标准化所有物品名称
		hasNameChanges := false
		itemNames := make([]string, len(items))

		for i, item := range items {
			// 清理物品名称，只保留中英文和常用标点符号
			cleanedName := util.NormalizeItemName(item.Name)

			// 如果名称有变化，则更新
			if cleanedName != item.Name {
				items[i].Name = cleanedName
				hasNameChanges = true
			}

			itemNames[i] = cleanedName
		}

		// 如果有物品名称被清理，先更新到数据库
		if hasNameChanges {
			logger.Info("Item names were cleaned and normalized", zap.Int64("manifestId", req.ID))
		}

		// 执行批量翻译（从中文到英文）
		translatedNames, err := s.translateService.TranslateTexts(ctx, itemNames, "zh-CN", "en")
		if err != nil {
			logger.Error("Failed to translate item names", zap.Error(err), zap.Int64("manifestId", req.ID))
			// 注意：我们不因为翻译失败而中断整个流程
		} else {
			// 更新物品英文名称
			for i, translatedName := range translatedNames {
				// 也对翻译后的英文名称进行清理
				items[i].NameEn = util.NormalizeItemName(translatedName)
			}

			// 更新物品信息到数据库
			if err := s.manifestRepo.UpdateManifestItems(ctx, req.ID, items); err != nil {
				logger.Error("Failed to update manifest items with translated names", zap.Error(err), zap.Int64("manifestId", req.ID))
				// 不因为翻译更新失败而中断审核流程
			} else {
				logger.Info("Successfully translated and updated item names", zap.Int64("manifestId", req.ID))
			}
		}
	}

	// 4. 更新运单状态为已通过(1)
	manifest.Status = 1

	// 5. 更新数据库
	if err := s.manifestRepo.UpdateManifestStatus(ctx, manifest.ID, manifest.Status); err != nil {
		logger.Error("Failed to update manifest status", zap.Error(err), zap.Int64("manifestId", req.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	logger.Info("Successfully approved manifest", zap.Int64("manifestId", req.ID))
	return &ManifestApproveResponse{
		ID: req.ID,
	}, valueobject.SUCCESS, nil
}

// GetPendingCount 获取待审核运单数量
func (s *ManifestServiceImpl) GetPendingCount(ginCtx *gin.Context) (*ManifestPendingCountResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Getting pending review manifest count")

	count, err := s.manifestRepo.CountPendingReview(ctx)
	if err != nil {
		logger.Error("Failed to count pending review manifests", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	logger.Debug("Successfully got pending review manifest count", zap.Int64("count", count))
	return &ManifestPendingCountResponse{
		Count: count,
	}, valueobject.SUCCESS, nil
}

// GetManifestByID 获取运单详情
func (s *ManifestServiceImpl) GetManifestByID(ginCtx *gin.Context, req *GetManifestByIDRequest) (*GetManifestByIDResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Getting manifest by ID", zap.Int64("manifestId", req.ID))

	// 获取运单信息
	manifest, err := s.manifestRepo.GetManifestByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Warn("Manifest not found", zap.Int64("manifestId", req.ID))
			return nil, valueobject.ERROR_MANIFEST_NOT_FOUND, errors.New("运单不存在")
		}
		logger.Error("Failed to get manifest by ID", zap.Error(err), zap.Int64("manifestId", req.ID))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 获取用户信息（如果有用户ID）
	var userNickname string
	if manifest.UserID > 0 {
		users, err := s.userRepo.FindByIDs(ctx, []int64{manifest.UserID})
		if err == nil && len(users) > 0 {
			userNickname = users[0].Nickname
		} else if err != nil {
			logger.Warn("Failed to get user info", zap.Error(err), zap.Int64("userId", manifest.UserID))
		}
	}

	// 将电话号码和邮编格式化为展示格式
	formattedPhone := formatPhoneNumber(manifest.ReceiverPhone)
	formattedZipCode := s.zipCodeService.FormatZipCode(manifest.ReceiverZipCode)

	// 转换物品列表
	itemDTOs := make([]*ManifestItemDTO, 0, len(manifest.Items))
	for _, item := range manifest.Items {
		itemDTOs = append(itemDTOs, &ManifestItemDTO{
			Name:     item.Name,
			Weight:   item.Weight,
			Quantity: item.Quantity,
			Price:    item.Price,
			Value:    item.Value,
		})
	}

	// 获取财务调整记录
	financialAdjustments, err := s.manifestFinancialAdjustmentRepo.FindByManifestID(ctx, req.ID)
	if err != nil {
		logger.Warn("Failed to get financial adjustments", zap.Error(err), zap.Int64("manifestId", req.ID))
		// 不因为获取财务调整记录失败而中断整个请求
	}

	// 收集所有创建者ID
	creatorIDs := make([]int64, 0, len(financialAdjustments))
	creatorIDMap := make(map[int64]bool) // 用于去重
	for _, adjustment := range financialAdjustments {
		if adjustment.CreatorID != nil && *adjustment.CreatorID > 0 && !creatorIDMap[*adjustment.CreatorID] {
			creatorIDs = append(creatorIDs, *adjustment.CreatorID)
			creatorIDMap[*adjustment.CreatorID] = true
		}
	}

	// 收集所有需要查询昵称的用户ID（创建者和作废操作员）
	userIDs := make([]int64, 0, len(financialAdjustments)*2) // 预估每个调整记录可能有两个用户ID
	userIDMap := make(map[int64]bool)                        // 用于去重

	for _, adjustment := range financialAdjustments {
		// 添加创建者ID
		if adjustment.CreatorID != nil && *adjustment.CreatorID > 0 && !userIDMap[*adjustment.CreatorID] {
			userIDs = append(userIDs, *adjustment.CreatorID)
			userIDMap[*adjustment.CreatorID] = true
		}

		// 添加作废操作员ID
		if adjustment.VoidedBy != nil && *adjustment.VoidedBy > 0 && !userIDMap[*adjustment.VoidedBy] {
			userIDs = append(userIDs, *adjustment.VoidedBy)
			userIDMap[*adjustment.VoidedBy] = true
		}
	}

	// 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			logger.Warn("Failed to get users by IDs", zap.Error(err), zap.Int64s("userIDs", userIDs))
			// 不因为获取用户信息失败而中断整个请求
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	// 转换财务调整记录
	financialAdjustmentDTOs := make([]*ManifestFinancialAdjustmentDTO, 0, len(financialAdjustments))
	for _, adjustment := range financialAdjustments {
		var creatorNickname string
		var creatorIDValue int64
		if adjustment.CreatorID != nil {
			creatorIDValue = *adjustment.CreatorID
			if creator, exists := userMap[creatorIDValue]; exists && creator != nil {
				creatorNickname = creator.Nickname
			}
		}

		var voidedByNickname string
		var voidedByIDValue int64
		if adjustment.VoidedBy != nil && *adjustment.VoidedBy > 0 {
			voidedByIDValue = *adjustment.VoidedBy
			if voider, exists := userMap[voidedByIDValue]; exists && voider != nil {
				voidedByNickname = voider.Nickname
			}
		}

		var attachmentPathsStr string
		if adjustment.AdditionalDetails != nil {
			if pathsInterface, ok := (*adjustment.AdditionalDetails)["proofOfValueImageUrls"]; ok {
				if paths, okPaths := pathsInterface.([]interface{}); okPaths { // JSON unmarshals arrays into []interface{}
					var strPaths []string
					for _, p := range paths {
						if s, okPath := p.(string); okPath {
							strPaths = append(strPaths, s)
						}
					}
					attachmentPathsStr = strings.Join(strPaths, ";")
				} else if pathStr, okStr := pathsInterface.(string); okStr { // Handle if it was stored as a single string
					attachmentPathsStr = pathStr
				}
			}
		}

		// 处理 additionalDetails
		var additionalDetailsMap map[string]interface{}
		if adjustment.AdditionalDetails != nil {
			additionalDetailsMap = *adjustment.AdditionalDetails
		} else {
			additionalDetailsMap = make(map[string]interface{})
		}

		financialAdjustmentDTOs = append(financialAdjustmentDTOs, &ManifestFinancialAdjustmentDTO{
			ID:                adjustment.ID,
			AdjustmentType:    adjustment.AdjustmentType,
			Description:       adjustment.Description,
			AdditionalDetails: additionalDetailsMap,
			Amount:            adjustment.Amount,
			Currency:          adjustment.Currency,
			EffectiveDate:     util.NewFormattedTime(adjustment.EffectiveDate),
			CustomerAccountID: adjustment.CustomerAccountID,
			IsVoid:            adjustment.IsVoid,
			VoidReason:        adjustment.VoidReason,
			VoidedBy:          voidedByIDValue,
			VoidedTime:        adjustment.VoidedTime,
			VoidedByNickname:  voidedByNickname,
			AttachmentPaths:   attachmentPathsStr,
			CreatorID:         creatorIDValue,
			CreatorNickname:   creatorNickname,
		})
	}

	// 计算总费用 = 基础费用 + 超长费 + 偏远费 + 其他费用 + 所有未作废的财务调整金额
	var totalAdjustmentAmount float64 = 0
	for _, adjustment := range financialAdjustments {
		// 只计算未作废的财务调整记录
		if !adjustment.IsVoid {
			totalAdjustmentAmount += adjustment.Amount
		}
	}

	// 基础费用包括：基本费用 + 超长费 + 偏远费 + 其他费用
	baseFee := manifest.Cost + manifest.OverLengthSurcharge + manifest.RemoteAreaSurcharge + manifest.OtherCost

	// 总费用 = 基础费用 + 财务调整金额
	totalFee := baseFee + totalAdjustmentAmount

	logger.Debug("Calculated fee total",
		zap.Float64("baseFee", baseFee),
		zap.Float64("totalAdjustmentAmount", totalAdjustmentAmount),
		zap.Float64("totalFee", totalFee))

	// 构建ManifestDetailDTO
	manifestDetailDTO := &ManifestDetailDTO{
		ID:                        manifest.ID,
		ExpressNumber:             manifest.ExpressNumber,
		OrderNumber:               manifest.OrderNumber,
		OrderNo:                   manifest.OrderNo,
		TransferredTrackingNumber: manifest.TransferredTrackingNumber,
		ReceiverZipCode:           formattedZipCode, // 使用格式化后的邮编
		ReceiverName:              manifest.ReceiverName,
		ReceiverAddress:           manifest.ReceiverAddress,
		ReceiverPhone:             formattedPhone, // 使用格式化后的电话
		CreateTime:                util.NewPointerFormattedTime(&manifest.CreateTime),
		PickUpTime:                manifest.PickUpTime,
		ShipmentTime:              manifest.ShipmentTime,
		DeliveredTime:             manifest.DeliveredTime,
		UpdateTime:                util.NewPointerFormattedTime(&manifest.UpdateTime),
		Status:                    manifest.Status,
		UserId:                    manifest.UserID,
		UserNickname:              userNickname,
		PreRegistrationBatchID:    manifest.PreRegistrationBatchID,
		MasterBillID:              manifest.MasterBillID,
		Remark:                    manifest.Remark,
		Items:                     itemDTOs,
		FinancialAdjustments:      financialAdjustmentDTOs,
		Weight:                    manifest.Weight,
		Length:                    manifest.Length,
		Width:                     manifest.Width,
		Height:                    manifest.Height,
		DimensionalWeight:         manifest.DimensionalWeight,
		Cost:                      manifest.Cost,
		OverLengthSurcharge:       manifest.OverLengthSurcharge,
		RemoteAreaSurcharge:       manifest.RemoteAreaSurcharge,
		OtherCostName:             manifest.OtherCostName,
		OtherCost:                 manifest.OtherCost,
		TotalFee:                  totalFee, // 使用重新计算的总费用
	}

	logger.Debug("Successfully got manifest by ID", zap.Int64("manifestId", req.ID))
	return &GetManifestByIDResponse{
		ManifestDetailDTO: manifestDetailDTO,
	}, valueobject.SUCCESS, nil
}

// GetProblemManifestCount 获取问题运单数量 (现在指待处理的工单数量)
func (s *ManifestServiceImpl) GetProblemManifestCount(ginCtx *gin.Context) (*ProblemManifestCountResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	count, err := s.manifestRepo.CountPendingProblemTickets(ctx) // 调用新的仓库方法
	if err != nil {
		logger.Error("Failed to count pending problem tickets from repository", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err // 使用通用错误码
	}

	logger.Info("Successfully counted pending problem tickets", zap.Int64("count", count))
	return &ProblemManifestCountResponse{Count: count}, valueobject.SUCCESS, nil
}

// GetUsersWithProblemManifests 获取拥有问题运单的用户列表
func (s *ManifestServiceImpl) GetUsersWithProblemManifests(ginCtx *gin.Context, req *UsersWithProblemManifestsRequest) (*UsersWithProblemManifestsResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Getting users with problem manifests", zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))

	users, total, err := s.manifestRepo.FindUsersWithProblemManifests(ctx, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to find users with problem manifests from repository", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	userDTOs := make([]*UserWithProblemManifestsDTO, 0, len(users))
	for _, user := range users {
		userDTOs = append(userDTOs, &UserWithProblemManifestsDTO{
			UserID:       user.ID,
			Nickname:     user.Nickname,
			PendingCount: user.PendingCount,
		})
	}

	logger.Debug("Successfully got users with problem manifests", zap.Int64("totalCount", total), zap.Int("returnedCount", len(userDTOs)))
	return &UsersWithProblemManifestsResponse{
		Total: total,
		List:  userDTOs,
	}, valueobject.SUCCESS, nil
}

// SearchManifests 多条件查询运单
func (s *ManifestServiceImpl) SearchManifests(ginCtx *gin.Context, req *SearchManifestsRequest) (*SearchManifestsResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Searching manifests", zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))

	manifests, total, err := s.manifestRepo.SearchManifests(ctx, req.Query, req.CreateTimeStart, req.CreateTimeEnd, req.PickUpTimeStart, req.PickUpTimeEnd, req.ShipmentTimeStart, req.ShipmentTimeEnd, req.DeliveredTimeStart, req.DeliveredTimeEnd, req.Status, req.UserID, req.MasterBillID, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to search manifests from repository", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 收集所有用户ID
	userIDs := make([]int64, 0, len(manifests))
	userIDMap := make(map[int64]bool) // 用于去重
	for _, m := range manifests {
		if m.UserID > 0 && !userIDMap[m.UserID] {
			userIDs = append(userIDs, m.UserID)
			userIDMap[m.UserID] = true
		}
	}

	// 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			logger.Warn("Failed to get users by IDs", zap.Error(err), zap.Int64s("userIDs", userIDs))
			// 这里选择继续，不因为用户信息查询失败而中断整个请求
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	manifestDTOs := make([]*ManifestDTO, 0, len(manifests))
	for _, m := range manifests {
		// 获取用户昵称
		var userNickname string
		if user, exists := userMap[m.UserID]; exists && user != nil {
			userNickname = user.Nickname
		}

		// 将电话号码和邮编格式化为展示格式
		formattedPhone := formatPhoneNumber(m.ReceiverPhone)
		formattedZipCode := s.zipCodeService.FormatZipCode(m.ReceiverZipCode)

		// 转换物品列表
		itemDTOs := make([]*ManifestItemDTO, 0, len(m.Items))
		for _, item := range m.Items {
			itemDTOs = append(itemDTOs, &ManifestItemDTO{
				Name:     item.Name,
				Weight:   item.Weight,
				Quantity: item.Quantity,
				Price:    item.Price,
				Value:    item.Value,
			})
		}

		// 构建DTO
		manifestDTO := &ManifestDTO{
			ID:                        m.ID,
			ExpressNumber:             m.ExpressNumber,
			OrderNumber:               m.OrderNumber,
			OrderNo:                   m.OrderNo,
			TransferredTrackingNumber: m.TransferredTrackingNumber,
			ReceiverZipCode:           formattedZipCode,
			ReceiverName:              m.ReceiverName,
			ReceiverAddress:           m.ReceiverAddress,
			ReceiverPhone:             formattedPhone,
			CreateTime:                util.NewPointerFormattedTime(&m.CreateTime),
			PickUpTime:                m.PickUpTime,
			ShipmentTime:              m.ShipmentTime,
			DeliveredTime:             m.DeliveredTime,
			UpdateTime:                util.NewPointerFormattedTime(&m.UpdateTime),
			Status:                    m.Status,
			UserId:                    m.UserID,
			UserNickname:              userNickname,
			PreRegistrationBatchID:    m.PreRegistrationBatchID,
			MasterBillID:              m.MasterBillID,
			Remark:                    m.Remark,
			Items:                     itemDTOs,
		}

		manifestDTOs = append(manifestDTOs, manifestDTO)
	}

	logger.Debug("Successfully searched manifests", zap.Int64("totalCount", total), zap.Int("returnedCount", len(manifestDTOs)))
	return &SearchManifestsResponse{
		Total: total,
		List:  manifestDTOs,
	}, valueobject.SUCCESS, nil
}

// GetManifestsByExpressNumber 根据快递单号模糊查询运单
func (s *ManifestServiceImpl) GetManifestsByExpressNumber(ginCtx *gin.Context, req *GetManifestsByExpressNumberRequest) (*GetManifestsByExpressNumberResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Getting manifests by express number", zap.String("expressNumber", req.ExpressNumber), zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))

	manifests, total, err := s.manifestRepo.FindManifestsByExpressNumber(ctx, req.ExpressNumber, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to get manifests by express number from repository", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 收集所有用户ID
	userIDs := make([]int64, 0, len(manifests))
	userIDMap := make(map[int64]bool) // 用于去重
	for _, m := range manifests {
		if m.UserID > 0 && !userIDMap[m.UserID] {
			userIDs = append(userIDs, m.UserID)
			userIDMap[m.UserID] = true
		}
	}

	// 批量查询用户信息
	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			logger.Warn("Failed to get users by IDs", zap.Error(err), zap.Int64s("userIDs", userIDs))
			// 这里选择继续，不因为用户信息查询失败而中断整个请求
		} else {
			for _, user := range users {
				userMap[user.ID] = user
			}
		}
	}

	manifestDTOs := make([]*ManifestDTO, 0, len(manifests))
	for _, m := range manifests {
		// 获取用户昵称
		var userNickname string
		if user, exists := userMap[m.UserID]; exists && user != nil {
			userNickname = user.Nickname
		}

		// 将电话号码和邮编格式化为展示格式
		formattedPhone := formatPhoneNumber(m.ReceiverPhone)
		formattedZipCode := s.zipCodeService.FormatZipCode(m.ReceiverZipCode)

		manifestDTOs = append(manifestDTOs, &ManifestDTO{
			ID:                        m.ID,
			ExpressNumber:             m.ExpressNumber,
			OrderNumber:               m.OrderNumber,
			OrderNo:                   m.OrderNo,
			TransferredTrackingNumber: m.TransferredTrackingNumber,
			ReceiverZipCode:           formattedZipCode,
			ReceiverName:              m.ReceiverName,
			ReceiverAddress:           m.ReceiverAddress,
			ReceiverPhone:             formattedPhone,
			CreateTime:                util.NewPointerFormattedTime(&m.CreateTime),
			PickUpTime:                m.PickUpTime,
			ShipmentTime:              m.ShipmentTime,
			DeliveredTime:             m.DeliveredTime,
			UpdateTime:                util.NewPointerFormattedTime(&m.UpdateTime),
			Status:                    m.Status,
			UserId:                    m.UserID,
			UserNickname:              userNickname,
			PreRegistrationBatchID:    m.PreRegistrationBatchID,
			MasterBillID:              m.MasterBillID,
			Remark:                    m.Remark,
			Items:                     make([]*ManifestItemDTO, 0, len(m.Items)),
		})

		// 转换物品列表
		for _, item := range m.Items {
			manifestDTOs[len(manifestDTOs)-1].Items = append(manifestDTOs[len(manifestDTOs)-1].Items, &ManifestItemDTO{
				Name:     item.Name,
				Weight:   item.Weight,
				Quantity: item.Quantity,
				Price:    item.Price,
				Value:    item.Value,
			})
		}
	}

	logger.Debug("Successfully got manifests by express number", zap.Int64("totalCount", total), zap.Int("returnedCount", len(manifestDTOs)))
	return &GetManifestsByExpressNumberResponse{
		Total: total,
		List:  manifestDTOs,
	}, valueobject.SUCCESS, nil
}

// CreateOrder 外部系统创建订单
func (s *ManifestServiceImpl) CreateOrder(ginCtx *gin.Context, req *CreateOrderRequest) (*CreateOrderResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	logger.Debug("Creating order", zap.String("merchantOrderNumber", req.MerchantOrderNumber), zap.Int64("shipmentTypeId", req.ShipmentTypeID))

	// 0. 获取当前用户信息
	claimsData, exists := ginCtx.Get(util.ClaimsContextKey)
	if !exists {
		logger.Error("No user claims found in context")
		return nil, valueobject.ERROR_UNAUTHORIZED, errors.New("用户未登录")
	}

	userID := claimsData.(*util.Claims).UserID
	logger.Debug("Current user", zap.Int64("userId", userID))

	// 1. 校验收件人电话号码
	normalizedPhone := normalizePhoneNumber(req.ReceiverPhone)
	if err := validateJapanesePhoneNumber(normalizedPhone); err != nil {
		logger.Warn("Invalid Japanese phone number", zap.String("phone", req.ReceiverPhone), zap.String("normalized", normalizedPhone), zap.Error(err))
		return nil, valueobject.ERROR_MANIFEST_PHONE_FORMAT_INVALID, fmt.Errorf("电话号码格式错误: %v", err)
	}

	// 2. 校验收件人邮编
	normalizedZipCode := s.zipCodeService.NormalizeZipCode(req.ReceiverZipCode)
	if !s.zipCodeService.IsValidFormat(req.ReceiverZipCode) {
		logger.Warn("Invalid Japanese ZIP code format", zap.String("zipCode", req.ReceiverZipCode), zap.String("normalized", normalizedZipCode))
		return nil, valueobject.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID, errors.New("邮编格式错误，请输入7位数字或XXX-XXXX格式")
	}
	if !s.zipCodeService.IsExist(normalizedZipCode) {
		logger.Warn("Japanese ZIP code does not exist", zap.String("zipCode", req.ReceiverZipCode), zap.String("normalized", normalizedZipCode))
		return nil, valueobject.ERROR_MANIFEST_ZIPCODE_NOT_EXIST, errors.New("该邮编不存在于日本邮编库中，请核对后重新输入")
	}

	// 3. 检查订单号是否已存在
	existingManifest, err := s.manifestRepo.GetManifestByOrderNumber(ctx, req.MerchantOrderNumber)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error("Failed to check existing order number", zap.Error(err), zap.String("merchantOrderNumber", req.MerchantOrderNumber))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	var isUpdate bool = false
	var manifestID int64
	var trackingNumber *entity.TrackingNumberPool
	var systemOrderNo string

	if existingManifest != nil {
		// 订单号已存在，检查状态
		if existingManifest.Status == 1 {
			// 状态为预报（1），允许更新
			isUpdate = true
			manifestID = existingManifest.ID
			logger.Info("Order number exists with forecast status, will update",
				zap.String("merchantOrderNumber", req.MerchantOrderNumber),
				zap.Int64("existingManifestId", manifestID),
				zap.Int("status", existingManifest.Status))
		} else {
			// 状态不是预报，不允许更新
			statusText := "未知状态"
			switch existingManifest.Status {
			case 2:
				statusText = "已揽件"
			case 3:
				statusText = "已发货"
			case 4:
				statusText = "已签收"
			default:
				statusText = fmt.Sprintf("状态%d", existingManifest.Status)
			}

			logger.Warn("Order number exists but cannot be updated",
				zap.String("merchantOrderNumber", req.MerchantOrderNumber),
				zap.Int("status", existingManifest.Status),
				zap.String("statusText", statusText))
			return nil, valueobject.ERROR_MANIFEST_ORDER_CANNOT_UPDATE,
				fmt.Errorf("订单号 %s 已存在且%s，无法更新", req.MerchantOrderNumber, statusText)
		}
	}

	// 4. 如果是新建订单，才需要分配运单号
	if !isUpdate {
		// 根据货物类型从tracking_number_channels获取对应的channel (location_id固定为1)
		channel, err := s.trackingNumberRepo.GetChannelByShipmentType(ctx, 1, req.ShipmentTypeID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Warn("No tracking number channel found", zap.Int64("locationId", 1), zap.Int64("shipmentTypeId", req.ShipmentTypeID))
				return nil, valueobject.ERROR_TRACKING_CHANNEL_NOT_FOUND, errors.New("未找到匹配的运单号渠道")
			}
			logger.Error("Failed to get tracking number channel", zap.Error(err))
			return nil, valueobject.ERROR_UNKNOWN, err
		}

		// 检查渠道是否启用
		if !channel.IsActive {
			logger.Warn("Tracking number channel is inactive", zap.Int64("channelId", channel.ID))
			return nil, valueobject.ERROR_TRACKING_CHANNEL_INACTIVE, errors.New("运单号渠道未启用")
		}

		// 根据channel.id去tracking_number_pool获取一个单号分配给该订单
		trackingNumber, err = s.trackingNumberRepo.AllocateTrackingNumber(ctx, channel.ID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Warn("No available tracking numbers", zap.Int64("channelId", channel.ID))
				return nil, valueobject.ERROR_INSUFFICIENT_TRACKING_NUMBERS, errors.New("所选渠道可用单号不足")
			}
			logger.Error("Failed to allocate tracking number", zap.Error(err))
			return nil, valueobject.ERROR_TRACKING_NUMBER_ALLOCATION_FAILED, err
		}

		// 生成系统订单号
		systemOrderNo, err = generateSystemOrderNumber(req.ShipmentTypeID, trackingNumber.TrackingNumber)
		if err != nil {
			logger.Error("Failed to generate system order number", zap.Error(err))
			return nil, valueobject.ERROR_UNKNOWN, err
		}
	}

	// 5. 计算偏远费（如果不是投函货物且为冲绳县邮编）
	var remoteAreaSurcharge float64 = 0
	if !isPostBoxShipmentType(req.ShipmentTypeID) && s.isOkinawaZipCode(normalizedZipCode) {
		remoteAreaSurcharge = 100.0
		logger.Debug("Applied Okinawa remote area surcharge", zap.Float64("amount", remoteAreaSurcharge))
	}

	// 6. 创建或更新运单记录
	err = s.manifestRepo.WithTransaction(ctx, func(ctx context.Context) error {
		if isUpdate {
			// 更新现有运单（保持原有运单号和系统订单号）
			existingManifest.ReceiverName = req.ReceiverName
			existingManifest.ReceiverPhone = normalizedPhone
			existingManifest.ReceiverZipCode = normalizedZipCode
			existingManifest.ReceiverAddress = req.ReceiverAddress
			existingManifest.RemoteAreaSurcharge = remoteAreaSurcharge
			existingManifest.UpdateTime = time.Now()

			// 更新运单基本信息
			if err := s.manifestRepo.UpdateManifest(ctx, existingManifest); err != nil {
				return err
			}

			// 删除原有物品并创建新物品
			items := make([]*entity.ManifestItem, 0, len(req.Items))
			for _, itemReq := range req.Items {
				item := &entity.ManifestItem{
					ManifestID: manifestID,
					Name:       itemReq.Name,
					Quantity:   itemReq.Quantity,
					Price:      1000,
					Value:      1000,
				}
				items = append(items, item)
			}

			if err := s.manifestRepo.UpdateManifestItems(ctx, manifestID, items); err != nil {
				return err
			}
		} else {
			// 创建新运单
			manifest := &entity.Manifest{
				ExpressNumber:       trackingNumber.TrackingNumber,
				OrderNumber:         req.MerchantOrderNumber,
				OrderNo:             systemOrderNo,
				ReceiverName:        req.ReceiverName,
				ReceiverPhone:       normalizedPhone,
				ReceiverZipCode:     normalizedZipCode,
				ReceiverAddress:     req.ReceiverAddress,
				UserID:              userID,
				Status:              1, // 预报状态
				RemoteAreaSurcharge: remoteAreaSurcharge,
				CreateTime:          time.Now(),
				UpdateTime:          time.Now(),
			}

			// 创建运单
			createdManifest, err := s.manifestRepo.CreateManifest(ctx, manifest)
			if err != nil {
				return err
			}
			manifestID = createdManifest.ID

			// 创建物品记录
			for _, itemReq := range req.Items {
				now := time.Now()
				item := &entity.ManifestItem{
					ManifestID: manifestID,
					Name:       itemReq.Name,
					Quantity:   itemReq.Quantity,
					Price:      1000,
					Value:      1000,
					CreateTime: util.NewPointerFormattedTime(&now),
					UpdateTime: util.NewPointerFormattedTime(&now),
				}

				if err := s.manifestRepo.CreateManifestItem(ctx, item); err != nil {
					return err
				}
			}
		}

		return nil
	})

	if err != nil {
		logger.Error("Failed to create manifest and items", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}

	// 获取最终的运单号和系统订单号
	var finalExpressNumber, finalSystemOrderNo string
	if isUpdate {
		// 更新时使用现有的运单号和系统订单号
		finalExpressNumber = existingManifest.ExpressNumber
		finalSystemOrderNo = existingManifest.OrderNo
		logger.Info("Successfully updated order",
			zap.Int64("manifestId", manifestID),
			zap.String("trackingNumber", finalExpressNumber),
			zap.String("merchantOrderNumber", req.MerchantOrderNumber),
			zap.String("systemOrderNo", finalSystemOrderNo),
			zap.Int64("userId", userID))
	} else {
		// 创建时使用新分配的运单号和系统订单号
		finalExpressNumber = trackingNumber.TrackingNumber
		finalSystemOrderNo = systemOrderNo
		logger.Info("Successfully created order",
			zap.Int64("manifestId", manifestID),
			zap.String("trackingNumber", finalExpressNumber),
			zap.String("merchantOrderNumber", req.MerchantOrderNumber),
			zap.String("systemOrderNo", finalSystemOrderNo),
			zap.Int64("userId", userID))
	}

	// 9. 返回创建结果
	return &CreateOrderResponse{
		TrackingNumber:      finalExpressNumber,
		MerchantOrderNumber: req.MerchantOrderNumber,
		SystemOrderNo:       finalSystemOrderNo,
	}, valueobject.SUCCESS, nil
}

// GenerateLabels 生成PDF面单
func (s *ManifestServiceImpl) GenerateLabels(ginCtx *gin.Context, req *GenerateLabelsRequest) ([]byte, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()

	// 1. 根据运单号批量查询运单信息
	manifests, err := s.manifestRepo.GetManifestsByTrackingNumbers(ctx, req.TrackingNumbers)
	if err != nil {
		logger.Error("Failed to get manifests by tracking numbers", zap.Error(err))
		return nil, errors.New("查询运单信息失败")
	}
	
	if len(manifests) == 0 {
		return nil, errors.New("未找到任何有效的运单信息")
	}

	// 2. 收集用户ID并批量查询用户信息
	userIDs := make([]int64, 0, len(manifests))
	userIDMap := make(map[int64]bool)
	for _, m := range manifests {
		if m.UserID > 0 && !userIDMap[m.UserID] {
			userIDs = append(userIDs, m.UserID)
			userIDMap[m.UserID] = true
		}
	}

	userMap := make(map[int64]*entity.User)
	if len(userIDs) > 0 {
		users, err := s.userRepo.FindByIDs(ctx, userIDs)
		if err != nil {
			logger.Warn("Failed to get users by IDs for labels", zap.Error(err))
			// 不中断流程，只是可能没有用户名
		} else {
			for _, u := range users {
				userMap[u.ID] = u
			}
		}
	}

	// 3. 调用PDF服务生成面单
	pdfBytes, err := s.pdfService.GenerateLabels(manifests, userMap)
	if err != nil {
		logger.Error("Failed to generate PDF labels", zap.Error(err))
		return nil, errors.New("生成PDF面单失败")
	}

	return pdfBytes, nil
}
