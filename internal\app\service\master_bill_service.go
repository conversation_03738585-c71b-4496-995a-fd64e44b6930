package service

import (
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ListMasterBillsRequest 获取主提单列表请求
type ListMasterBillsRequest struct {
	Keyword  string `form:"keyword"`                        // 关键字(提单号)
	Page     int    `form:"page,default=1" binding:"min=1"` // 页码
	PageSize int    `form:"pageSize,default=20" binding:"min=1,max=100"` // 每页数量
}

// MasterBillOption 主提单选项，用于下拉框
type MasterBillOption struct {
	ID               int64  `json:"id"`               // 主提单ID
	MasterBillNumber string `json:"masterBillNumber"` // 提单号/航班号
}

// ListMasterBillsResponse 获取主提单列表响应
type ListMasterBillsResponse struct {
	Total int64             `json:"total"` // 总数
	List  []MasterBillOption `json:"list"`  // 主提单列表
}

// MasterBillService 主提单服务接口
type MasterBillService interface {
	// ListMasterBills 获取主提单列表，用于前端下拉框
	ListMasterBills(ginCtx *gin.Context, req *ListMasterBillsRequest) (*ListMasterBillsResponse, int, error)
}

// MasterBillServiceImpl 主提单服务实现
type MasterBillServiceImpl struct {
	masterBillRepo repository.MasterBillRepository
}

// NewMasterBillService 创建主提单服务
func NewMasterBillService(masterBillRepo repository.MasterBillRepository) MasterBillService {
	return &MasterBillServiceImpl{
		masterBillRepo: masterBillRepo,
	}
}

// ListMasterBills 获取主提单列表
func (s *MasterBillServiceImpl) ListMasterBills(ginCtx *gin.Context, req *ListMasterBillsRequest) (*ListMasterBillsResponse, int, error) {
	logger := ginCtx.MustGet(util.LoggerContextKey).(*zap.Logger)
	ctx := ginCtx.Request.Context()
	
	logger.Debug("Getting master bill list", zap.String("keyword", req.Keyword), zap.Int("page", req.Page), zap.Int("pageSize", req.PageSize))
	
	// 查询主提单列表
	masterBills, total, err := s.masterBillRepo.ListForOptions(ctx, req.Keyword, req.Page, req.PageSize)
	if err != nil {
		logger.Error("Failed to get master bill list", zap.Error(err))
		return nil, valueobject.ERROR_UNKNOWN, err
	}
	
	// 转换为前端需要的格式
	masterBillOptions := make([]MasterBillOption, 0, len(masterBills))
	for _, masterBill := range masterBills {
		masterBillOptions = append(masterBillOptions, MasterBillOption{
			ID:               masterBill.ID,
			MasterBillNumber: masterBill.MasterBillNumber,
		})
	}
	
	logger.Debug("Successfully got master bill list", zap.Int64("total", total), zap.Int("listSize", len(masterBillOptions)))
	return &ListMasterBillsResponse{
		Total: total,
		List:  masterBillOptions,
	}, valueobject.SUCCESS, nil
} 