# 财务模块 - 批量财务调整记录创建功能修改说明

## 修改概述

新增批量创建财务调整记录功能，支持根据订单号列表和调整类型组合批量创建改派、退回、销毁三种类型的财务调整记录。该功能大幅提升了财务调整记录的录入效率，特别适用于需要对多个订单进行相同调整操作的业务场景。

## 功能特点

### 1. **批量处理能力**

- 支持一次性处理最多 100 个订单号
- 支持改派、退回、销毁三种调整类型的组合选择
- 每个订单号可以同时创建多种类型的调整记录

### 2. **灵活的参数配置**

- 每种调整类型可以单独设置金额，支持精细化的费用控制
- 统一的生效日期设置
- 可选的调整描述信息
- 支持多种货币类型
- 客户账户 ID 自动从运单信息中获取，确保数据一致性
- 支持标准的调整类型常量（REASSIGNMENT、RETURN、DESTRUCTION 等）

### 3. **详细的结果反馈**

- 提供成功/失败的详细统计
- 记录每个失败项的具体错误信息
- 生成按调整类型分组的汇总报告

### 4. **容错处理机制**

- 单个订单号查询失败不影响其他订单的处理
- 部分成功的情况下仍返回成功状态
- 详细记录每个失败项的错误原因

## 技术实现

### 1. 新增 DTO 定义

#### BatchCreateAdjustmentItem

```go
type BatchCreateAdjustmentItem struct {
    AdjustmentType string  `json:"adjustmentType" binding:"required"` // 调整类型：REASSIGNMENT、RETURN、DESTRUCTION等
    Amount         float64 `json:"amount" binding:"required"`         // 该类型的调整金额
}
```

#### BatchCreateFinancialAdjustmentDTO

```go
type BatchCreateFinancialAdjustmentDTO struct {
    TrackingNumbers   []string                    `json:"trackingNumbers" binding:"required,min=1,max=100"`
    AdjustmentItems   []BatchCreateAdjustmentItem `json:"adjustmentItems" binding:"required,min=1"`
    EffectiveDate     time.Time                   `json:"effectiveDate" binding:"required"`
    Description       string                      `json:"description"`
    Currency          string                      `json:"currency" binding:"required"`
    CreatorID         int64                       `json:"creatorId"`
}
```

#### BatchCreateFinancialAdjustmentResponse

```go
type BatchCreateFinancialAdjustmentResponse struct {
    SuccessCount      int                               `json:"successCount"`
    FailureCount      int                               `json:"failureCount"`
    TotalCount        int                               `json:"totalCount"`
    SuccessItems      []*FinancialAdjustmentItemResponse `json:"successItems"`
    FailureItems      []*BatchCreateFailureItem         `json:"failureItems"`
    Summary           *BatchCreateSummary               `json:"summary"`
}
```

#### BatchCreateFailureItem

```go
type BatchCreateFailureItem struct {
    TrackingNumber  string `json:"trackingNumber"`
    AdjustmentType  string `json:"adjustmentType"`
    ErrorCode       int    `json:"errorCode"`
    ErrorMessage    string `json:"errorMessage"`
}
```

#### BatchCreateSummary

```go
type BatchCreateSummary struct {
    TotalAmount       float64 `json:"totalAmount"`
    ReassignmentCount int     `json:"reassignmentCount"`
    ReturnCount       int     `json:"returnCount"`
    DestructionCount  int     `json:"destructionCount"`
}
```

### 2. 服务层实现

#### 接口扩展

在`FinancialAdjustmentService`接口中新增：

```go
// BatchCreateFinancialAdjustments 批量创建财务调整记录
BatchCreateFinancialAdjustments(ctx context.Context, req *BatchCreateFinancialAdjustmentDTO) (*BatchCreateFinancialAdjustmentResponse, int, error)
```

#### 核心实现逻辑

```go
func (s *FinancialAdjustmentServiceImpl) BatchCreateFinancialAdjustments(ctx context.Context, req *BatchCreateFinancialAdjustmentDTO) (*BatchCreateFinancialAdjustmentResponse, int, error) {
    // 1. 验证调整类型
    // 2. 根据订单号查询运单信息
    // 3. 批量创建调整记录
    // 4. 统计处理结果
    // 5. 返回详细响应
}
```

#### 辅助方法

```go
func (s *FinancialAdjustmentServiceImpl) createSingleAdjustment(ctx context.Context, manifest *entity.Manifest, adjType string, req *BatchCreateFinancialAdjustmentDTO) (*FinancialAdjustmentItemResponse, error) {
    // 创建单个调整记录的具体实现
}
```

### 3. 控制器层实现

#### Handler 方法

```go
// BatchCreateFinancialAdjustments 批量创建财务调整记录
func (h *FinancialAdjustmentHandler) BatchCreateFinancialAdjustments(c *gin.Context) {
    // 1. 参数绑定和验证
    // 2. 用户认证检查
    // 3. 日期格式解析
    // 4. 调用服务层
    // 5. 返回响应结果
}
```

#### 请求体定义

```go
type BatchCreateAdjustmentItemRequest struct {
    AdjustmentType string  `json:"adjustmentType" binding:"required"` // 调整类型：REASSIGNMENT、RETURN、DESTRUCTION等
    Amount         float64 `json:"amount" binding:"required"`         // 该类型的调整金额
}

type BatchCreateFinancialAdjustmentRequest struct {
    TrackingNumbers   []string                           `json:"trackingNumbers" binding:"required,min=1,max=100"`
    AdjustmentItems   []BatchCreateAdjustmentItemRequest `json:"adjustmentItems" binding:"required,min=1"`
    EffectiveDateStr  string                             `json:"effectiveDate" binding:"required"`
    Description       string                             `json:"description"`
    Currency          string                             `json:"currency" binding:"required"`
}
```

### 4. 路由配置

在`internal/router/router.go`中新增路由：

```go
financialAdjustmentRoutes.POST("/batch", financialAdjustmentHandler.BatchCreateFinancialAdjustments)
```

## 处理流程详解

### 1. 参数验证阶段

- 验证订单号列表（1-100 个）
- 验证调整类型列表（reassignment、return、destruction）
- 验证金额、日期、货币等必填参数
- 检查用户认证状态

### 2. 订单号查询阶段

```go
// 使用现有的模糊查询方法逐个查询
for _, trackingNumber := range req.TrackingNumbers {
    manifests, _, err := s.manifestRepo.FindManifestsByExpressNumber(ctx, trackingNumber, 1, 10)
    // 查找精确匹配的运单
    for _, manifest := range manifests {
        if manifest.ExpressNumber == trackingNumber ||
           (manifest.TransferredTrackingNumber != "" && manifest.TransferredTrackingNumber == trackingNumber) {
            manifestMap[trackingNumber] = manifest
            break
        }
    }
}
```

### 3. 批量创建阶段

```go
// 为每个订单号的每种调整类型创建记录
for _, trackingNumber := range req.TrackingNumbers {
    for _, adjType := range req.AdjustmentTypes {
        adjustmentItem, err := s.createSingleAdjustment(ctx, manifest, adjType, req)
        // 处理成功/失败结果
    }
}
```

**重要说明**：每条调整记录的 `customerAccountID` 从对应运单的 `UserID` 字段自动获取，这样确保了：

- 数据的一致性和准确性
- 避免了手动指定客户账户 ID 可能带来的错误
- 支持同一批次中包含不同客户的订单（虽然在实际业务中较少见）

### 4. 结果统计阶段

- 统计成功创建的记录数
- 统计失败的记录数
- 按调整类型分组统计
- 计算总调整金额

## 业务逻辑规则

### 1. 创建规则

- **数量计算**: 订单号数量 × 调整项数量 = 总记录数
- **独立处理**: 每条记录的创建是独立的，单条失败不影响其他记录
- **灵活金额**: 每种调整类型可以设置不同的金额，支持精细化费用控制
- **客户账户 ID**: 自动从每个运单的 UserID 字段获取，确保数据的准确性和一致性
- **调整类型**: 使用标准的调整类型常量（REASSIGNMENT、RETURN、DESTRUCTION、COMPENSATION、FEE、REBATE、OTHER）

### 2. 附加详情设置

根据调整类型自动设置：

- **改派类型**: `{"description": "用户描述"}`
- **退回类型**: `{"return_reason": "用户描述"}`
- **销毁类型**: `{"destruction_reason": "用户描述"}`

### 3. 错误处理

- **订单号不存在**: 记录失败项，继续处理其他订单
- **数据库保存失败**: 记录失败项，提供详细错误信息
- **参数验证失败**: 直接返回 400 错误，停止处理

## 性能优化

### 1. 查询优化

- 使用 Map 缓存订单号到运单的映射关系
- 避免重复查询相同的订单号
- 利用现有的索引进行快速查询

### 2. 批量处理限制

- 最多支持 100 个订单号，防止超大批量操作
- 合理的内存使用，避免一次性加载过多数据

### 3. 数据库操作

- 每条记录独立保存，避免长事务
- 利用现有的`Save`方法，保证数据一致性

## 使用场景

### 1. 批量改派处理

当多个订单需要改派到新地址时，可以一次性创建改派调整记录：

```json
{
  "trackingNumbers": ["ZM001", "ZM002", "ZM003"],
  "adjustmentItems": [
    {
      "adjustmentType": "REASSIGNMENT",
      "amount": 15.0
    }
  ],
  "effectiveDate": "2024-01-15",
  "description": "批量改派到新仓库",
  "currency": "CNY"
}
```

### 2. 组合调整处理

对某些订单同时进行改派和退回处理，并设置不同的金额：

```json
{
  "trackingNumbers": ["ZM001", "ZM002"],
  "adjustmentItems": [
    {
      "adjustmentType": "REASSIGNMENT",
      "amount": 20.0
    },
    {
      "adjustmentType": "RETURN",
      "amount": 12.0
    }
  ],
  "effectiveDate": "2024-01-15",
  "description": "改派失败后退回处理",
  "currency": "CNY"
}
```

### 3. 批量销毁处理

对多个问题订单进行销毁处理：

```json
{
  "trackingNumbers": ["ZM001", "ZM002", "ZM003", "ZM004"],
  "adjustmentItems": [
    {
      "adjustmentType": "DESTRUCTION",
      "amount": 50.0
    }
  ],
  "effectiveDate": "2024-01-15",
  "description": "海关扣留销毁处理",
  "currency": "CNY"
}
```

### 4. 多类型费用调整

对订单进行多种类型的费用调整，每种类型设置不同金额：

```json
{
  "trackingNumbers": ["ZM001"],
  "adjustmentItems": [
    {
      "adjustmentType": "FEE",
      "amount": 10.0
    },
    {
      "adjustmentType": "REBATE",
      "amount": -5.0
    },
    {
      "adjustmentType": "COMPENSATION",
      "amount": -20.0
    }
  ],
  "effectiveDate": "2024-01-15",
  "description": "综合费用调整",
  "currency": "CNY"
}
```

## 接口兼容性

### 1. 向后兼容

- 不影响现有的单个创建接口
- 保持现有数据结构和业务逻辑不变
- 复用现有的仓储层方法

### 2. 数据格式一致

- 创建的记录格式与单个创建接口完全一致
- 响应中的成功项使用相同的数据结构
- 错误码和错误消息保持统一

## 测试建议

### 1. 功能测试

- 测试各种调整类型的组合
- 测试边界条件（1 个订单号、100 个订单号）
- 测试部分成功的场景
- 测试全部失败的场景

### 2. 性能测试

- 测试最大批量（100 个订单号）的处理时间
- 测试并发调用的性能表现
- 测试数据库连接池的使用情况

### 3. 错误处理测试

- 测试无效订单号的处理
- 测试数据库连接失败的恢复
- 测试参数验证的各种错误情况

## 部署注意事项

### 1. 数据库性能

- 确保运单表的索引覆盖查询需求
- 监控批量操作对数据库的影响
- 考虑在低峰期进行大批量操作

### 2. 监控告警

- 监控批量操作的成功率
- 设置处理时间的告警阈值
- 记录详细的操作日志

### 3. 用户培训

- 提供操作手册和最佳实践
- 建议合理的批量大小
- 说明错误处理和重试机制

## 文件修改清单

### 新增文件

1. `docs/财务模块_批量财务调整记录创建_API文档.md` - API 接口文档
2. `docs/财务模块_批量财务调整记录创建_修改说明.md` - 功能修改说明

### 修改文件

1. `internal/app/service/financial_adjustment_service.go`

   - 新增批量创建相关的 DTO 定义
   - 扩展 FinancialAdjustmentService 接口
   - 实现 BatchCreateFinancialAdjustments 方法
   - 新增 createSingleAdjustment 辅助方法

2. `internal/handler/financial_adjustment_handler.go`

   - 新增 BatchCreateFinancialAdjustmentRequest 请求体定义
   - 实现 BatchCreateFinancialAdjustments 处理方法

3. `internal/router/router.go`

   - 新增批量创建接口的路由配置

4. `examples/billing_processor_usage.go`
   - 修复 linter 错误（使用 typed nil 声明）

## 版本信息

- **功能版本**: v1.0.0
- **创建日期**: 2024-01-15
- **修改类型**: 新增功能 - 批量财务调整记录创建
- **影响范围**: 财务调整模块
- **向后兼容**: 是

## 相关文档

- [财务模块\_批量财务调整记录创建\_API 文档.md](./财务模块_批量财务调整记录创建_API文档.md)
- [现有财务调整记录相关文档]

---

**注意**: 该功能已完成开发，建议在测试环境充分验证后再部署到生产环境。
