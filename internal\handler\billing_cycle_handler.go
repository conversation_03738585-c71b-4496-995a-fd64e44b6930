package handler

import (
	"net/http"
	"strconv"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// BillingCycleHandler 账期批次处理器
type BillingCycleHandler struct {
	billingCycleService service.BillingCycleService
}

// NewBillingCycleHandler 创建账期批次处理器
func NewBillingCycleHandler(billingCycleService service.BillingCycleService) *BillingCycleHandler {
	return &BillingCycleHandler{
		billingCycleService: billingCycleService,
	}
}

// CreateBillingCycle 创建账期批次
// @Summary 创建账期批次
// @Description 创建新的账期批次记录
// @Tags 账期批次管理
// @Accept json
// @Produce json
// @Param request body service.CreateBillingCycleRequest true "创建账期批次请求"
// @Success 200 {object} util.Response{data=service.CreateBillingCycleResponse} "创建成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/billing/cycles [post]
func (h *BillingCycleHandler) CreateBillingCycle(c *gin.Context) {
	logger := util.GetLoggerFromContext(c.Request.Context())

	var req service.CreateBillingCycleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		if logger != nil {
			logger.Warn("Invalid request parameters", zap.Error(err))
		}
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "请求参数格式错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 从上下文中获取当前用户ID作为操作员ID
	if userID, exists := c.Get("userID"); exists {
		if uid, ok := userID.(int64); ok {
			req.GeneratedByUserID = &uid
		}
	}

	response, code, err := h.billingCycleService.CreateBillingCycle(c.Request.Context(), &req)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to create billing cycle", zap.Error(err))
		}
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, response)
}

// ListBillingCycles 分页查询账期批次列表
// @Summary 分页查询账期批次列表
// @Description 根据条件分页查询账期批次列表
// @Tags 账期批次管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Param cycleYear query int false "账期年份"
// @Param cycleMonth query int false "账期月份"
// @Param status query string false "账期批次状态"
// @Param cycleName query string false "账期名称（模糊查询）"
// @Param generatedByUserId query int false "生成操作员ID"
// @Success 200 {object} util.Response{data=service.ListBillingCyclesResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/billing/cycles [get]
func (h *BillingCycleHandler) ListBillingCycles(c *gin.Context) {
	logger := util.GetLoggerFromContext(c.Request.Context())

	var req service.ListBillingCyclesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		if logger != nil {
			logger.Warn("Invalid query parameters", zap.Error(err))
		}
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "查询参数格式错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	response, code, err := h.billingCycleService.ListBillingCycles(c.Request.Context(), &req)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to list billing cycles", zap.Error(err))
		}
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, response)
}

// GetBillingCycleDetail 获取账期批次详情
// @Summary 获取账期批次详情
// @Description 根据账期批次ID获取详细信息
// @Tags 账期批次管理
// @Accept json
// @Produce json
// @Param billingCycleId path int true "账期批次ID"
// @Success 200 {object} util.Response{data=service.GetBillingCycleDetailResponse} "查询成功"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "账期批次不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/billing/cycles/{billingCycleId} [get]
func (h *BillingCycleHandler) GetBillingCycleDetail(c *gin.Context) {
	logger := util.GetLoggerFromContext(c.Request.Context())

	// 解析路径参数
	billingCycleIDStr := c.Param("billingCycleId")
	billingCycleID, err := strconv.ParseInt(billingCycleIDStr, 10, 64)
	if err != nil {
		if logger != nil {
			logger.Warn("Invalid billing cycle ID", zap.String("billingCycleId", billingCycleIDStr), zap.Error(err))
		}
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "账期批次ID格式错误", http.StatusBadRequest)
		return
	}

	req := &service.GetBillingCycleDetailRequest{
		BillingCycleID: billingCycleID,
	}

	response, code, err := h.billingCycleService.GetBillingCycleDetail(c.Request.Context(), req)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to get billing cycle detail", zap.Int64("billingCycleId", billingCycleID), zap.Error(err))
		}
		
		// 根据错误码返回相应的HTTP状态码
		httpStatus := http.StatusInternalServerError
		if code == valueobject.ERROR_RESOURCE_NOT_FOUND {
			httpStatus = http.StatusNotFound
		} else if code == valueobject.ERROR_INVALID_PARAMETER {
			httpStatus = http.StatusBadRequest
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, response)
} 