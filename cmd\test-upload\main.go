package main

import (
	"fmt"
	"log"
	"zebra-hub-system/internal/config"
	"zebra-hub-system/internal/domain/service/impl"
	"zebra-hub-system/internal/handler"
	"zebra-hub-system/internal/middleware"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("./configs/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化 Logger
	util.InitLogger(cfg.Server.Mode)
	defer util.SyncLogger()
	
	// 初始化文件上传服务
	fileUploadService, err := impl.NewFileUploadService(
		cfg.OSS.AccessKeyID,
		cfg.OSS.AccessKeySecret,
		cfg.OSS.Endpoint,
		cfg.OSS.BucketName,
		util.Logger,
	)
	if err != nil {
		util.Logger.Fatal("Failed to initialize file upload service", zap.Error(err))
	}

	// 初始化处理器
	fileUploadHandler := handler.NewFileUploadHandler(fileUploadService, util.Logger)

	// 设置路由
	r := gin.Default()
	
	// 全局中间件
	r.Use(middleware.RequestID())
	r.Use(middleware.CORS())

	// 公共路由组，不需要认证（用于测试）
	publicGroup := r.Group("/api/v1")
	{
		// 文件上传相关路由（测试时不需要认证）
		uploadRoutes := publicGroup.Group("/upload")
		{
			uploadRoutes.POST("/image", fileUploadHandler.UploadImage)   // 上传图片
			uploadRoutes.DELETE("/image", fileUploadHandler.DeleteImage) // 删除图片
			uploadRoutes.GET("/info", fileUploadHandler.GetUploadInfo)   // 获取上传配置信息
		}
	}

	// 启动服务
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	util.Logger.Info("Test upload server is running", zap.String("address", addr))
	if err := r.Run(addr); err != nil {
		util.Logger.Fatal("Failed to start server", zap.Error(err))
	}
} 