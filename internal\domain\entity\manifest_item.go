package entity

import (
	"zebra-hub-system/internal/util"
)

// ManifestItem 运单物品实体
type ManifestItem struct {
	ID          int64                    `json:"id"`
	ManifestID  int64                    `json:"manifestId"` // 运单ID
	Name        string                   `json:"name"`        // 物品名称
	NameEn      string                   `json:"nameEn"`      // 物品英文名称
	Weight      float64                  `json:"weight"`      // 重量
	Quantity    int                      `json:"quantity"`    // 数量
	Price       float64                  `json:"price"`       // 单价
	Value       float64                  `json:"value"`       // 申报价值 (Price * Quantity)
	CreateTime  util.PointerFormattedTime `json:"createTime"`  // 创建时间
	UpdateTime  util.PointerFormattedTime `json:"updateTime"`  // 更新时间
} 