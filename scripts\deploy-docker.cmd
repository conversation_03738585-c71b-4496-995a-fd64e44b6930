@echo off
REM Docker部署脚本 (Windows版本)
REM 用法: scripts\deploy-docker.cmd [simple|full] [up|down|restart]

setlocal enabledelayedexpansion

REM 设置默认参数
set COMPOSE_FILE=docker-compose.yml
set ACTION=up

REM 解析参数
:parse_args
if "%1"=="" goto :start_deploy
if "%1"=="simple" (
    set COMPOSE_FILE=docker-compose.simple.yml
    echo [INFO] 使用简化版docker-compose配置（无数据库和消息队列）
) else if "%1"=="full" (
    set COMPOSE_FILE=docker-compose.yml
    echo [INFO] 使用完整版docker-compose配置（包含数据库和消息队列）
) else if "%1"=="up" (
    set ACTION=up
) else if "%1"=="down" (
    set ACTION=down
) else if "%1"=="restart" (
    set ACTION=restart
) else (
    echo [ERROR] 未知参数: %1
    echo 用法: %0 [simple^|full] [up^|down^|restart]
    echo   simple  - 使用简化版配置（仅应用服务）
    echo   full    - 使用完整版配置（包含数据库和消息队列）
    echo   up      - 启动服务（默认）
    echo   down    - 停止服务
    echo   restart - 重启服务
    exit /b 1
)
shift
goto :parse_args

:start_deploy
echo [INFO] 使用配置文件: %COMPOSE_FILE%
echo [INFO] 执行操作: %ACTION%

REM 检查配置文件是否存在
if not exist "%COMPOSE_FILE%" (
    echo [ERROR] 配置文件 %COMPOSE_FILE% 不存在
    exit /b 1
)

REM 检查Docker Compose是否安装
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装
    exit /b 1
)

REM 执行操作
if "%ACTION%"=="up" (
    echo [INFO] 启动服务...
    docker-compose -f "%COMPOSE_FILE%" up -d
    if errorlevel 1 (
        echo [ERROR] 服务启动失败
        exit /b 1
    )
    echo [SUCCESS] 服务启动完成
) else if "%ACTION%"=="down" (
    echo [INFO] 停止服务...
    docker-compose -f "%COMPOSE_FILE%" down
    if errorlevel 1 (
        echo [ERROR] 服务停止失败
        exit /b 1
    )
    echo [SUCCESS] 服务停止完成
) else if "%ACTION%"=="restart" (
    echo [INFO] 重启服务...
    docker-compose -f "%COMPOSE_FILE%" down
    docker-compose -f "%COMPOSE_FILE%" up -d
    if errorlevel 1 (
        echo [ERROR] 服务重启失败
        exit /b 1
    )
    echo [SUCCESS] 服务重启完成
)

REM 显示服务状态
echo [INFO] 当前服务状态：
docker-compose -f "%COMPOSE_FILE%" ps 