@echo off
REM 消费者配置验证脚本

setlocal enabledelayedexpansion

echo ====================================
echo 账单生成消费者配置验证
echo ====================================
echo.

REM 设置工作目录
cd /d "%~dp0\.."

echo [1/5] 验证Go环境...
go version >NUL 2>&1
if !ERRORLEVEL! neq 0 (
    echo ❌ Go环境未安装
    goto error
)
echo ✅ Go环境正常

echo.
echo [2/5] 验证配置文件...
if not exist "configs\config.yaml" (
    echo ❌ 配置文件不存在: configs\config.yaml
    goto error
)

REM 检查RabbitMQ配置
findstr "rabbitmq:" configs\config.yaml >NUL 2>&1
if !ERRORLEVEL! neq 0 (
    echo ❌ 配置文件缺少RabbitMQ配置
    goto error
)
echo ✅ 配置文件结构正常

echo.
echo [3/5] 验证程序构建...
go build -o bin\billing-consumer-validate.exe cmd\billing-consumer\main.go
if !ERRORLEVEL! neq 0 (
    echo ❌ 程序构建失败
    goto error
)
echo ✅ 程序构建成功

echo.
echo [4/5] 验证依赖包...
go mod verify >NUL 2>&1
if !ERRORLEVEL! neq 0 (
    echo ⚠️  依赖包验证警告，但继续验证
) else (
    echo ✅ 依赖包验证正常
)

echo.
echo [5/5] 验证网络连接...

REM 提取配置中的主机和端口
for /f "tokens=2" %%i in ('findstr "host:" configs\config.yaml ^| findstr -v "#" ^| findstr -v "database"') do set RABBITMQ_HOST=%%i
for /f "tokens=2" %%i in ('findstr "port:" configs\config.yaml ^| findstr "567"') do set RABBITMQ_PORT=%%i

if defined RABBITMQ_HOST if defined RABBITMQ_PORT (
    echo 检测RabbitMQ连接: %RABBITMQ_HOST%:%RABBITMQ_PORT%
    
    powershell -Command "try { $socket = New-Object System.Net.Sockets.TcpClient; $socket.Connect('%RABBITMQ_HOST%', %RABBITMQ_PORT%); $socket.Close(); exit 0 } catch { exit 1 }" >NUL 2>&1
    if !ERRORLEVEL! equ 0 (
        echo ✅ RabbitMQ连接正常
    ) else (
        echo ⚠️  RabbitMQ连接失败，请检查网络和服务
    )
) else (
    echo ⚠️  无法解析RabbitMQ配置
)

echo.
echo ====================================
echo ✅ 配置验证完成！
echo ====================================
echo.
echo 消费者可以启动：
echo   方式1: scripts\start-billing-consumer.bat start
echo   方式2: make start-consumer
echo   方式3: bin\billing-consumer.exe
echo.

goto cleanup

:error
echo.
echo ❌ 配置验证失败！
echo 请修复上述问题后重试。
echo.

:cleanup
if exist bin\billing-consumer-validate.exe del bin\billing-consumer-validate.exe 2>NUL
pause 