# 外部系统创建订单接口实现说明

## 概述

已完成外部系统对接的创建订单接口实现，支持自动分配运单号、生成系统订单号、校验收件人信息等完整功能。

## 已实现的功能

### 1. 核心接口

- **路径**: `POST /api/v1/external/orders`
- **认证**: 需要 Bearer Token 认证
- **功能**: 创建运单和物品记录，自动分配运单号

### 2. 业务逻辑

#### 数据校验

- ✅ 收件人电话号码格式校验（日本格式）
- ✅ 收件人邮编格式校验和存在性校验
- ✅ 货物类型对应的运单号渠道校验

#### 系统订单号生成

- ✅ 普通货物/带电货物: `BM + 月日 + 6位随机数` (如: BM1217123456)
- ✅ 投函货物: `SF + 6位随机数` (如: SF123456)

#### 费用计算

- ✅ 冲绳县邮编自动添加 100 元偏远费（非投函货物）
- ✅ 支持 900-906 开头的冲绳县邮编识别

#### 运单号分配

- ✅ 根据货物类型和地点 ID 获取对应渠道
- ✅ 从渠道对应的号池中分配可用运单号
- ✅ 自动标记运单号为已分配状态

#### 数据创建

- ✅ 在事务中创建运单记录和物品记录
- ✅ 设置当前用户为运单所属用户
- ✅ 运单初始状态为待审核（status=0）

### 3. 技术实现

#### 新增实体

- `TrackingNumberChannel`: 运单号渠道实体
- `TrackingNumberPool`: 运单号池实体

#### 新增 Repository

- `TrackingNumberRepository`: 运单号相关仓储接口
- `TrackingNumberRepositoryImpl`: 运单号仓储实现

#### 错误码定义

- `202001`: 运单号渠道不存在
- `202002`: 运单号渠道未启用
- `202005`: 可用单号不足
- `202006`: 运单号分配失败

#### 工具函数

- `isOkinawaZipCode()`: 判断冲绳县邮编
- `isPostBoxShipmentType()`: 判断投函货物类型
- `generateSystemOrderNumber()`: 生成系统订单号
- `generateRandomNumber()`: 生成随机数字

## 文件结构

```
internal/
├── app/service/
│   └── manifest_service.go           # 添加CreateOrder方法和相关结构体
├── handler/
│   └── manifest_handler.go          # 添加CreateOrder handler
├── router/
│   └── router.go                     # 添加/external/orders路由
├── domain/
│   ├── entity/
│   │   ├── tracking_number_channel.go  # 新增
│   │   └── tracking_number_pool.go     # 新增
│   ├── repository/
│   │   └── tracking_number_repository.go  # 新增
│   └── valueobject/
│       └── error_code.go            # 添加tracking number相关错误码
└── adapter/persistence/
    └── tracking_number_repository_impl.go  # 新增
```

## 请求示例

```json
{
  "orderNumber": "MERCHANT_ORDER_20241217001",
  "shipmentTypeId": 1,
  "receiverName": "田中太郎",
  "receiverPhone": "090-1234-5678",
  "receiverZipCode": "100-0001",
  "receiverAddress": "東京都千代田区千代田1-1",
  "items": [
    {
      "name": "手机壳",
      "quantity": 2
    }
  ]
}
```

## 响应示例

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "manifestId": 12345,
    "expressNumber": "JP123456789",
    "orderNumber": "MERCHANT_ORDER_20241217001",
    "systemOrderNo": "BM1217123456"
  }
}
```

## 数据库表结构

### tracking_number_channels

运单号渠道定义表，存储不同承运商、地点、货物类型的组合配置。

### tracking_number_pool

运单号池表，存储预先导入的运单号，支持分配状态管理。

## 待实现功能

由于某些 repository 方法可能尚未实现，建议按以下优先级补充：

1. **高优先级（接口运行必需）**:

   - `ManifestRepository.CreateManifest()`
   - `ManifestRepository.CreateManifestItem()`
   - `ManifestRepository.WithTransaction()`
   - `TrackingNumberRepositoryImpl.GetChannelByShipmentType()`
   - `TrackingNumberRepositoryImpl.AllocateTrackingNumber()`

2. **中优先级（完善功能）**:
   - 运单号导入功能
   - 渠道管理功能
   - 运单号分配批次管理

## 测试建议

1. 使用`test_create_order.md`中的测试用例进行接口测试
2. 验证不同货物类型的系统订单号生成规则
3. 测试冲绳县邮编的偏远费计算
4. 验证各种错误场景的处理

## 注意事项

1. 接口需要认证，确保在请求头中包含有效的 Bearer Token
2. 货物类型 ID 需要在数据库中有对应的渠道配置
3. 运单号池需要预先导入可用的运单号
4. 系统依赖日本邮编库进行邮编校验
