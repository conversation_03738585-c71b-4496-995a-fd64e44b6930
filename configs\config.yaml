server:
  port: 8080
  mode: debug

database:
  host: *************
  port: 3336
  username: root
  password: 123456
  database: zebra_express_hub
  max_idle_conns: 10
  max_open_conns: 100

# Redis配置
redis:
  host: *************
  port: 6379
  password: banma1346797
  db: 0

# 邮编配置
zipcode:
  japan:
    filepath: "data/zipcode/japan_zipcode.csv"

# Google API配置
google_apis:
  translate:
    api_key: "" # API Key方式认证（可选）
    credential_file: "configs/google-translate-key.json" # JSON密钥文件路径（推荐），例如："configs/google-translate-key.json"
    project: "zebra-hub-system" # Google Cloud项目ID（可从密钥文件中获取）
    enabled: true # 设置为true启用翻译功能

# RabbitMQ配置
rabbitmq:
  host: *************
  port: 5673
  virtual-host: /
  username: zebra
  password: banma1346797

# 阿里云OSS配置
oss:
  access_key_id: "LTAI5tSZs1KNc1RTk2LjaJFJ"
  access_key_secret: "******************************"
  endpoint: "https://oss-cn-fuzhou.aliyuncs.com"
  region: "cn-fuzhou"
  bucket_name: "zebra-logistics-files"
