# 财务模块运费模板 JSON 解析实现

## 概述

在财务模块的账单记录中，`applied_freight_templates` 字段以 JSON 格式存储了生成账单时使用的运费模板信息。为了正确读取和展示这些信息，我们实现了完整的 JSON 解析逻辑。

## 数据结构说明

### 数据库存储格式

在数据库中，`applied_freight_templates` 字段存储的 JSON 结构如下：

```json
{
  "generalTemplate": {
    "id": 1,
    "name": "普货标准模板",
    "type": 1,
    "firstWeightPrice": 15.0,
    "firstWeightRange": 0.5,
    "continuedWeightPrice": 8.0,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 6000,
    "threeSidesStart": 60.0,
    "createTime": "2024-01-01 12:00:00",
    "updateTime": "2024-01-01 12:00:00"
  },
  "batteryTemplate": {
    "id": 2,
    "name": "带电货物模板",
    "type": 2,
    "firstWeightPrice": 20.0,
    "firstWeightRange": 0.5,
    "continuedWeightPrice": 12.0,
    "continuedWeightInterval": 0.5,
    "bulkCoefficient": 6000,
    "threeSidesStart": 60.0,
    "createTime": "2024-01-01 12:00:00",
    "updateTime": "2024-01-01 12:00:00"
  },
  "postBoxTemplate": null
}
```

### 实体结构

对应的 Go 实体结构：

```go
// AppliedFreightTemplate 应用的运费模板信息
type AppliedFreightTemplate struct {
    GeneralTemplate *ShippingFeeTemplate `json:"generalTemplate,omitempty"`  // 普货模板
    BatteryTemplate *ShippingFeeTemplate `json:"batteryTemplate,omitempty"`  // 带电货物模板
    PostBoxTemplate *ShippingFeeTemplate `json:"postBoxTemplate,omitempty"`  // 投函货物模板
}

// ShippingFeeTemplate 运费模板实体
type ShippingFeeTemplate struct {
    ID                      int64               `json:"id"`
    Name                    string              `json:"name"`
    FirstWeightPrice        float64             `json:"firstWeightPrice"`
    FirstWeightRange        float64             `json:"firstWeightRange"`
    ContinuedWeightPrice    float64             `json:"continuedWeightPrice"`
    ContinuedWeightInterval float64             `json:"continuedWeightInterval"`
    BulkCoefficient         int                 `json:"bulkCoefficient"`
    ThreeSidesStart         float64             `json:"threeSidesStart"`
    Type                    int                 `json:"type"`
    CreateTime              util.FormattedTime  `json:"createTime"`
    UpdateTime              util.FormattedTime  `json:"updateTime"`
}
```

## 实现细节

### 1. JSON 类型定义

在 `internal/adapter/persistence/model/billing_model.go` 中定义了自定义 JSON 类型：

```go
// AppliedFreightTemplatesJSON 应用的运费模板信息JSON类型
type AppliedFreightTemplatesJSON map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AppliedFreightTemplatesJSON) Value() (driver.Value, error) {
    if a == nil {
        return nil, nil
    }
    return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AppliedFreightTemplatesJSON) Scan(value interface{}) error {
    if value == nil {
        *a = nil
        return nil
    }

    bytes, ok := value.([]byte)
    if !ok {
        return nil
    }

    return json.Unmarshal(bytes, a)
}
```

### 2. 解析函数实现

#### 主解析函数

```go
// parseAppliedFreightTemplates 解析应用的运费模板JSON数据
func (r *BillingRepositoryImpl) parseAppliedFreightTemplates(templatesJSON *model.AppliedFreightTemplatesJSON) (*entity.AppliedFreightTemplate, error) {
    if templatesJSON == nil {
        return nil, nil
    }

    result := &entity.AppliedFreightTemplate{}
    templateMap := map[string]interface{}(*templatesJSON)

    // 解析普货模板
    if generalTemplate, exists := templateMap["generalTemplate"]; exists && generalTemplate != nil {
        if templateData, ok := generalTemplate.(map[string]interface{}); ok {
            template, err := r.parseShippingFeeTemplate(templateData)
            if err == nil {
                result.GeneralTemplate = template
            }
        }
    }

    // 解析带电货物模板
    if batteryTemplate, exists := templateMap["batteryTemplate"]; exists && batteryTemplate != nil {
        if templateData, ok := batteryTemplate.(map[string]interface{}); ok {
            template, err := r.parseShippingFeeTemplate(templateData)
            if err == nil {
                result.BatteryTemplate = template
            }
        }
    }

    // 解析投函模板
    if postBoxTemplate, exists := templateMap["postBoxTemplate"]; exists && postBoxTemplate != nil {
        if templateData, ok := postBoxTemplate.(map[string]interface{}); ok {
            template, err := r.parseShippingFeeTemplate(templateData)
            if err == nil {
                result.PostBoxTemplate = template
            }
        }
    }

    return result, nil
}
```

#### 单个模板解析函数

```go
// parseShippingFeeTemplate 解析单个运费模板数据
func (r *BillingRepositoryImpl) parseShippingFeeTemplate(templateData map[string]interface{}) (*entity.ShippingFeeTemplate, error) {
    template := &entity.ShippingFeeTemplate{}

    // 解析各个字段
    if id, exists := templateData["id"]; exists {
        if idValue, ok := id.(float64); ok {
            template.ID = int64(idValue)
        }
    }

    if name, exists := templateData["name"]; exists {
        if nameValue, ok := name.(string); ok {
            template.Name = nameValue
        }
    }

    // ... 其他字段解析逻辑

    return template, nil
}
```

### 3. 类型转换处理

在 JSON 解析过程中，需要处理以下类型转换：

#### 数值类型转换

- JSON 中的数字都会被解析为 `float64` 类型
- 需要根据目标字段类型进行转换：
  - `int64`: `int64(floatValue)`
  - `int`: `int(floatValue)`
  - `float64`: 直接使用

#### 时间类型转换

- JSON 中的时间以字符串形式存储："2006-01-02 15:04:05"
- 解析为 `time.Time` 后转换为 `util.FormattedTime`

```go
if createTime, exists := templateData["createTime"]; exists {
    if timeStr, ok := createTime.(string); ok {
        if parsedTime, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
            template.CreateTime = util.NewFormattedTime(parsedTime)
        }
    }
}
```

### 4. 错误处理

- **解析失败容错**: 当 JSON 解析失败时，返回空的模板结构而不是错误
- **字段缺失容错**: 缺失的字段保持零值，不影响其他字段的解析
- **类型转换容错**: 类型转换失败时跳过该字段，继续解析其他字段

## 使用位置

### 1. FindBillingRecordByID 方法

```go
// 解析应用的运费模板信息
if po.AppliedFreightTemplates != nil {
    appliedTemplates, err := r.parseAppliedFreightTemplates(po.AppliedFreightTemplates)
    if err == nil && appliedTemplates != nil {
        record.AppliedFreightTemplates = appliedTemplates
    } else {
        // 如果解析失败，创建空的模板结构
        record.AppliedFreightTemplates = &entity.AppliedFreightTemplate{}
    }
}
```

### 2. FindBillingRecords 方法

在分页查询账单记录时，同样使用相同的解析逻辑确保数据一致性。

## 数据存储过程

### 保存时的 JSON 序列化

在 `SaveBillingRecord` 方法中，实体的运费模板信息会被转换为 JSON 格式存储：

```go
// 转换应用的运费模板信息为JSON
var appliedTemplatesJSON *model.AppliedFreightTemplatesJSON
if record.AppliedFreightTemplates != nil {
    templatesMap := make(map[string]interface{})
    if record.AppliedFreightTemplates.GeneralTemplate != nil {
        templatesMap["generalTemplate"] = record.AppliedFreightTemplates.GeneralTemplate
    }
    if record.AppliedFreightTemplates.BatteryTemplate != nil {
        templatesMap["batteryTemplate"] = record.AppliedFreightTemplates.BatteryTemplate
    }
    if record.AppliedFreightTemplates.PostBoxTemplate != nil {
        templatesMap["postBoxTemplate"] = record.AppliedFreightTemplates.PostBoxTemplate
    }
    jsonData := model.AppliedFreightTemplatesJSON(templatesMap)
    appliedTemplatesJSON = &jsonData
}
```

## 优势和特点

### 1. 数据完整性保护

- 账单生成时的运费模板信息被完整保存为 JSON 快照
- 即使原始模板被修改或删除，账单中的历史信息依然完整

### 2. 版本兼容性

- JSON 格式便于扩展新字段
- 向后兼容，新字段不影响旧数据的解析

### 3. 性能优化

- 避免了复杂的关联查询
- 账单查询时直接从 JSON 中获取模板信息

### 4. 业务逻辑清晰

- 账单记录自包含所有必要的费用计算信息
- 便于审计和对账

## 测试验证

### 1. 单元测试建议

```go
func TestParseAppliedFreightTemplates(t *testing.T) {
    // 测试完整的模板数据解析
    jsonData := model.AppliedFreightTemplatesJSON{
        "generalTemplate": map[string]interface{}{
            "id": float64(1),
            "name": "测试模板",
            "firstWeightPrice": 15.0,
            // ... 其他字段
        },
    }

    repo := &BillingRepositoryImpl{}
    result, err := repo.parseAppliedFreightTemplates(&jsonData)

    assert.NoError(t, err)
    assert.NotNil(t, result)
    assert.NotNil(t, result.GeneralTemplate)
    assert.Equal(t, int64(1), result.GeneralTemplate.ID)
}
```

### 2. 集成测试建议

- 测试完整的保存和读取流程
- 验证 JSON 序列化和反序列化的一致性
- 测试异常数据的容错处理

## 注意事项

### 1. 数据库字段类型

- 确保数据库字段使用 `JSON` 类型或足够大的 `TEXT` 类型
- MySQL 5.7+ 推荐使用 `JSON` 类型获得更好的性能

### 2. 内存使用

- JSON 解析会创建临时的 `map[string]interface{}` 对象
- 对于大量数据的批处理，需要注意内存使用

### 3. 并发安全

- 解析函数是无状态的，支持并发调用
- JSON 序列化/反序列化操作是线程安全的

## 扩展方向

### 1. 缓存优化

可以考虑对解析结果进行缓存，特别是对于频繁查询的账单记录。

### 2. 验证增强

可以添加 JSON schema 验证，确保数据格式的正确性。

### 3. 压缩优化

对于大量的模板数据，可以考虑 JSON 压缩存储。

这个实现提供了完整的 JSON 解析功能，确保了账单记录中运费模板信息的正确读取和展示，同时保持了良好的错误处理和向后兼容性。
