package middleware

import (
	"net/http"
	"strings"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "未提供认证Token", http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 验证token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			util.ResponseError(c, valueobject.ERROR_UNAUTHORIZED, "Token格式错误", http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 解析token
		claims, err := util.ParseToken(parts[1])
		if err != nil {
			util.ResponseError(c, valueobject.ERROR_TOKEN_INVALID, "Token无效或已过期", http.StatusUnauthorized)
			c.Abort()
			return
		}

		// 将用户信息保存到上下文
		c.Set(util.ClaimsContextKey, claims)

		c.Next()
	}
} 