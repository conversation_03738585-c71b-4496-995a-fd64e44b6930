# 财务模块 - 批量财务调整记录创建 API 文档

## 接口概述

本接口用于批量创建财务调整记录，支持根据订单号列表和调整类型组合批量创建改派、退回、销毁三种类型的财务调整记录。

## 基本信息

- **接口路径**: `POST /api/v1/financial-adjustments/batch`
- **接口名称**: 批量创建财务调整记录
- **权限要求**: 需要登录认证
- **Content-Type**: `application/json`

## 请求参数

### 请求体 (JSON)

```json
{
  "trackingNumbers": ["ZM2024010001", "ZM2024010002", "ZM2024010003"],
  "adjustmentItems": [
    {
      "adjustmentType": "REASSIGNMENT",
      "amount": 15.5
    },
    {
      "adjustmentType": "RETURN",
      "amount": 8.0
    }
  ],
  "effectiveDate": "2024-01-15",
  "description": "批量改派和退回处理",
  "currency": "CNY"
}
```

### 参数说明

| 参数名                           | 类型     | 必填 | 说明                                                                                                                            |
| -------------------------------- | -------- | ---- | ------------------------------------------------------------------------------------------------------------------------------- |
| trackingNumbers                  | []string | 是   | 订单号列表，最多 100 个                                                                                                         |
| adjustmentItems                  | []object | 是   | 调整项列表，每项包含调整类型和对应金额                                                                                          |
| adjustmentItems[].adjustmentType | string   | 是   | 调整类型，可选值：REASSIGNMENT(改派)、RETURN(退回)、DESTRUCTION(销毁)、COMPENSATION(赔偿)、FEE(费用)、REBATE(返款)、OTHER(其他) |
| adjustmentItems[].amount         | float64  | 是   | 该调整类型的金额                                                                                                                |
| effectiveDate                    | string   | 是   | 生效日期，格式：YYYY-MM-DD                                                                                                      |
| description                      | string   | 否   | 调整描述                                                                                                                        |
| currency                         | string   | 是   | 货币代码，如：CNY、USD                                                                                                          |

### 参数校验规则

1. **trackingNumbers**:

   - 必填，至少 1 个，最多 100 个订单号
   - 支持运单号(express_number)和转单号(transferred_tracking_number)

2. **adjustmentItems**:

   - 必填，至少 1 项调整类型
   - 每项必须包含 adjustmentType 和 amount
   - 可以多种类型组合：[{"adjustmentType": "REASSIGNMENT", "amount": 15.5}]

3. **adjustmentType**:

   - 支持的类型：REASSIGNMENT、RETURN、DESTRUCTION、COMPENSATION、FEE、REBATE、OTHER
   - 大小写敏感，必须使用大写

4. **amount**:

   - 必填，必须为正数
   - 支持小数，精度到分
   - 每种调整类型可以设置不同的金额

5. **effectiveDate**:

   - 必填，格式必须为 YYYY-MM-DD
   - 例如：2024-01-15

6. **currency**:
   - 必填，支持标准货币代码
   - 常用值：CNY、USD、EUR、JPY

**注意**：customerAccountId 不需要在请求中提供，系统会自动从运单信息中获取对应的用户 ID 作为客户账户 ID。

## 响应结果

### 成功响应 (200)

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "successCount": 4,
    "failureCount": 2,
    "totalCount": 6,
    "successItems": [
      {
        "id": 1001,
        "manifestId": 12345,
        "trackingNumber": "ZM2024010001",
        "transferredNumber": null,
        "adjustmentType": "reassignment",
        "description": "批量改派和退回处理",
        "additionalDetails": {
          "description": "批量改派和退回处理"
        },
        "amount": 15.5,
        "currency": "CNY",
        "effectiveDate": "2024-01-15T00:00:00Z",
        "customerAccountId": 10001,
        "creatorId": 1001,
        "createTime": "2024-01-15T10:30:00Z",
        "updateTime": "2024-01-15T10:30:00Z"
      },
      {
        "id": 1002,
        "manifestId": 12345,
        "trackingNumber": "ZM2024010001",
        "transferredNumber": null,
        "adjustmentType": "return",
        "description": "批量改派和退回处理",
        "additionalDetails": {
          "return_reason": "批量改派和退回处理"
        },
        "amount": 15.5,
        "currency": "CNY",
        "effectiveDate": "2024-01-15T00:00:00Z",
        "customerAccountId": 10001,
        "creatorId": 1001,
        "createTime": "2024-01-15T10:30:00Z",
        "updateTime": "2024-01-15T10:30:00Z"
      }
    ],
    "failureItems": [
      {
        "trackingNumber": "ZM2024010003",
        "adjustmentType": "reassignment",
        "errorCode": 100006,
        "errorMessage": "订单号不存在: ZM2024010003"
      },
      {
        "trackingNumber": "ZM2024010003",
        "adjustmentType": "return",
        "errorCode": 100006,
        "errorMessage": "订单号不存在: ZM2024010003"
      }
    ],
    "summary": {
      "totalAmount": 62.0,
      "reassignmentCount": 2,
      "returnCount": 2,
      "destructionCount": 0
    }
  }
}
```

### 失败响应

#### 参数错误 (400)

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "无效的调整类型: invalid_type",
  "data": null
}
```

#### 未授权 (401)

```json
{
  "success": false,
  "errorCode": 100004,
  "errorMessage": "未授权的请求",
  "data": null
}
```

#### 服务器错误 (500)

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "服务器内部错误",
  "data": null
}
```

## 响应字段说明

### 主要响应字段

| 字段名       | 类型     | 说明               |
| ------------ | -------- | ------------------ |
| successCount | int      | 成功创建的记录数   |
| failureCount | int      | 失败的记录数       |
| totalCount   | int      | 总记录数           |
| successItems | []object | 成功创建的记录列表 |
| failureItems | []object | 失败的记录列表     |
| summary      | object   | 汇总信息           |

### successItems 字段说明

| 字段名            | 类型                   | 说明          |
| ----------------- | ---------------------- | ------------- |
| id                | int64                  | 调整记录 ID   |
| manifestId        | int64                  | 关联的运单 ID |
| trackingNumber    | string                 | 运单号        |
| transferredNumber | string                 | 转单号        |
| adjustmentType    | string                 | 调整类型      |
| description       | string                 | 调整描述      |
| additionalDetails | map[string]interface{} | 附加详情      |
| amount            | float64                | 调整金额      |
| currency          | string                 | 货币代码      |
| effectiveDate     | string                 | 生效日期      |
| customerAccountId | int64                  | 客户账户 ID   |
| creatorId         | int64                  | 创建者 ID     |
| createTime        | string                 | 创建时间      |
| updateTime        | string                 | 更新时间      |

### failureItems 字段说明

| 字段名         | 类型   | 说明     |
| -------------- | ------ | -------- |
| trackingNumber | string | 订单号   |
| adjustmentType | string | 调整类型 |
| errorCode      | int    | 错误码   |
| errorMessage   | string | 错误消息 |

### summary 字段说明

| 字段名            | 类型    | 说明       |
| ----------------- | ------- | ---------- |
| totalAmount       | float64 | 总调整金额 |
| reassignmentCount | int     | 改派记录数 |
| returnCount       | int     | 退回记录数 |
| destructionCount  | int     | 销毁记录数 |

## 业务逻辑说明

### 处理流程

1. **参数验证**: 验证请求参数的合法性
2. **订单号查询**: 根据订单号列表查询对应的运单信息
3. **批量创建**: 为每个订单号的每种调整类型创建调整记录
4. **结果统计**: 统计成功和失败的记录数量，生成汇总信息

### 创建规则

- 每个订单号 × 每种调整类型 = 一条调整记录
- 例如：3 个订单号 × 2 种调整类型 = 6 条调整记录
- 单个订单号不存在时，该订单号对应的所有调整类型都会失败
- 部分成功的情况下，接口仍返回 200 状态码，通过响应数据中的成功/失败统计来判断结果

### 附加详情设置

根据不同的调整类型，系统会自动设置相应的附加详情：

- **reassignment**: `{"description": "用户输入的描述"}`
- **return**: `{"return_reason": "用户输入的描述"}`
- **destruction**: `{"destruction_reason": "用户输入的描述"}`

## 使用示例

### cURL 示例

```bash
curl -X POST "http://localhost:8080/api/v1/financial-adjustments/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "trackingNumbers": ["ZM2024010001", "ZM2024010002"],
    "adjustmentItems": [
      {"adjustmentType": "REASSIGNMENT", "amount": 15.5},
      {"adjustmentType": "RETURN", "amount": 8.0}
    ],
    "effectiveDate": "2024-01-15",
    "description": "批量处理改派和退回费用",
    "currency": "CNY"
  }'
```

### JavaScript 示例

```javascript
const response = await fetch("/api/v1/financial-adjustments/batch", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    Authorization: "Bearer " + token,
  },
  body: JSON.stringify({
    trackingNumbers: ["ZM2024010001", "ZM2024010002"],
    adjustmentItems: [
      { adjustmentType: "REASSIGNMENT", amount: 15.5 },
      { adjustmentType: "RETURN", amount: 8.0 },
    ],
    effectiveDate: "2024-01-15",
    description: "批量处理改派和退回费用",
    currency: "CNY",
  }),
});

const result = await response.json();
console.log("批量创建结果:", result);
```

## 错误码说明

| 错误码 | 说明              |
| ------ | ----------------- |
| 100000 | 操作成功          |
| 100001 | 未知错误/系统繁忙 |
| 100002 | 无效参数          |
| 100003 | 缺少必要参数      |
| 100004 | 未授权/未登录     |
| 100006 | 请求的资源未找到  |

## 注意事项

1. **性能考虑**:

   - 最多支持 100 个订单号的批量处理
   - 建议根据系统负载情况合理控制批次大小

2. **事务处理**:

   - 每条调整记录的创建是独立的
   - 单条记录失败不会影响其他记录的创建

3. **重复处理**:

   - 系统不会自动去重，相同的订单号和调整类型会创建多条记录
   - 建议前端做好去重处理

4. **权限控制**:

   - 需要登录用户权限
   - 创建的记录会自动关联当前登录用户作为创建者

5. **数据一致性**:
   - 所有成功创建的记录使用相同的金额、生效日期、货币等参数
   - 如需不同金额，建议分别调用接口

## 版本信息

- **API 版本**: v1.0.0
- **创建日期**: 2024-01-15
- **更新日期**: 2024-01-15
- **维护人员**: 开发团队
