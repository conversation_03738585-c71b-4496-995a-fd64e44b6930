package util

import (
	"net/url"
	"regexp"
	"strings"
)

// 保留中文、英文字母、数字和常用标点符号的正则表达式
// 中文范围（CJK统一汉字）：[\u4e00-\u9fa5]
// 英文字母和数字：[a-zA-Z0-9]
// 常用标点：,.!?;:()'"-_/+%
var validCharsRegex = regexp.MustCompile(`[^\p{Han}a-zA-Z0-9,.!?;:()'"\-_/+% ]`)

// CleanText 清理文本，只保留中英文字符、数字和常用标点符号
func CleanText(text string) string {
	// 替换所有非法字符为空字符串
	cleanedText := validCharsRegex.ReplaceAllString(text, "")
	// 移除多余空格
	cleanedText = strings.TrimSpace(cleanedText)
	// 将连续多个空格替换为单个空格
	return regexp.MustCompile(`\s+`).ReplaceAllString(cleanedText, " ")
}

// NormalizeItemName 规范化物品名称，用于编辑和翻译前清理
func NormalizeItemName(name string) string {
	return CleanText(name)
}

// FormatTextDescriptionForManifest 格式化文本描述，用于运单
func FormatTextDescriptionForManifest(text string) string {
	// 去除两端空格
	text = strings.TrimSpace(text)
	// 去除换行符
	text = regexp.MustCompile(`\r?\n`).ReplaceAllString(text, " ")
	// 将多个连续空格替换为单个空格
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	return text
}

// FormatZipCode 格式化邮政编码，确保格式为XXX-XXXX
func FormatZipCode(zipCode string) string {
	// 移除所有非数字字符
	digitsOnly := regexp.MustCompile(`\D`).ReplaceAllString(zipCode, "")
	
	// 日本邮编通常为7位数字，格式化为XXX-XXXX
	if len(digitsOnly) == 7 {
		return digitsOnly[:3] + "-" + digitsOnly[3:]
	}
	
	// 如果不是7位，则保持原样
	return zipCode
}

// URLEncode URL编码字符串，用于处理文件名中的中文和特殊字符
func URLEncode(str string) string {
	// 使用URL编码进行转义
	return url.QueryEscape(str)
} 