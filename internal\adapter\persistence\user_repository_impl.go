package persistence

import (
	"context"
	"time"
	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// UserRepositoryImpl 用户仓储实现
type UserRepositoryImpl struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓储
func NewUserRepository(db *gorm.DB) repository.UserRepository {
	return &UserRepositoryImpl{
		db: db,
	}
}

// FindByUsername 根据用户名查找用户
func (r *UserRepositoryImpl) FindByUsername(ctx context.Context, username string) (*entity.User, error) {
	var userPO model.UserPO
	if err := r.db.Where("username = ?", username).First(&userPO).Error; err != nil {
		return nil, err
	}
	return userPO.ToEntity(), nil
}

// FindByPhone 根据手机号查找用户
func (r *UserRepositoryImpl) FindByPhone(ctx context.Context, phone string) (*entity.User, error) {
	var userPO model.UserPO
	if err := r.db.Where("phone = ?", phone).First(&userPO).Error; err != nil {
		return nil, err
	}
	return userPO.ToEntity(), nil
}

// FindByID 根据用户ID查找用户
func (r *UserRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.User, error) {
	var userPO model.UserPO
	if err := r.db.Where("id = ?", id).First(&userPO).Error; err != nil {
		return nil, err
	}
	return userPO.ToEntity(), nil
}

// UpdateLoginInfo 更新用户登录信息
func (r *UserRepositoryImpl) UpdateLoginInfo(ctx context.Context, id int64, loginIP string) error {
	now := time.Now()
	return r.db.Model(&model.UserPO{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"login_ip":    loginIP,
			"login_date":  now,
			"update_time": now,
		}).Error
}

// FindByIDs 根据用户ID批量查询用户
func (r *UserRepositoryImpl) FindByIDs(ctx context.Context, ids []int64) ([]*entity.User, error) {
	if len(ids) == 0 {
		return []*entity.User{}, nil
	}

	var userPOs []*model.UserPO
	if err := r.db.Where("id IN ?", ids).Find(&userPOs).Error; err != nil {
		return nil, err
	}

	users := make([]*entity.User, 0, len(userPOs))
	for _, po := range userPOs {
		users = append(users, po.ToEntity())
	}
	return users, nil
} 

// ListUsers 查询用户列表，用于下拉框
func (r *UserRepositoryImpl) ListUsers(ctx context.Context, keyword string, page, pageSize int) ([]*entity.User, int64, error) {
	var userPOs []*model.UserPO
	var total int64
	
	offset := (page - 1) * pageSize
	
	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&model.UserPO{})
	
	// 添加关键字过滤
	if keyword != "" {
		keyword = "%" + keyword + "%"
		query = query.Where("username LIKE ? OR nickname LIKE ?", keyword, keyword)
	}
	
	// 只查询有效用户
	query = query.Where("status = ?", 1)
	
	// 查询总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询数据
	if err := query.Order("id ASC").Offset(offset).Limit(pageSize).Find(&userPOs).Error; err != nil {
		return nil, 0, err
	}
	
	// 转换为实体
	users := make([]*entity.User, 0, len(userPOs))
	for _, po := range userPOs {
		users = append(users, po.ToEntity())
	}
	
	return users, total, nil
} 