# Docker 快速部署

## 📁 当前文件

```
├── Dockerfile                   # API服务器
├── Dockerfile.consumer          # 账单消费者
├── docker-compose.yml           # 完整服务编排
├── docker-compose.api-only.yml  # 仅API服务
└── .dockerignore               # 构建忽略
```

## 🚀 部署选项

### 选项 1：完整部署（API + 账单处理）

```bash
docker-compose up -d --build
```

### 选项 2：仅 API 服务（推荐用于简单场景）

```bash
# 方式A：选择性启动
docker-compose up -d api-server

# 方式B：使用专用配置
docker-compose -f docker-compose.api-only.yml up -d --build
```

### 选项 3：直接 Docker 命令

```bash
# 构建镜像
docker build --build-arg APP_NAME=api-server --build-arg BINARY_NAME=zebra-hub-system -t zebra-hub-system/api-server:latest -f Dockerfile .

# 运行容器
docker run -d --name zebra-api-server -p 8080:8080 -v $(pwd)/configs:/app/configs -v $(pwd)/data:/app/data -v $(pwd)/assets:/app/assets -e TZ=Asia/Shanghai --restart unless-stopped zebra-hub-system/api-server:latest
```

## 🔍 验证部署

```bash
# 检查API服务
curl http://localhost:8080/api/v1/upload/info

# 查看容器状态
docker ps | grep zebra

# 查看日志
docker logs zebra-api-server
```

## 📋 常用命令

### 完整部署管理

```bash
# 停止所有服务
docker-compose down

# 重启所有服务
docker-compose restart

# 查看所有服务日志
docker-compose logs -f
```

### 仅 API 服务管理

```bash
# 停止API服务
docker-compose stop api-server
# 或
docker-compose -f docker-compose.api-only.yml down

# 重启API服务
docker-compose restart api-server
# 或
docker restart zebra-api-server

# 查看API服务日志
docker-compose logs -f api-server
# 或
docker logs -f zebra-api-server
```

## 🏗️ 服务说明

- **zebra-api-server**: REST API 服务 (端口 8080)
- **zebra-billing-consumer**: 账单处理服务（可选）

## 💾 数据挂载

- `./configs` → 配置文件
- `./data` → 数据文件
- `./assets` → 字体资源

## 🎯 使用建议

- **开发测试**: 使用"仅 API 服务"部署
- **生产环境**: 根据需要选择完整部署或仅 API
- **资源有限**: 推荐仅 API 服务部署

---

> **注意**: 仅 API 部署时，账单处理功能需要手动处理或配置外部处理服务
