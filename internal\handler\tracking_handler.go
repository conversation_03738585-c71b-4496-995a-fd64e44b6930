package handler

import (
	"net/http"
	"strconv"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// TrackingHandler 轨迹处理器
type TrackingHandler struct {
	trackingService *service.TrackingService
}

// NewTrackingHandler 创建轨迹处理器
func NewTrackingHandler(trackingService *service.TrackingService) *TrackingHandler {
	return &TrackingHandler{
		trackingService: trackingService,
	}
}

// GetTrackingsByManifestID 根据运单ID查询轨迹
// @Summary 根据运单ID查询轨迹
// @Description 获取指定运单ID的所有物流轨迹记录
// @Tags 轨迹查询
// @Accept json
// @Produce json
// @Param id path int true "运单ID"
// @Success 200 {object} util.Response{data=service.GetTrackingsByManifestIDResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "运单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/manifests/{id}/trackings [get]
func (h *TrackingHandler) GetTrackingsByManifestID(c *gin.Context) {
	// 从路径参数获取运单ID
	manifestIDStr := c.Param("id")
	manifestID, err := strconv.ParseInt(manifestIDStr, 10, 64)
	if err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "无效的运单ID", http.StatusBadRequest)
		return
	}

	// 构造请求
	req := &service.GetTrackingsByManifestIDRequest{
		ManifestID: manifestID,
	}

	// 调用服务层
	resp, code, err := h.trackingService.GetTrackingsByManifestID(c.Request.Context(), req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		switch code {
		case valueobject.ERROR_RESOURCE_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	// 返回结果
	util.ResponseSuccess(c, resp)
} 