package repository

import (
	"context"
	"time"
	"zebra-hub-system/internal/domain/entity"
)

// BillingRepository 账单仓储接口
type BillingRepository interface {
	// SaveBillingRecord 保存账单记录
	SaveBillingRecord(ctx context.Context, record *entity.BillingRecord) error
	
	// SaveBillingRecordItems 批量保存账单明细
	SaveBillingRecordItems(ctx context.Context, items []*entity.BillingRecordItem) error
	
	// SaveBillingFinancialAdjustmentSnapshots 批量保存财务调整快照
	SaveBillingFinancialAdjustmentSnapshots(ctx context.Context, snapshots []*entity.BillingFinancialAdjustmentSnapshot) error
	
	// FindManifestsForBilling 根据条件查询用于生成账单的运单
	// timeType: 时间类型（CREATE_TIME 或 SHIPMENT_TIME）
	// startTime, endTime: 时间范围
	// userID: 用户ID
	// 返回符合条件的运单列表
	FindManifestsForBilling(ctx context.Context, timeType entity.BillingTimeType, startTime, endTime time.Time, userID int64) ([]*entity.Manifest, error)
	
	// FindFinancialAdjustmentsForBilling 根据用户ID和生效日期范围查询财务调整记录
	// userID: 用户ID
	// startDate, endDate: 生效日期范围
	// 返回符合条件的财务调整记录列表
	FindFinancialAdjustmentsForBilling(ctx context.Context, userID int64, startDate, endDate time.Time) ([]*entity.ManifestFinancialAdjustment, error)
	
	// FindManifestsByIDs 根据运单ID列表批量查询运单信息
	// manifestIDs: 运单ID列表
	// 返回运单列表
	FindManifestsByIDs(ctx context.Context, manifestIDs []int64) ([]*entity.Manifest, error)
	
	// GenerateBillNumber 生成唯一的账单编号
	GenerateBillNumber(ctx context.Context) (string, error)
	
	// FindBillingRecordByID 根据ID查询账单记录
	FindBillingRecordByID(ctx context.Context, id int64) (*entity.BillingRecord, error)
	
	// CountBillingRecords 统计账单记录总数
	CountBillingRecords(ctx context.Context, filters interface{}) (int64, error)
	
	// FindBillingRecords 分页查询账单记录
	FindBillingRecords(ctx context.Context, filters interface{}, page, pageSize int) ([]*entity.BillingRecord, error)
	
	// FindBillingRecordItemsByBillingRecordID 根据账单记录ID查询账单明细列表
	FindBillingRecordItemsByBillingRecordID(ctx context.Context, billingRecordID int64, page, pageSize int) ([]*entity.BillingRecordItem, int64, error)
	
	// FindUsersWithManifestsForBilling 查询在指定时间范围内有可生成账单运单的用户统计
	// timeType: 时间类型（CREATE_TIME 或 SHIPMENT_TIME）
	// startTime, endTime: 时间范围
	// 返回用户统计列表
	FindUsersWithManifestsForBilling(ctx context.Context, timeType entity.BillingTimeType, startTime, endTime time.Time) ([]*entity.BillingUserStats, error)
	
	// FindUsersWithAdjustmentsForBilling 查询在指定时间范围内有财务调整记录的用户统计
	// startTime, endTime: 时间范围（根据effective_date）
	// 返回用户统计列表
	FindUsersWithAdjustmentsForBilling(ctx context.Context, startTime, endTime time.Time) ([]*entity.BillingUserStats, error)

	// FindBillingFinancialAdjustmentSnapshotsByBillingRecordID 根据账单记录ID分页查询财务调整快照列表
	// billingRecordID: 账单记录ID
	// page, pageSize: 分页参数
	// 返回快照列表和总数
	FindBillingFinancialAdjustmentSnapshotsByBillingRecordID(ctx context.Context, billingRecordID int64, page, pageSize int) ([]*entity.BillingFinancialAdjustmentSnapshot, int64, error)
} 