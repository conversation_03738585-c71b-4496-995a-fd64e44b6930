# 财务模块 - 账单记录详情接口说明

## 接口概述

新增了根据账单记录 ID 获取账单记录详情的接口，提供完整的账单信息查询功能。

## 接口信息

### 基本信息

- **接口路径**: `GET /api/v1/finance/billing/records/{billingRecordId}`
- **接口名称**: 根据账单记录 ID 获取账单记录详情
- **认证要求**: 需要 JWT 认证
- **请求方式**: GET

### 路径参数

| 参数名          | 类型  | 必填 | 说明        |
| --------------- | ----- | ---- | ----------- |
| billingRecordId | int64 | 是   | 账单记录 ID |

### 响应数据结构

#### 成功响应 (200)

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "billingRecord": {
      "id": 1,
      "billNumber": "BILL-********-143022-123456",
      "customerAccountId": 1001,
      "customerNickname": "张三",
      "customerUsername": "zhangsan",
      "billDate": "2024-12-01",
      "dueDate": "2024-12-31",
      "billingPeriodStart": "2024-11-01",
      "billingPeriodEnd": "2024-11-30",
      "appliedFreightTemplates": {
        "generalTemplate": {
          "id": 1,
          "name": "普货标准模板",
          "type": 1,
          "firstWeightPrice": 15.0,
          "firstWeightRange": 0.5,
          "continuedWeightPrice": 8.0,
          "continuedWeightInterval": 0.5,
          "bulkCoefficient": 6000,
          "threeSidesStart": 60.0
        },
        "batteryTemplate": {
          "id": 2,
          "name": "带电货物模板",
          "type": 2,
          "firstWeightPrice": 20.0,
          "firstWeightRange": 0.5,
          "continuedWeightPrice": 12.0,
          "continuedWeightInterval": 0.5,
          "bulkCoefficient": 6000,
          "threeSidesStart": 60.0
        },
        "postBoxTemplate": null
      },
      "totalAmount": 1250.5,
      "amountPaid": 0.0,
      "balanceDue": 1250.5,
      "currency": "CNY",
      "status": "UNPAID",
      "statusName": "未付款",
      "paymentMethod": null,
      "paymentTransactionId": null,
      "paymentDate": null,
      "notes": "2024年11月账期账单",
      "generatedByUserId": 1,
      "generatedByNickname": "系统管理员",
      "createTime": "2024-12-01 14:30:22",
      "updateTime": "2024-12-01 14:30:22"
    }
  },
  "requestId": "req-*********",
  "timestamp": "2024-12-01T14:30:22Z"
}
```

#### 错误响应

**400 - 参数错误**

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "账单记录ID必须大于0",
  "data": null,
  "requestId": "req-*********",
  "timestamp": "2024-12-01T14:30:22Z"
}
```

**404 - 账单记录不存在**

```json
{
  "success": false,
  "errorCode": 100006,
  "errorMessage": "账单记录不存在",
  "data": null,
  "requestId": "req-*********",
  "timestamp": "2024-12-01T14:30:22Z"
}
```

**500 - 服务器内部错误**

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "服务器内部错误",
  "data": null,
  "requestId": "req-*********",
  "timestamp": "2024-12-01T14:30:22Z"
}
```

## 响应字段说明

### BillingRecordDetailDTO 字段说明

| 字段名                  | 类型    | 说明                                       |
| ----------------------- | ------- | ------------------------------------------ |
| id                      | int64   | 账单主键 ID                                |
| billNumber              | string  | 账单编号                                   |
| customerAccountId       | int64   | 客户账户 ID                                |
| customerNickname        | string  | 客户昵称                                   |
| customerUsername        | string  | 客户用户名                                 |
| billDate                | string  | 账单日期（yyyy-MM-dd 格式）                |
| dueDate                 | string  | 付款截止日期（yyyy-MM-dd 格式，可选）      |
| billingPeriodStart      | string  | 账期开始日期（yyyy-MM-dd 格式）            |
| billingPeriodEnd        | string  | 账期结束日期（yyyy-MM-dd 格式）            |
| appliedFreightTemplates | object  | 应用的运费模板信息                         |
| totalAmount             | float64 | 账单总金额                                 |
| amountPaid              | float64 | 已付金额                                   |
| balanceDue              | float64 | 应付余额                                   |
| currency                | string  | 货币单位                                   |
| status                  | string  | 账单状态                                   |
| statusName              | string  | 账单状态名称                               |
| paymentMethod           | string  | 支付方式（可选）                           |
| paymentTransactionId    | string  | 支付交易号（可选）                         |
| paymentDate             | string  | 支付日期（yyyy-MM-dd HH:mm:ss 格式，可选） |
| notes                   | string  | 账单备注（可选）                           |
| generatedByUserId       | int64   | 账单生成操作员 ID（可选）                  |
| generatedByNickname     | string  | 账单生成操作员昵称（可选）                 |
| createTime              | string  | 记录创建时间（yyyy-MM-dd HH:mm:ss 格式）   |
| updateTime              | string  | 记录更新时间（yyyy-MM-dd HH:mm:ss 格式）   |

### 账单状态说明

| 状态值         | 状态名称 | 说明                 |
| -------------- | -------- | -------------------- |
| UNPAID         | 未付款   | 账单已生成，等待付款 |
| PAID           | 已付款   | 账单已完全付款       |
| PARTIALLY_PAID | 部分付款 | 账单已部分付款       |
| OVERDUE        | 逾期     | 账单已逾期未付款     |
| VOID           | 作废     | 账单已作废           |

### 运费模板信息说明

`appliedFreightTemplates` 字段包含生成账单时使用的运费模板信息：

- **generalTemplate**: 普货模板信息
- **batteryTemplate**: 带电货物模板信息
- **postBoxTemplate**: 投函货物模板信息

每个模板包含以下字段：

- `id`: 模板 ID
- `name`: 模板名称
- `type`: 模板类型（1-普通模板，2-带电模板，3-投函模板）
- `firstWeightPrice`: 首重价格
- `firstWeightRange`: 首重范围
- `continuedWeightPrice`: 续重价格
- `continuedWeightInterval`: 续重区间大小
- `bulkCoefficient`: 轻抛系数
- `threeSidesStart`: 三边和超过多少开始计算体积重量

## 业务逻辑说明

### 1. 参数验证

- 验证账单记录 ID 是否大于 0
- 如果参数无效，返回 400 错误

### 2. 数据查询

- 根据账单记录 ID 查询账单基本信息
- 如果账单记录不存在，返回 404 错误

### 3. 用户信息补充

- 查询客户账户信息（昵称、用户名）
- 查询账单生成操作员信息（如果有）
- 如果用户信息查询失败，不影响主要功能，只是相关字段为空

### 4. 数据转换

- 将实体对象转换为 DTO 对象
- 格式化时间字段为指定格式
- 转换账单状态为中文名称
- 保留完整的运费模板信息

## 使用示例

### cURL 示例

```bash
curl -X GET \
  'http://localhost:8080/api/v1/finance/billing/records/1' \
  -H 'Authorization: Bearer your-jwt-token' \
  -H 'Content-Type: application/json'
```

### JavaScript 示例

```javascript
// 使用 fetch API
async function getBillingRecordDetail(billingRecordId) {
  try {
    const response = await fetch(
      `/api/v1/finance/billing/records/${billingRecordId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    const result = await response.json();

    if (result.success) {
      console.log("账单详情:", result.data.billingRecord);
      return result.data.billingRecord;
    } else {
      console.error("获取账单详情失败:", result.errorMessage);
      throw new Error(result.errorMessage);
    }
  } catch (error) {
    console.error("请求失败:", error);
    throw error;
  }
}

// 使用示例
getBillingRecordDetail(1)
  .then((billingRecord) => {
    console.log("账单编号:", billingRecord.billNumber);
    console.log("客户昵称:", billingRecord.customerNickname);
    console.log("账单总金额:", billingRecord.totalAmount);
    console.log("账单状态:", billingRecord.statusName);
  })
  .catch((error) => {
    console.error("获取账单详情失败:", error);
  });
```

## 相关接口

### 关联接口

- `GET /finance/billing/records` - 分页查询账单记录列表
- `GET /finance/billing/records/{billingRecordId}/items` - 查询账单明细列表
- `POST /finance/billing/generate` - 生成账单

### 典型使用流程

1. 通过账单列表接口获取账单概要信息
2. 使用本接口获取特定账单的详细信息
3. 如需查看账单明细，调用账单明细列表接口

## 技术实现

### 代码结构

```
internal/
├── app/service/billing_service.go          # 服务层实现
├── handler/finance_handler.go              # 处理器层实现
├── router/router.go                        # 路由配置
└── adapter/persistence/billing_repository_impl.go  # 数据访问层
```

### 关键方法

- **服务层**: `BillingService.GetBillingRecordDetail()`
- **处理器层**: `FinanceHandler.GetBillingRecordDetail()`
- **仓储层**: `BillingRepository.FindBillingRecordByID()`

### 数据流程

```
HTTP请求 → 路由 → 处理器 → 服务层 → 仓储层 → 数据库
                ↓
HTTP响应 ← JSON序列化 ← DTO转换 ← 实体对象 ← 查询结果
```

## 注意事项

### 1. 性能考虑

- 接口会查询用户信息，但采用批量查询优化
- 如果用户信息查询失败，不影响主要功能
- 运费模板信息直接从账单记录中获取，无需额外查询

### 2. 数据一致性

- 账单记录中的运费模板信息是生成账单时的快照
- 即使原始模板被修改或删除，账单中的信息保持不变

### 3. 错误处理

- 参数验证失败返回 400 错误
- 账单记录不存在返回 404 错误
- 系统异常返回 500 错误
- 用户信息查询失败不影响主要功能

### 4. 安全考虑

- 需要 JWT 认证
- 建议添加权限控制（如只能查看自己的账单）
- 敏感信息（如支付交易号）需要权限控制

## 扩展建议

### 1. 权限控制

可以在处理器中添加权限验证：

```go
// 检查用户是否有权限查看该账单
if !hasPermission(userID, billingRecordID) {
    util.ResponseError(c, valueobject.ERROR_FORBIDDEN_ACCESS, "无权限访问该账单", http.StatusForbidden)
    return
}
```

### 2. 缓存优化

对于频繁查询的账单详情，可以考虑添加缓存：

```go
// 先从缓存查询
if cachedRecord := cache.Get(billingRecordID); cachedRecord != nil {
    return cachedRecord, nil
}
```

### 3. 审计日志

可以添加查询日志记录：

```go
logger.Info("User viewed billing record detail",
    zap.Int64("userId", userID),
    zap.Int64("billingRecordId", billingRecordID))
```

这个接口为财务模块提供了完整的账单详情查询功能，支持前端展示账单的完整信息，包括客户信息、运费模板、支付状态等关键数据。
