package handler

import (
	"net/http"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
)

// ManifestHandler 运单相关处理器
type ManifestHandler struct {
	manifestService service.ManifestService
}

// NewManifestHandler 创建运单处理器
func NewManifestHandler(manifestService service.ManifestService) *ManifestHandler {
	return &ManifestHandler{manifestService: manifestService}
}

// ListPendingReview 分页查询待审核运单
// @Summary 分页查询待审核运单
// @Description 获取状态为待审核 (status=0) 的运单列表，包含运单物品信息
// @Tags Manifests
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} util.Response{data=service.ListPendingReviewManifestsResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/pending-review [get]
// @Security ApiKeyAuth
func (h *ManifestHandler) ListPendingReview(c *gin.Context) {
	var req service.ListPendingReviewManifestsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.manifestService.ListPendingReview(c, &req)
	if err != nil {
		// 根据实际错误类型和业务需求，可能需要返回不同的 HTTP 状态码
		// 例如，如果是因为资源找不到，可以返回 http.StatusNotFound
		// 这里统一返回 http.StatusInternalServerError，具体错误信息在 errorMessage 中
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, resp)
}

// UpdateManifest 更新运单信息
// @Summary 更新运单信息
// @Description 更新运单的基本信息和物品信息，包括收件人姓名、电话、邮编、地址和物品列表
// @Tags Manifests
// @Accept json
// @Produce json
// @Param request body service.UpdateManifestRequest true "更新运单请求"
// @Success 200 {object} util.Response{data=service.UpdateManifestResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "运单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/update [post]
// @Security ApiKeyAuth
func (h *ManifestHandler) UpdateManifest(c *gin.Context) {
	var req service.UpdateManifestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.manifestService.UpdateManifest(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		switch code {
		case valueobject.ERROR_MANIFEST_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER,
			valueobject.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID,
			valueobject.ERROR_MANIFEST_ZIPCODE_NOT_EXIST,
			valueobject.ERROR_MANIFEST_PHONE_FORMAT_INVALID:
			httpStatus = http.StatusBadRequest
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// ApproveManifest 审核通过运单
// @Summary 审核通过运单
// @Description 将运单状态设置为通过(1)
// @Tags Manifests
// @Accept json
// @Produce json
// @Param request body service.ManifestApproveRequest true "运单审核通过请求"
// @Success 200 {object} util.Response{data=service.ManifestApproveResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "运单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/approve [post]
// @Security ApiKeyAuth
func (h *ManifestHandler) ApproveManifest(c *gin.Context) {
	var req service.ManifestApproveRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.manifestService.ApproveManifest(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		switch code {
		case valueobject.ERROR_MANIFEST_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetPendingCount 获取待审核运单数量
// @Summary 获取待审核运单数量
// @Description 获取状态为待审核(status=0)的运单数量
// @Tags Manifests
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=service.ManifestPendingCountResponse} "成功响应"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/pending-count [get]
// @Security ApiKeyAuth
func (h *ManifestHandler) GetPendingCount(c *gin.Context) {
	resp, code, err := h.manifestService.GetPendingCount(c)
	if err != nil {
		util.ResponseError(c, code, err.Error(), http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetManifestByID 根据ID获取运单详情
// @Summary 根据ID获取运单详情
// @Description 根据运单ID获取详细信息，包含物品列表、用户信息和财务调整记录
// @Tags Manifests
// @Accept json
// @Produce json
// @Param id path int true "运单ID"
// @Success 200 {object} util.Response{data=service.GetManifestByIDResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 404 {object} util.Response "运单不存在"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/{id} [get]
// @Security ApiKeyAuth
func (h *ManifestHandler) GetManifestByID(c *gin.Context) {
	var req service.GetManifestByIDRequest
	if err := c.ShouldBindUri(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.manifestService.GetManifestByID(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError
		
		switch code {
		case valueobject.ERROR_MANIFEST_NOT_FOUND:
			httpStatus = http.StatusNotFound
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}
		
		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetProblemManifestCount godoc
// @Summary 获取问题运单数量
// @Description 获取校验失败且未删除的运单数量
// @Tags Manifests
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=service.ProblemManifestCountResponse} "成功"
// @Failure 500 {object} util.Response "服务器错误"
// @Router /manifests/problem-count [get]
// @Security ApiKeyAuth
func (h *ManifestHandler) GetProblemManifestCount(c *gin.Context) {
	resp, errCode, err := h.manifestService.GetProblemManifestCount(c)
	if err != nil {
		util.ResponseError(c, errCode, err.Error(), http.StatusInternalServerError) // 假设通用错误返回500
		return
	}
	util.ResponseSuccess(c, resp)
} 

// SearchManifests godoc
// @Summary 多条件查询运单
// @Description 根据多种条件查询运单，支持按单号、时间范围、状态等过滤
// @Tags Manifests
// @Accept json
// @Produce json
// @Param query query string false "单号搜索(运单号、转单号、系统订单号、商家订单号)"
// @Param createTimeStart query string false "创建开始时间 (格式: 2006-01-02)"
// @Param createTimeEnd query string false "创建结束时间 (格式: 2006-01-02)"
// @Param pickUpTimeStart query string false "揽件开始时间 (格式: 2006-01-02)"
// @Param pickUpTimeEnd query string false "揽件结束时间 (格式: 2006-01-02)"
// @Param shipmentTimeStart query string false "发货开始时间 (格式: 2006-01-02)"
// @Param shipmentTimeEnd query string false "发货结束时间 (格式: 2006-01-02)"
// @Param deliveredTimeStart query string false "签收开始时间 (格式: 2006-01-02)"
// @Param deliveredTimeEnd query string false "签收结束时间 (格式: 2006-01-02)"
// @Param status query int false "运单状态"
// @Param userId query int false "用户ID"
// @Param masterBillId query int false "提单ID"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} util.Response{data=service.SearchManifestsResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/search [get]
// @Security ApiKeyAuth
func (h *ManifestHandler) SearchManifests(c *gin.Context) {
	var req service.SearchManifestsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.manifestService.SearchManifests(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// GetManifestsByExpressNumber 根据快递单号模糊查询运单
// @Summary 根据快递单号模糊查询运单
// @Description 根据快递单号进行模糊查询，支持分页
// @Tags Manifests
// @Accept json
// @Produce json
// @Param expressNumber query string true "快递单号（支持模糊查询）"
// @Param page query int false "页码" default(1)
// @Param pageSize query int false "每页数量" default(10)
// @Success 200 {object} util.Response{data=service.GetManifestsByExpressNumberResponse} "成功响应"
// @Failure 400 {object} util.Response "请求参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/by-express-number [get]
// @Security ApiKeyAuth
func (h *ManifestHandler) GetManifestsByExpressNumber(c *gin.Context) {
	var req service.GetManifestsByExpressNumberRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, code, err := h.manifestService.GetManifestsByExpressNumber(c, &req)
	if err != nil {
		httpStatus := http.StatusInternalServerError

		switch code {
		case valueobject.ERROR_INVALID_PARAMETER:
			httpStatus = http.StatusBadRequest
		}

		util.ResponseError(c, code, err.Error(), httpStatus)
		return
	}

	util.ResponseSuccess(c, resp)
}

// CreateOrder 外部系统创建订单
// @Summary 外部系统创建订单
// @Description 供外部系统对接的创建订单接口，支持订单号重复时的更新操作。如果商家订单号已存在且运单状态为预报(1)，则执行更新操作；如果状态不是预报，则返回错误。校验收件人信息并自动分配运单号。更新时保持原有运单号不变。
// @Tags External
// @Accept json
// @Produce json
// @Param request body service.CreateOrderRequest true "创建订单请求"
// @Success 200 {object} util.Response{data=service.CreateOrderResponse} "成功响应（创建或更新）"
// @Failure 400 {object} util.Response "请求参数错误（电话、邮编格式错误等）"
// @Failure 401 {object} util.Response "未授权"
// @Failure 409 {object} util.Response "资源冲突（如商家订单号已存在且不允许更新、单号不足等）"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /external/orders [post]
func (h *ManifestHandler) CreateOrder(c *gin.Context) {
	var req service.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数绑定错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	resp, errorCode, err := h.manifestService.CreateOrder(c, &req)
	if err != nil {
		switch errorCode {
		case valueobject.ERROR_MANIFEST_PHONE_FORMAT_INVALID:
			util.ResponseError(c, errorCode, "电话号码格式无效", http.StatusBadRequest)
		case valueobject.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID:
			util.ResponseError(c, errorCode, "邮编格式无效", http.StatusBadRequest)
		case valueobject.ERROR_MANIFEST_ZIPCODE_NOT_EXIST:
			util.ResponseError(c, errorCode, "该邮编不存在", http.StatusBadRequest)
		case valueobject.ERROR_TRACKING_CHANNEL_NOT_FOUND:
			util.ResponseError(c, errorCode, "未找到匹配的运单号渠道", http.StatusBadRequest)
		case valueobject.ERROR_TRACKING_CHANNEL_INACTIVE:
			util.ResponseError(c, errorCode, "运单号渠道未启用", http.StatusBadRequest)
		case valueobject.ERROR_INSUFFICIENT_TRACKING_NUMBERS:
			util.ResponseError(c, errorCode, "所选渠道可用单号不足", http.StatusConflict)
		case valueobject.ERROR_TRACKING_NUMBER_ALLOCATION_FAILED:
			util.ResponseError(c, errorCode, "单号分配失败", http.StatusInternalServerError)
		case valueobject.ERROR_MANIFEST_ORDER_CANNOT_UPDATE:
			util.ResponseError(c, errorCode, "订单不允许更新", http.StatusConflict)

		default:
			util.ResponseError(c, valueobject.ERROR_UNKNOWN, "创建订单失败", http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, resp)
}

// GenerateLabels 生成PDF面单
// @Summary 批量生成PDF面单
// @Description 根据一批运单号，查询订单信息并生成包含多个页面的PDF面单文件。
// @Tags Manifest
// @Accept json
// @Produce application/pdf
// @Param request body service.GenerateLabelsRequest true "生成面单请求"
// @Success 200 {file} file "PDF面单文件"
// @Failure 400 {object} util.Response "请求参数错误或未找到任何运单"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /manifests/labels [post]
func (h *ManifestHandler) GenerateLabels(c *gin.Context) {
	var req service.GenerateLabelsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, err.Error(), http.StatusBadRequest)
		return
	}

	pdfBytes, err := h.manifestService.GenerateLabels(c, &req)
	if err != nil {
		// 根据错误类型返回不同的HTTP状态码
		if err.Error() == "未找到任何有效的运单信息" {
			util.ResponseError(c, valueobject.ERROR_RESOURCE_NOT_FOUND, err.Error(), http.StatusBadRequest)
		} else {
			util.ResponseError(c, valueobject.ERROR_UNKNOWN, err.Error(), http.StatusInternalServerError)
		}
		return
	}

	// 设置响应头，让浏览器能够下载文件
	c.Header("Content-Disposition", `attachment; filename="labels.pdf"`)
	c.Data(http.StatusOK, "application/pdf", pdfBytes)
}