package valueobject

// 通用错误
const (
	SUCCESS                 = 100000 // 操作成功
	ERROR_UNKNOWN           = 100001 // 未知错误/系统繁忙
	ERROR_INVALID_PARAMETER = 100002 // 无效参数
	ERROR_PARAMETER_MISSING = 100003 // 缺少必要参数
	ERROR_UNAUTHORIZED      = 100004 // 未授权/未登录
	ERROR_FORBIDDEN_ACCESS  = 100005 // 无权限访问
	ERROR_RESOURCE_NOT_FOUND= 100006 // 请求的资源未找到
	ERROR_SERVICE_UNAVAILABLE = 100009 // 服务暂时不可用
)

// 用户与认证模块错误
const (
	ERROR_USER_NOT_FOUND         = 101001 // 用户不存在
	ERROR_USER_PASSWORD_WRONG    = 101002 // 用户名或密码错误
	ERROR_USER_ACCOUNT_LOCKED    = 101003 // 用户账户已锁定
	ERROR_USER_ACCOUNT_DISABLED  = 101004 // 用户账户已禁用
	ERROR_TOKEN_INVALID          = 101005 // Token 无效或已过期
	ERROR_USERNAME_ALREADY_EXISTS= 101006 // 用户名已存在
	ERROR_EMAIL_ALREADY_EXISTS   = 101007 // 邮箱已存在
)

// 运单模块 (shipments) (例如，201xxx)
const (
	ERROR_MANIFEST_NOT_FOUND                 = 201001 // 运单不存在
	ERROR_MANIFEST_STATUS_INVALID_OPERATION  = 201002 // 当前运单状态不允许此操作
	ERROR_MANIFEST_WEIGHT_INVALID            = 201003 // 运单重量无效 (如超出限制)
	ERROR_MANIFEST_DIMENSIONS_INVALID        = 201004 // 运单尺寸无效
	ERROR_MANIFEST_RECEIVER_ADDRESS_INVALID  = 201005 // 收件人地址信息不完整或无效
	ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID    = 201009 // 邮编格式无效
	ERROR_MANIFEST_ZIPCODE_NOT_EXIST         = 201010 // 邮编不存在于数据库中
	ERROR_MANIFEST_PHONE_FORMAT_INVALID      = 201011 // 电话号码格式无效
	ERROR_MANIFEST_ITEM_PROHIBITED           = 201006 // 运单包含违禁品
	ERROR_MANIFEST_ALREADY_EXISTS            = 201007 // 运单号已存在 (尝试创建重复时)
	ERROR_MANIFEST_UPDATE_NOT_ALLOWED        = 201008 // 运单信息不允许修改 (例如已发货)
	ERROR_MANIFEST_ORDER_CANNOT_UPDATE       = 201012 // 商家订单号已存在且状态不允许更新
)

// 单号池与分配模块 (tracking_number_pool, channels) (例如，202xxx)
const (
	ERROR_TRACKING_CHANNEL_NOT_FOUND         = 202001 // 未找到匹配的运单号渠道
	ERROR_TRACKING_CHANNEL_INACTIVE          = 202002 // 运单号渠道未启用
	ERROR_INSUFFICIENT_TRACKING_NUMBERS      = 202003 // 所选渠道可用单号不足
	ERROR_TRACKING_NUMBER_ALLOCATION_FAILED  = 202004 // 单号分配失败
)

// 问题工单模块 (例如，203xxx)
const (
	ERROR_PROBLEM_TICKET_NOT_FOUND                 = 203001 // 问题工单不存在
	ERROR_PROBLEM_TICKET_STATUS_INVALID_OPERATION  = 203002 // 当前工单状态不允许此操作
	ERROR_PROBLEM_TICKET_ALREADY_ASSIGNED          = 203003 // 工单已分配
	ERROR_PROBLEM_TICKET_ALREADY_RESOLVED          = 203004 // 工单已解决
) 