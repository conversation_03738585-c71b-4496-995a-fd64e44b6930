# 财务模块时间格式说明\n\n## 概述\n\n 财务模块中所有时间相关的字段都严格使用`yyyy-MM-dd HH:mm:ss`格式进行输入和输出。这确保了前后端时间数据的一致性和准确性。\n\n## 时间格式标准\n\n### 输入格式（前端 → 后端）\n- **格式**: `yyyy-MM-dd HH:mm:ss`\n- **示例**: `2024-12-19 14:30:00`\n- **时区**: 系统默认时区\n\n### 输出格式（后端 → 前端）\n- **格式**: `yyyy-MM-dd HH:mm:ss`\n- **示例**: `2024-12-19 14:30:00`\n- **时区**: 系统默认时区\n\n## 涉及的接口和字段\n\n### 1. 生成账单接口\n**接口**: `POST /api/v1/finance/billing/generate`\n\n**时间字段**:\n- `startTime`: 开始时间，格式：yyyy-MM-dd HH:mm:ss\n- `endTime`: 结束时间，格式：yyyy-MM-dd HH:mm:ss\n- `generalTemplate.createTime`: 普货模板创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `generalTemplate.updateTime`: 普货模板更新时间，格式：yyyy-MM-dd HH:mm:ss\n- `batteryTemplate.createTime`: 带电模板创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `batteryTemplate.updateTime`: 带电模板更新时间，格式：yyyy-MM-dd HH:mm:ss\n- `postBoxTemplate.createTime`: 投函模板创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `postBoxTemplate.updateTime`: 投函模板更新时间，格式：yyyy-MM-dd HH:mm:ss\n\n### 2. 查询用户运费模板接口\n**接口**: `GET /api/v1/finance/shipping-fee-templates/user/{userId}`\n\n**时间字段**:\n- `templates[].createTime`: 模板创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `templates[].updateTime`: 模板更新时间，格式：yyyy-MM-dd HH:mm:ss\n\n### 3. 查询账单明细列表接口\n**接口**: `GET /api/v1/finance/billing/records/{billingRecordId}/items`\n\n**时间字段**:\n- `list[].manifestCreateTime`: 运单创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].shipmentTime`: 发货时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].deliveredTime`: 签收时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].pickUpTime`: 揽件时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].createTime`: 记录创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].updateTime`: 记录更新时间，格式：yyyy-MM-dd HH:mm:ss\n\n### 4. 查询可生成账单的用户列表接口\n**接口**: `GET /api/v1/finance/billing/users`\n\n**时间字段**:\n- `startTime`: 查询开始时间，格式：yyyy-MM-dd HH:mm:ss\n- `endTime`: 查询结束时间，格式：yyyy-MM-dd HH:mm:ss\n\n### 5. 分页查询账单记录接口\n**接口**: `GET /api/v1/finance/billing/records`\n\n**时间字段**:\n- `list[].createTime`: 记录创建时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].updateTime`: 记录更新时间，格式：yyyy-MM-dd HH:mm:ss\n- `list[].paymentDate`: 支付时间，格式：yyyy-MM-dd HH:mm:ss\n\n## 技术实现\n\n### 自定义时间类型\n 项目使用自定义的`util.FormattedTime`类型来处理时间格式：\n\n`go\ntype FormattedTime struct {\n    time.Time\n}\n`\n\n### JSON 序列化/反序列化\n- **序列化**: 自动将`time.Time`转换为`yyyy-MM-dd HH:mm:ss`格式的字符串\n- **反序列化**: 自动将`yyyy-MM-dd HH:mm:ss`格式的字符串解析为`time.Time`\n\n### 数据库存储\n- 数据库中时间字段使用标准的`DATETIME`类型\n- GORM 自动处理`util.FormattedTime`与数据库时间类型的转换\n\n## 示例\n\n### 生成账单请求示例\n`json\n{\n  \"startTime\": \"2024-12-01 00:00:00\",\n  \"endTime\": \"2024-12-31 23:59:59\",\n  \"userId\": 123,\n  \"currency\": \"CNY\",\n  \"generalTemplate\": {\n    \"id\": 1,\n    \"name\": \"普通模板\",\n    \"type\": 1,\n    \"firstWeightPrice\": 15.0,\n    \"firstWeightRange\": 1.0,\n    \"continuedWeightPrice\": 5.0,\n    \"continuedWeightInterval\": 0.5,\n    \"bulkCoefficient\": 5000,\n    \"threeSidesStart\": 60.0,\n    \"createTime\": \"2024-01-01 10:00:00\",\n    \"updateTime\": \"2024-12-19 14:30:00\"\n  }\n}\n`\n\n### 查询用户运费模板响应示例\n`json\n{\n  \"success\": true,\n  \"errorCode\": 100000,\n  \"errorMessage\": \"操作成功\",\n  \"data\": {\n    \"userId\": 123,\n    \"templates\": [\n      {\n        \"id\": 1,\n        \"name\": \"普通模板\",\n        \"type\": 1,\n        \"typeName\": \"普通模板\",\n        \"firstWeightPrice\": 15.0,\n        \"firstWeightRange\": 1.0,\n        \"continuedWeightPrice\": 5.0,\n        \"continuedWeightInterval\": 0.5,\n        \"bulkCoefficient\": 5000,\n        \"threeSidesStart\": 60.0,\n        \"createTime\": \"2024-01-01 10:00:00\",\n        \"updateTime\": \"2024-12-19 14:30:00\"\n      }\n    ]\n  }\n}\n`\n\n## 注意事项\n\n1. **时间格式一致性**: 所有时间字段必须严格遵循`yyyy-MM-dd HH:mm:ss`格式\n2. **时区处理**: 目前使用系统默认时区，后续可根据需要支持时区转换\n3. **空值处理**: 可选的时间字段可以为`null`，必填的时间字段不能为空\n4. **前端解析**: 前端收到时间字符串后，可直接使用或转换为本地 Date 对象\n5. **参数验证**: 系统会自动验证时间格式的正确性，格式错误会返回 400 状态码\n\n## 错误处理\n\n### 时间格式错误\n 当提供的时间格式不正确时，系统会返回：\n`json\n{\n  \"success\": false,\n  \"errorCode\": 100002,\n  \"errorMessage\": \"时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式\",\n  \"data\": null\n}\n`\n\n### 时间范围错误\n 当时间范围不合理时（如结束时间早于开始时间），系统会返回：\n`json\n{\n  \"success\": false,\n  \"errorCode\": 100002,\n  \"errorMessage\": \"结束时间不能早于开始时间\",\n  \"data\": null\n}\n`
