package model

import (
	"time"
)

// BillingCyclePO 账期批次数据库模型
type BillingCyclePO struct {
	ID                        int64      `gorm:"column:id;primaryKey;autoIncrement"`
	CycleYear                 int        `gorm:"column:cycle_year;not null"`
	CycleMonth                int        `gorm:"column:cycle_month;not null"`
	CycleName                 *string    `gorm:"column:cycle_name"`
	Status                    string     `gorm:"column:status;default:PENDING;not null"`
	TotalCustomersBilled      *int       `gorm:"column:total_customers_billed"`
	TotalBillsGenerated       *int       `gorm:"column:total_bills_generated"`
	TotalBilledAmount         *float64   `gorm:"column:total_billed_amount;type:decimal(19,2)"`
	TotalAmountPaidInCycle    *float64   `gorm:"column:total_amount_paid_in_cycle;type:decimal(19,2)"`
	TotalBalanceDueInCycle    *float64   `gorm:"column:total_balance_due_in_cycle;type:decimal(19,2)"`
	GeneratedByUserID         *int64     `gorm:"column:generated_by_user_id"`
	GenerationStartTime       *time.Time `gorm:"column:generation_start_time"`
	GenerationEndTime         *time.Time `gorm:"column:generation_end_time"`
	Notes                     *string    `gorm:"column:notes;type:text"`
	CreateTime                time.Time  `gorm:"column:create_time;autoCreateTime"`
	UpdateTime                time.Time  `gorm:"column:update_time;autoUpdateTime"`
}

// TableName 指定表名
func (BillingCyclePO) TableName() string {
	return "billing_cycles"
} 