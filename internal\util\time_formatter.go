package util

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"
)

const (
	DefaultTimeFormat = "2006-01-02 15:04:05"
	ShortDateFormat   = "2006-01-02"
)

// FormattedTime 自定义时间类型，用于统一JSON输出格式
type FormattedTime struct {
	time.Time
}

// NewFormattedTime 从 time.Time 创建 FormattedTime
func NewFormattedTime(t time.Time) FormattedTime {
	return FormattedTime{Time: t}
}

// MarshalJSON 实现 json.Marshaler 接口
func (ft FormattedTime) MarshalJSON() ([]byte, error) {
	if ft.Time.IsZero() {
		return []byte("null"), nil // 如果时间是零值，则返回 null
	}
	formatted := fmt.Sprintf("\"%s\"", ft.Time.Format(DefaultTimeFormat))
	return []byte(formatted), nil
}

// UnmarshalJSON 实现 json.Unmarshaler 接口 (可选, 如果需要从JSON反序列化)
func (ft *FormattedTime) UnmarshalJSON(data []byte) error {
	str := strings.Trim(string(data), "\"")
	if str == "null" || str == "" {
		ft.Time = time.Time{}
		return nil
	}
	t, err := time.ParseInLocation(DefaultTimeFormat, str, time.Local)
	if err != nil {
		// 尝试解析短日期格式
		t, err = time.ParseInLocation(ShortDateFormat, str, time.Local)
		if err != nil {
			return err
		}
	}
	ft.Time = t
	return nil
}

// Value 实现 driver.Valuer 接口 (用于GORM写入数据库)
func (ft FormattedTime) Value() (driver.Value, error) {
	if ft.Time.IsZero() {
		return nil, nil
	}
	return ft.Time, nil
}

// Scan 实现 sql.Scanner 接口 (用于GORM从数据库读取)
func (ft *FormattedTime) Scan(value interface{}) error {
	if value == nil {
		ft.Time = time.Time{}
		return nil
	}
	if vt, ok := value.(time.Time); ok {
		ft.Time = vt
		return nil
	}
	return fmt.Errorf("can't convert %T to time.Time", value)
}

// IsZero 检查是否为零值
func (ft FormattedTime) IsZero() bool {
	return ft.Time.IsZero()
}

// PointerFormattedTime 指针版本的自定义时间类型，用于处理可能为nil的时间字段
type PointerFormattedTime struct {
	*time.Time
}

// NewPointerFormattedTime 从 *time.Time 创建 PointerFormattedTime
func NewPointerFormattedTime(t *time.Time) PointerFormattedTime {
	return PointerFormattedTime{Time: t}
}

// MarshalJSON 实现 json.Marshaler 接口
func (pft PointerFormattedTime) MarshalJSON() ([]byte, error) {
	if pft.Time == nil || pft.Time.IsZero() {
		return []byte("null"), nil
	}
	formatted := fmt.Sprintf("\"%s\"", pft.Time.Format(DefaultTimeFormat))
	return []byte(formatted), nil
}

// UnmarshalJSON 实现 json.Unmarshaler 接口
func (pft *PointerFormattedTime) UnmarshalJSON(data []byte) error {
	str := strings.Trim(string(data), "\"")
	if str == "null" || str == "" {
		pft.Time = nil
		return nil
	}
	t, err := time.ParseInLocation(DefaultTimeFormat, str, time.Local)
	if err != nil {
		// 尝试解析短日期格式
		t, err = time.ParseInLocation(ShortDateFormat, str, time.Local)
		if err != nil {
			return err
		}
	}
	pft.Time = &t
	return nil
}

// Value 实现 driver.Valuer 接口
func (pft PointerFormattedTime) Value() (driver.Value, error) {
	if pft.Time == nil {
		return nil, nil
	}
	return *pft.Time, nil
}

// Scan 实现 sql.Scanner 接口
func (pft *PointerFormattedTime) Scan(value interface{}) error {
	if value == nil {
		pft.Time = nil
		return nil
	}
	if vt, ok := value.(time.Time); ok {
		pft.Time = &vt
		return nil
	}
	return fmt.Errorf("can't convert %T to *time.Time", value)
}

// IsZero 检查内部时间是否为 nil 或零值
func (pft PointerFormattedTime) IsZero() bool {
	return pft.Time == nil || pft.Time.IsZero()
}
