# 财务模块 - 账单生成任务详情接口

## 概述

新增了根据任务 ID 获取账单生成任务详情的接口，提供比任务列表更加详细的信息，包括目标客户列表解析、任务状态判断、进度描述等扩展信息。

## API 接口

### 获取任务详情

**接口地址：** `GET /finance/billing/generation-tasks/{taskId}`

**功能说明：** 根据任务 ID 获取账单生成任务的详细信息，包含任务状态、进度、客户列表、提交者信息等

**请求参数：**

| 参数名 | 类型   | 位置 | 必填 | 说明                 |
| ------ | ------ | ---- | ---- | -------------------- |
| taskId | string | path | 是   | 任务 ID（UUID 格式） |

**请求示例：**

```bash
# 获取任务详情
curl -X GET "http://localhost:8080/finance/billing/generation-tasks/123e4567-e89b-12d3-a456-************" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json"
```

**响应数据结构：**

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "task": {
      "taskId": "123e4567-e89b-12d3-a456-************",
      "billingCycleId": 1,
      "targetCustomerIds": "1001,1002,1003",
      "targetCustomerList": [1001, 1002, 1003],
      "status": "COMPLETED",
      "statusName": "已完成",
      "progressPercentage": 100,
      "totalItemsToProcess": 3,
      "itemsProcessedCount": 3,
      "errorMessage": null,
      "submittedByUserId": 1,
      "submittedByNickname": "张三",
      "submittedByUsername": "zhangsan",
      "submitTime": "2024-01-15 10:30:00",
      "startTime": "2024-01-15 10:30:05",
      "endTime": "2024-01-15 10:32:30",
      "duration": 145,
      "isCompleted": true,
      "isFailed": false,
      "isProcessing": false,
      "isPending": false,
      "customerCount": 3,
      "progressDescription": "处理完成，共处理 3 项，耗时 145 秒"
    }
  },
  "requestId": "req_xxx",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

**响应字段说明：**

| 字段名              | 类型    | 说明                                          |
| ------------------- | ------- | --------------------------------------------- |
| taskId              | string  | 任务 UUID                                     |
| billingCycleId      | int64   | 关联的账期批次 ID                             |
| targetCustomerIds   | string  | 目标客户 ID 列表（逗号分隔字符串）            |
| targetCustomerList  | []int64 | 解析后的目标客户 ID 列表                      |
| status              | string  | 任务状态：PENDING/PROCESSING/COMPLETED/FAILED |
| statusName          | string  | 任务状态中文名称                              |
| progressPercentage  | int     | 进度百分比（0-100）                           |
| totalItemsToProcess | int     | 预计总处理项数                                |
| itemsProcessedCount | int     | 已处理项数                                    |
| errorMessage        | string  | 错误信息（失败时）                            |
| submittedByUserId   | int64   | 任务提交者 ID                                 |
| submittedByNickname | string  | 任务提交者昵称                                |
| submittedByUsername | string  | 任务提交者用户名                              |
| submitTime          | string  | 提交时间（yyyy-MM-dd HH:mm:ss）               |
| startTime           | string  | 处理开始时间                                  |
| endTime             | string  | 处理结束时间                                  |
| duration            | int64   | 执行耗时（秒）                                |
| isCompleted         | bool    | 是否已完成                                    |
| isFailed            | bool    | 是否失败                                      |
| isProcessing        | bool    | 是否正在处理                                  |
| isPending           | bool    | 是否待处理                                    |
| customerCount       | int     | 客户数量                                      |
| progressDescription | string  | 进度描述                                      |

## 进度描述规则

系统会根据任务状态自动生成进度描述：

- **待处理：** "任务已提交，等待处理"
- **处理中：** "正在处理中，已完成 X/Y 项（Z%）" 或 "正在处理中，进度 Z%"
- **已完成：** "处理完成，共处理 X 项，耗时 Y 秒" 或 "处理完成，共处理 X 项"
- **失败：** "处理失败：{错误信息}" 或 "处理失败"

## 状态判断字段

为了便于前端处理，提供了四个布尔类型的状态判断字段：

- `isCompleted`：任务是否已完成
- `isFailed`：任务是否失败
- `isProcessing`：任务是否正在处理
- `isPending`：任务是否待处理

## 错误响应

### 400 - 参数错误

```json
{
  "success": false,
  "errorCode": 100002,
  "errorMessage": "任务ID不能为空",
  "data": null,
  "requestId": "req_xxx",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### 404 - 任务不存在

```json
{
  "success": false,
  "errorCode": 100006,
  "errorMessage": "账单生成任务不存在",
  "data": null,
  "requestId": "req_xxx",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

### 500 - 服务器错误

```json
{
  "success": false,
  "errorCode": 100001,
  "errorMessage": "查询任务详情失败",
  "data": null,
  "requestId": "req_xxx",
  "timestamp": "2024-01-15T10:35:00Z"
}
```

## 使用场景

### 1. 任务监控

前端可以通过这个接口实时获取任务的详细状态，包括：

- 任务执行进度
- 客户处理情况
- 错误信息
- 执行耗时

### 2. 任务管理

管理员可以查看：

- 任务提交者信息
- 目标客户列表
- 任务执行时间线
- 详细的状态描述

### 3. 故障排查

当任务失败时，可以获取：

- 详细的错误信息
- 任务执行的时间节点
- 已处理的客户数量

## 实现特点

### 1. 数据完整性

- 自动解析目标客户 ID 字符串为数组
- 提供状态的布尔判断字段
- 自动计算执行耗时和进度描述

### 2. 容错机制

- 用户信息查询失败不影响主流程
- 客户 ID 解析失败返回空数组
- 所有时间字段都有空值处理

### 3. 扩展性

- 详情 DTO 包含了比列表 DTO 更多的字段
- 进度描述可以根据业务需求调整
- 支持未来添加更多状态判断

## 数据库依赖

接口需要查询以下表：

- `billing_generation_tasks`：任务基础信息
- `tb_user`：提交者用户信息

## 权限要求

需要 JWT 认证，通过`@Security ApiKeyAuth`注解控制访问权限。

## 性能考虑

- 单条记录查询，性能良好
- 用户信息查询通过 ID 索引，效率较高
- 客户 ID 解析在内存中进行，速度较快

## 相关接口

- [查询任务列表](./财务模块_账单生成任务管理接口实现.md)：`GET /finance/billing/generation-tasks`
- [异步生成账单](./财务模块_异步账单生成功能实现.md)：`POST /finance/billing/async-generate`
