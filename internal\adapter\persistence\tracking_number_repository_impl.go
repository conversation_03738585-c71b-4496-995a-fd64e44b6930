package persistence

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// TrackingNumberRepositoryImpl TrackingNumber仓储实现
type TrackingNumberRepositoryImpl struct {
	db *gorm.DB
}

// NewTrackingNumberRepository 创建TrackingNumber仓储实例
func NewTrackingNumberRepository(db *gorm.DB) repository.TrackingNumberRepository {
	return &TrackingNumberRepositoryImpl{db: db}
}

// GetChannelByShipmentType 根据货物类型获取渠道
func (r *TrackingNumberRepositoryImpl) GetChannelByShipmentType(ctx context.Context, locationID int64, shipmentTypeID int64) (*entity.TrackingNumberChannel, error) {
	var channel entity.TrackingNumberChannel
	err := r.db.WithContext(ctx).
		Where("location_id = ? AND shipment_type_id = ? AND is_active = ?", locationID, shipmentTypeID, true).
		First(&channel).Error
	
	if err != nil {
		return nil, err
	}
	
	return &channel, nil
}

// AllocateTrackingNumber 分配一个可用的运单号
func (r *TrackingNumberRepositoryImpl) AllocateTrackingNumber(ctx context.Context, channelID int64) (*entity.TrackingNumberPool, error) {
	var trackingNumber entity.TrackingNumberPool
	
	// 在事务中查找并更新一个可用的单号
	err := r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查找一个可用的单号
		if err := tx.Where("channel_id = ? AND status = ?", channelID, entity.TrackingNumberStatusAvailable).
			First(&trackingNumber).Error; err != nil {
			return err
		}
		
		// 将状态更新为已分配
		if err := tx.Model(&trackingNumber).
			Update("status", entity.TrackingNumberStatusAllocated).Error; err != nil {
			return err
		}
		
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	return &trackingNumber, nil
} 