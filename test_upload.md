# 文件上传功能测试说明

## 功能概述

已成功实现阿里云 OSS 图片上传功能，支持赔偿类财务调整中的货值证明图片上传。

## API 接口

### 1. 上传图片

- **接口**: `POST /api/v1/upload/image`
- **认证**: 需要 JWT Token
- **请求类型**: `multipart/form-data`
- **参数**:
  - `file`: 图片文件 (必需)
  - `businessType`: 业务类型 (必需)
    - `compensation_proof`: 赔偿证明
    - `adjustment_proof`: 调整证明

**示例请求**:

```bash
curl -X POST "http://localhost:8080/api/v1/upload/image" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@/path/to/image.jpg" \
  -F "businessType=compensation_proof"
```

**成功响应**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "uuid-string",
  "timestamp": "2024-01-01 12:00:00",
  "data": {
    "fileUrl": "https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/compensation/proofs/2024/01/01/uuid.jpg",
    "fileName": "compensation/proofs/2024/01/01/uuid.jpg",
    "fileSize": 1024000,
    "uploadTime": "2024-01-01 12:00:00"
  }
}
```

### 2. 删除图片

- **接口**: `DELETE /api/v1/upload/image?fileUrl=图片URL`
- **认证**: 需要 JWT Token

**示例请求**:

```bash
curl -X DELETE "http://localhost:8080/api/v1/upload/image?fileUrl=https://zebra-logistics-files.oss-cn-fuzhou.aliyuncs.com/compensation/proofs/2024/01/01/uuid.jpg" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 获取上传配置信息

- **接口**: `GET /api/v1/upload/info`
- **认证**: 需要 JWT Token

**示例请求**:

```bash
curl -X GET "http://localhost:8080/api/v1/upload/info" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 文件限制

- **支持格式**: jpg, jpeg, png, gif, bmp, webp
- **最大文件大小**: 10MB
- **存储路径规则**: `业务类型/年/月/日/UUID.扩展名`

## 存储路径示例

- 赔偿证明: `compensation/proofs/2024/01/01/uuid.jpg`
- 调整证明: `adjustments/proofs/2024/01/01/uuid.png`

## 配置信息

OSS 配置已在 `configs/config.yaml` 中设置：

```yaml
oss:
  access_key_id: "LTAI5tSZs1KNc1RTk2LjaJFJ"
  access_key_secret: "******************************"
  endpoint: "https://oss-cn-fuzhou.aliyuncs.com"
  region: "cn-fuzhou"
  bucket_name: "zebra-logistics-files"
```

## 使用流程

1. **前端上传图片**: 调用上传接口，获得图片 URL
2. **创建赔偿调整**: 在创建赔偿类财务调整时，将图片 URL 填入 `ProofOfValueImageUrls` 字段
3. **图片管理**: 可以通过删除接口删除不需要的图片

## 安全考虑

- 所有接口都需要 JWT 认证
- 文件类型和大小验证
- 使用 UUID 生成唯一文件名，避免冲突
- 按日期分目录存储，便于管理

## 错误处理

- 文件格式不支持: 返回 400 错误
- 文件大小超限: 返回 400 错误
- OSS 服务异常: 返回 500 错误
- 认证失败: 返回 401 错误
