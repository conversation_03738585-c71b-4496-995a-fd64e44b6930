# 财务模块 - Excel 零值隐藏功能修改说明

## 修改概述

根据用户需求，对财务模块 Excel 导出功能进行优化，实现**数字为 0 时不显示**的功能。这样可以让 Excel 表格更加简洁，突出有实际数值的字段。

## 修改范围

### 1. 新增辅助函数

在 `internal/domain/service/impl/finance_service_impl.go` 中新增辅助函数：

```go
// setCellNumberValue 设置数值单元格，如果值为0则不显示
func setCellNumberValue(f *excelize.File, sheetName, cell string, value float64) {
	if value == 0 {
		// 当数值为0时，不设置单元格值，保持空白
		return
	}
	f.SetCellValue(sheetName, cell, value)
}
```

### 2. 运费账单导出功能

#### 影响的字段

在 `generateExcel` 函数中，以下数值字段应用了零值隐藏：

**运单明细数值字段：**

- 重量 (Weight)
- 长度 (Length)
- 宽度 (Width)
- 高度 (Height)
- 三边和 (ThreeSidesSum)
- 体积重 (VolumeWeight)
- 计费重量 (ChargeableWeight)
- 首重费用 (FirstWeightPrice)
- 续重费用 (ContinuedPrice)
- 超长费 (OverLengthSurcharge)
- 偏远费 (RemoteAreaSurcharge)
- 超重费 (OtherCost)
- 总费用 (TotalPrice)

#### 修改示例

**修改前：**

```go
f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), item.Weight) // 重量
```

**修改后：**

```go
setCellNumberValue(f, sheetName, fmt.Sprintf("J%d", row), item.Weight) // 重量(如果为0则不显示)
```

### 3. 账单记录导出功能

#### 影响的字段

在 `generateBillingRecordExcel` 函数中，同样应用了零值隐藏：

**运单明细部分：**

- 所有物理尺寸和重量字段
- 所有费用字段
- 总金额字段

**其他费用明细部分：**

- 运单相关的物理参数字段
- 调整金额字段

#### 具体应用范围

1. **运单明细行：** 列 L-R（重量、尺寸字段）和 S-W、Y（费用字段）
2. **其他费用明细行：** 列 L-R（运单物理参数）和 Y（调整金额）

## 功能特点

### 1. 视觉效果优化

- **简洁性提升：** 表格中不再显示大量的 "0" 值，视觉更简洁
- **数据突出：** 有实际数值的字段更加突出，便于快速识别
- **可读性增强：** 减少无意义信息，提高表格可读性

### 2. 保持数据完整性

- **样式保持：** 隐藏 0 值不影响单元格样式设置
- **公式兼容：** 空白单元格在 Excel 中参与计算时默认为 0
- **数据类型：** 保持数值单元格的数据类型不变

### 3. 适用场景

- **运费明细：** 某些费用项可能为 0（如无超长费、偏远费等）
- **物理参数：** 部分商品可能缺少某些尺寸信息
- **调整费用：** 调整金额可能为 0 的情况

## 实现逻辑

### 判断条件

```go
if value == 0 {
    // 不设置单元格值，保持空白
    return
}
```

### 优势说明

1. **简单高效：** 直接通过浮点数比较判断
2. **无副作用：** 不影响现有的样式和格式设置
3. **兼容性好：** 与 Excel 的默认行为一致

## 用户体验改进

### 导出前

```
重量: 1.5kg    长: 0cm     宽: 0cm     高: 0cm
首重: 15.00元  续重: 0.00元 超长: 0.00元 偏远: 0.00元
```

### 导出后

```
重量: 1.5kg    长:         宽:         高:
首重: 15.00元  续重:       超长:       偏远:
```

## 注意事项

### 1. 数据完整性

- 零值隐藏不会影响数据的完整性和准确性
- Excel 中的空白单元格在计算时会被视为 0

### 2. 样式一致性

- 隐藏零值的单元格仍然保持原有的边框、背景色等样式
- 不影响整体表格的视觉布局

### 3. 兼容性

- 功能与所有 Excel 版本兼容
- 不影响其他非数值字段的显示

## 版本信息

- **修改日期：** 2024-01-15
- **版本：** v2.3.0
- **修改类型：** 功能优化
- **影响范围：** 所有 Excel 导出功能的数值字段

## 相关接口

- `/api/v1/finance/shipping-bill/export` - 运费账单导出
- `/api/v1/finance/billing-record/export` - 账单记录导出

## 测试建议

1. **零值验证：** 测试包含大量 0 值的数据导出效果
2. **混合数据：** 测试既有 0 值又有非 0 值的混合数据
3. **样式检查：** 确认隐藏 0 值后样式仍然正确应用
4. **计算验证：** 验证合计行的计算结果不受影响
