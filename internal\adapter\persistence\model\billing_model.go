package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// AppliedFreightTemplatesJSON 应用的运费模板信息JSON类型
type AppliedFreightTemplatesJSON map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AppliedFreightTemplatesJSON) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AppliedFreightTemplatesJSON) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, a)
}

// AdditionalDetailsJSON 附加详情JSON类型
type AdditionalDetailsJSON map[string]interface{}

// Value 实现 driver.Valuer 接口
func (a AdditionalDetailsJSON) Value() (driver.Value, error) {
	if a == nil {
		return nil, nil
	}
	return json.Marshal(a)
}

// Scan 实现 sql.Scanner 接口
func (a *AdditionalDetailsJSON) Scan(value interface{}) error {
	if value == nil {
		*a = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, a)
}

// BillingRecordPO 账单主记录数据库模型
type BillingRecordPO struct {
	ID                       int64                        `gorm:"column:id;primaryKey;autoIncrement"`
	BillNumber               string                       `gorm:"column:bill_number;uniqueIndex;not null"`
	CustomerAccountID        int64                        `gorm:"column:customer_account_id;not null"`
	BillingCycleID           int64                        `gorm:"column:billing_cycle_id;not null"`
	BillDate                 time.Time                    `gorm:"column:bill_date;not null"`
	DueDate                  *time.Time                   `gorm:"column:due_date"`
	BillingPeriodStart       time.Time                    `gorm:"column:billing_period_start;not null"`
	BillingPeriodEnd         time.Time                    `gorm:"column:billing_period_end;not null"`
	AppliedFreightTemplates  *AppliedFreightTemplatesJSON `gorm:"column:applied_freight_templates;type:json"`
	FreightChargesTotal      float64                      `gorm:"column:freight_charges_total;type:decimal(19,2);default:0.00;not null"`
	AdjustmentChargesTotal   float64                      `gorm:"column:adjustment_charges_total;type:decimal(19,2);default:0.00;not null"`
	TotalAmount              float64                      `gorm:"column:total_amount;type:decimal(19,2);default:0.00;not null"`
	AmountPaid               float64                      `gorm:"column:amount_paid;type:decimal(19,2);default:0.00;not null"`
	Currency                 string                       `gorm:"column:currency;default:CNY;not null"`
	Status                   string                       `gorm:"column:status;default:UNPAID;not null"`
	PaymentMethod            *string                      `gorm:"column:payment_method"`
	PaymentTransactionID     *string                      `gorm:"column:payment_transaction_id"`
	PaymentDate              *time.Time                   `gorm:"column:payment_date"`
	Notes                    *string                      `gorm:"column:notes;type:text"`
	GeneratedByUserID        *int64                       `gorm:"column:generated_by_user_id"`
	CreateTime               time.Time                    `gorm:"column:create_time;autoCreateTime"`
	UpdateTime               time.Time                    `gorm:"column:update_time;autoUpdateTime"`
}

// TableName 指定表名
func (BillingRecordPO) TableName() string {
	return "billing_records"
}

// BillingRecordItemPO 账单明细数据库模型
type BillingRecordItemPO struct {
	ID                        int64      `gorm:"column:id;primaryKey;autoIncrement"`
	BillingRecordID           int64      `gorm:"column:billing_record_id;not null"`
	ManifestID                *int64     `gorm:"column:manifest_id"`
	ExpressNumber             *string    `gorm:"column:express_number"`
	OrderNo                   *string    `gorm:"column:order_no"`
	TransferredTrackingNumber *string    `gorm:"column:transferred_tracking_number"`
	OrderNumber               *string    `gorm:"column:order_number"`
	ManifestCreateTime        *time.Time `gorm:"column:manifest_create_time"`
	ShipmentTime              *time.Time `gorm:"column:shipment_time"`
	ReceiverName              *string    `gorm:"column:receiver_name"`
	ItemDescription           string     `gorm:"column:item_description;not null"`
	CargoType                 int        `gorm:"column:cargo_type;not null"`
	Weight                    *float64   `gorm:"column:weight;type:decimal(19,2)"`
	Length                    *float64   `gorm:"column:length;type:decimal(19,2)"`
	Width                     *float64   `gorm:"column:width;type:decimal(19,2)"`
	Height                    *float64   `gorm:"column:height;type:decimal(19,2)"`
	SumOfSides                *float64   `gorm:"column:sum_of_sides;type:decimal(19,2)"`
	DimensionalWeight         *float64   `gorm:"column:dimensional_weight;type:decimal(19,2)"`
	ChargeableWeight          *float64   `gorm:"column:chargeable_weight;type:decimal(19,2)"`
	BaseFreightFee            *float64   `gorm:"column:base_freight_fee;type:decimal(19,2)"`
	FirstWeightFee            *float64   `gorm:"column:first_weight_fee;type:decimal(19,2)"`
	ContinuedWeightFee        *float64   `gorm:"column:continued_weight_fee;type:decimal(19,2)"`
	OverLengthSurcharge       *float64   `gorm:"column:over_length_surcharge;type:decimal(19,2)"`
	RemoteAreaSurcharge       *float64   `gorm:"column:remote_area_surcharge;type:decimal(19,2)"`
	OverweightSurcharge       *float64   `gorm:"column:overweight_surcharge;type:decimal(19,2)"`
	ItemTotalAmount           float64    `gorm:"column:item_total_amount;type:decimal(19,2);not null"`
	CreateTime                time.Time  `gorm:"column:create_time;autoCreateTime"`
	UpdateTime                time.Time  `gorm:"column:update_time;autoUpdateTime"`
}

// TableName 指定表名
func (BillingRecordItemPO) TableName() string {
	return "billing_record_items"
}

// BillingFinancialAdjustmentSnapshotPO 账单中财务调整的快照数据库模型
type BillingFinancialAdjustmentSnapshotPO struct {
	ID                   int64                  `gorm:"column:id;primaryKey;autoIncrement"`
	BillingRecordID      int64                  `gorm:"column:billing_record_id;uniqueIndex;not null"`
	OriginalAdjustmentID int64                  `gorm:"column:original_adjustment_id;not null"`
	
	// 财务调整信息快照
	AdjustmentType       string                 `gorm:"column:adjustment_type;not null"`
	Description          *string                `gorm:"column:description;type:text"`
	AdditionalDetails    *AdditionalDetailsJSON `gorm:"column:additional_details;type:json"`
	Amount               float64                `gorm:"column:amount;type:decimal(19,2);not null"`
	Currency             string                 `gorm:"column:currency;not null"`
	EffectiveDate        time.Time              `gorm:"column:effective_date;type:date;not null"`
	
	// 关联运单信息快照
	ManifestID                      *int64     `gorm:"column:manifest_id"`
	ManifestExpressNumber           *string    `gorm:"column:manifest_express_number"`
	ManifestOrderNo                 *string    `gorm:"column:manifest_order_no"`
	ManifestTransferredTrackingNumber *string  `gorm:"column:manifest_transferred_tracking_number"`
	ManifestCustomerOrderNumber     *string    `gorm:"column:manifest_customer_order_number"`
	ManifestCreateTime              *time.Time `gorm:"column:manifest_create_time"`
	ManifestShipmentTime            *time.Time `gorm:"column:manifest_shipment_time"`
	ManifestReceiverName            *string    `gorm:"column:manifest_receiver_name"`
	ManifestItemDescription         *string    `gorm:"column:manifest_item_description"`
	ManifestCargoType               *string    `gorm:"column:manifest_cargo_type"`
	
	// 运单尺寸重量信息快照
	ManifestWeight              *float64 `gorm:"column:manifest_weight;type:decimal(19,2)"`
	ManifestLength              *float64 `gorm:"column:manifest_length;type:decimal(19,2)"`
	ManifestWidth               *float64 `gorm:"column:manifest_width;type:decimal(19,2)"`
	ManifestHeight              *float64 `gorm:"column:manifest_height;type:decimal(19,2)"`
	ManifestSumOfSides          *float64 `gorm:"column:manifest_sum_of_sides;type:decimal(19,2)"`
	ManifestDimensionalWeight   *float64 `gorm:"column:manifest_dimensional_weight;type:decimal(19,2)"`
	ManifestChargeableWeight    *float64 `gorm:"column:manifest_chargeable_weight;type:decimal(19,2)"`
	
	// 快照创建时间
	SnapshotTime         time.Time `gorm:"column:snapshot_time;autoCreateTime"`
}

// TableName 指定表名
func (BillingFinancialAdjustmentSnapshotPO) TableName() string {
	return "billing_financial_adjustment_snapshots"
} 