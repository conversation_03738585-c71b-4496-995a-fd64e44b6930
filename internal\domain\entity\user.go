package entity

import (
	"zebra-hub-system/internal/util"
)

// User 用户实体
type User struct {
	ID          int64                    `json:"id"`
	Username    string                   `json:"username"`
	Password    string                   `json:"-"` // 密码不返回给前端
	OpenID      string                   `json:"openId"`      // 原 open_id
	SocialType  int                      `json:"socialType"`  // 原 social_type
	Nickname    string                   `json:"nickname"`
	Remark      string                   `json:"remark"`
	City        string                   `json:"city"`
	Province    string                   `json:"province"`
	Country     string                   `json:"country"`
	Email       string                   `json:"email"`
	Phone       string                   `json:"phone"`
	Gender      int                      `json:"gender"`
	Avatar      string                   `json:"avatar"`
	Status      int                      `json:"status"`
	RoleID      int64                    `json:"roleId"`      // 原 role_id
	LoginIP     string                   `json:"loginIp"`     // 原 login_ip
	LoginDate   util.PointerFormattedTime `json:"loginDate"`   // 使用 PointerFormattedTime
	CreateTime  util.PointerFormattedTime `json:"createTime"`  // 使用 PointerFormattedTime
	UpdateTime  util.PointerFormattedTime `json:"updateTime"`  // 使用 PointerFormattedTime
} 

// UserWithProblemCount 带有问题运单数量的用户信息
type UserWithProblemCount struct {
	ID           int64  `json:"id"`           // 用户ID
	Username     string `json:"username"`     // 用户名
	Nickname     string `json:"nickname"`     // 昵称
	PendingCount int64  `json:"pendingCount"` // 待处理问题运单数量
} 