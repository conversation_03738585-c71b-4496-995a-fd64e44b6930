package entity

import (
	"time"
)

// TaskStatus 任务状态枚举
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "PENDING"    // 待处理
	TaskStatusProcessing TaskStatus = "PROCESSING" // 处理中
	TaskStatusCompleted  TaskStatus = "COMPLETED"  // 已完成
	TaskStatusFailed     TaskStatus = "FAILED"     // 失败
)

// BillingGenerationTask 账单生成任务实体
type BillingGenerationTask struct {
	TaskID                string     `json:"taskId"`                // 任务UUID
	BillingCycleID        int64      `json:"billingCycleId"`        // 关联的账期批次ID
	TargetCustomerIds     *string    `json:"targetCustomerIds"`     // 目标客户ID列表（逗号分隔或JSON数组）
	Status                TaskStatus `json:"status"`                // 任务状态
	ProgressPercentage    int        `json:"progressPercentage"`    // 进度百分比（0-100）
	TotalItemsToProcess   *int       `json:"totalItemsToProcess"`   // 预计总处理项数
	ItemsProcessedCount   int        `json:"itemsProcessedCount"`   // 已处理项数
	ErrorMessage          *string    `json:"errorMessage"`          // 错误信息
	SubmittedByUserID     *int64     `json:"submittedByUserId"`     // 任务提交者ID
	SubmitTime            time.Time  `json:"submitTime"`            // 提交时间
	StartTime             *time.Time `json:"startTime"`             // 处理开始时间
	EndTime               *time.Time `json:"endTime"`               // 处理结束时间
}

// IsCompleted 检查任务是否已完成
func (t *BillingGenerationTask) IsCompleted() bool {
	return t.Status == TaskStatusCompleted
}

// IsFailed 检查任务是否失败
func (t *BillingGenerationTask) IsFailed() bool {
	return t.Status == TaskStatusFailed
}

// IsProcessing 检查任务是否正在处理
func (t *BillingGenerationTask) IsProcessing() bool {
	return t.Status == TaskStatusProcessing
}

// IsPending 检查任务是否待处理
func (t *BillingGenerationTask) IsPending() bool {
	return t.Status == TaskStatusPending
}

// GetStatusName 获取任务状态中文名称
func (t *BillingGenerationTask) GetStatusName() string {
	switch t.Status {
	case TaskStatusPending:
		return "待处理"
	case TaskStatusProcessing:
		return "处理中"
	case TaskStatusCompleted:
		return "已完成"
	case TaskStatusFailed:
		return "失败"
	default:
		return "未知状态"
	}
}

// GetDuration 获取任务执行耗时（秒）
func (t *BillingGenerationTask) GetDuration() *int64 {
	if t.StartTime == nil {
		return nil
	}
	
	endTime := t.EndTime
	if endTime == nil && (t.IsProcessing() || t.IsPending()) {
		// 如果任务还在进行中，使用当前时间
		now := time.Now()
		endTime = &now
	}
	
	if endTime != nil {
		duration := endTime.Sub(*t.StartTime).Seconds()
		durationInt := int64(duration)
		return &durationInt
	}
	
	return nil
} 