package entity

import (
	"zebra-hub-system/internal/util"
)

// Tracking 物流轨迹实体
type Tracking struct {
	ID         int64                     `json:"id" gorm:"column:id;primary_key"`
	ManifestID int64                     `json:"manifestId" gorm:"column:manifest_id"`
	OperatorID int64                     `json:"operatorId" gorm:"column:operator_id"`
	Status     int                       `json:"status" gorm:"column:status"`
	Track      string                    `json:"track" gorm:"column:track"`
	Place      string                    `json:"place" gorm:"column:place"`
	Time       util.PointerFormattedTime `json:"time" gorm:"column:time"`
	CreateTime util.PointerFormattedTime `json:"createTime" gorm:"column:create_time"`
	UpdateTime util.PointerFormattedTime `json:"updateTime" gorm:"column:update_time"`
}

// TableName 指定表名
func (Tracking) TableName() string {
	return "tb_tracking"
} 