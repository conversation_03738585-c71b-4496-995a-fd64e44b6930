package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// UserConfiguredTemplate 用户配置的模板信息（包含type）
type UserConfiguredTemplate struct {
	Template *entity.ShippingFeeTemplate `json:"template"` // 模板信息
	Type     int                         `json:"type"`     // 模板类型
}

// ShippingFeeTemplateRepository 运费模板仓库接口
type ShippingFeeTemplateRepository interface {
	// GetTemplateByID 根据ID获取运费模板
	GetTemplateByID(ctx context.Context, id int64) (entity.ShippingFeeTemplate, error)
	

	
	// GetDefaultUserTemplateID 获取用户默认模板ID（通常为普通模板，type=1）
	GetDefaultUserTemplateID(ctx context.Context, userID int64) (int64, error)
	
	// GetUserAllTemplates 根据用户ID获取所有类型的运费模板
	GetUserAllTemplates(ctx context.Context, userID int64) ([]*entity.ShippingFeeTemplate, error)
	

	
	// GetTemplatesWithSearch 查询运费模板，支持模板名模糊搜索
	GetTemplatesWithSearch(ctx context.Context, name string) ([]*entity.ShippingFeeTemplate, error)
	
	// CreateTemplate 创建运费模板
	CreateTemplate(ctx context.Context, template *entity.ShippingFeeTemplate) error
	
	// UpdateTemplate 更新运费模板
	UpdateTemplate(ctx context.Context, template *entity.ShippingFeeTemplate) error
	
	// DeleteTemplate 删除运费模板
	DeleteTemplate(ctx context.Context, id int64) error
	
	// CheckTemplateExists 检查模板是否存在
	CheckTemplateExists(ctx context.Context, id int64) (bool, error)
	
	// CheckTemplateNameExists 检查模板名称是否已存在
	CheckTemplateNameExists(ctx context.Context, name string) (bool, error)
	
	// CheckTemplateNameExistsExcludeID 检查模板名称是否已存在（排除指定ID的模板）
	CheckTemplateNameExistsExcludeID(ctx context.Context, name string, excludeID int64) (bool, error)
	
	// CheckTemplateInUse 检查模板是否被用户使用
	CheckTemplateInUse(ctx context.Context, templateID int64) (bool, error)
	
	// GetUserConfiguredTemplates 根据用户ID获取其已配置的运费模板（包含type信息）
	GetUserConfiguredTemplates(ctx context.Context, userID int64) ([]*UserConfiguredTemplate, error)
	
	// UpdateUserTemplateConfigurations 更新用户的模板配置
	UpdateUserTemplateConfigurations(ctx context.Context, userID int64, templateConfigs []*entity.ShippingFeeTemplateUser) error
} 