# 财务模块 - 账单生成任务管理接口实现

## 概述

根据用户需求，为账单生成任务表 `billing_generation_tasks` 添加了根据账期批次和当前登录用户分页获取任务列表的接口。接口会自动使用当前登录用户的 ID 来过滤任务。

## 数据库表结构

```sql
CREATE TABLE billing_generation_tasks (
    task_id VARCHAR(36) PRIMARY KEY COMMENT '任务UUID',
    billing_cycle_id BIGINT NOT NULL COMMENT '关联的账期批次ID (FK -> billing_cycles.id)',
    target_customer_ids TEXT NULL COMMENT '目标客户ID列表 (当 task_scope 为 SPECIFIC_CUSTOMERS 或 SINGLE_CUSTOMER 时, 逗号分隔或JSON数组)',
    status VARCHAR(30) NOT NULL DEFAULT 'PENDING' COMMENT '任务状态 (PENDING, PROCESSING, COMPLETED, FAILED)',
    progress_percentage INT NOT NULL DEFAULT 0 COMMENT '进度百分比 (0-100)',
    total_items_to_process INT NULL COMMENT '预计总处理项数 (客户数或账单数)',
    items_processed_count INT NOT NULL DEFAULT 0 COMMENT '已处理项数',
    error_message TEXT NULL COMMENT '如果失败，记录错误信息',
    submitted_by_user_id BIGINT NULL COMMENT '任务提交者ID',
    submit_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    start_time DATETIME NULL COMMENT '处理开始时间',
    end_time DATETIME NULL COMMENT '处理结束时间',
    CONSTRAINT fk_task_billing_cycle FOREIGN KEY (billing_cycle_id) REFERENCES billing_cycles(id)
) COMMENT = '账单生成任务状态表 (支持不同范围)';
```

## 实现架构

### 1. 领域层 (Domain Layer)

#### 实体类 `internal/domain/entity/billing_generation_task.go`

```go
type BillingGenerationTask struct {
    TaskID                string     // 任务UUID
    BillingCycleID        int64      // 关联的账期批次ID
    TargetCustomerIds     *string    // 目标客户ID列表
    Status                TaskStatus // 任务状态
    ProgressPercentage    int        // 进度百分比（0-100）
    TotalItemsToProcess   *int       // 预计总处理项数
    ItemsProcessedCount   int        // 已处理项数
    ErrorMessage          *string    // 错误信息
    SubmittedByUserID     *int64     // 任务提交者ID
    SubmitTime            time.Time  // 提交时间
    StartTime             *time.Time // 处理开始时间
    EndTime               *time.Time // 处理结束时间
}
```

**主要特点：**

- 包含任务状态枚举：`PENDING`、`PROCESSING`、`COMPLETED`、`FAILED`
- 提供业务方法：`IsCompleted()`、`IsFailed()`、`IsProcessing()`、`IsPending()`
- 提供状态中文名称：`GetStatusName()`
- 计算执行耗时：`GetDuration()`

#### 仓储接口 `internal/domain/repository/billing_generation_task_repository.go`

```go
type BillingGenerationTaskRepository interface {
    SaveTask(ctx context.Context, task *entity.BillingGenerationTask) error
    FindTaskByID(ctx context.Context, taskID string) (*entity.BillingGenerationTask, error)
    FindTasksByBillingCycleID(ctx context.Context, billingCycleID int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error)
    FindTasksByUserID(ctx context.Context, userID int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error)
    FindTasksByBillingCycleAndUser(ctx context.Context, billingCycleID int64, userID *int64, page, pageSize int) ([]*entity.BillingGenerationTask, int64, error)
    UpdateTaskStatus(ctx context.Context, taskID string, status entity.TaskStatus) error
    UpdateTaskProgress(ctx context.Context, taskID string, progressPercentage int, itemsProcessedCount int) error
    DeleteTask(ctx context.Context, taskID string) error
}
```

### 2. 基础设施层 (Infrastructure Layer)

#### PO 模型 `internal/adapter/persistence/model/billing_generation_task.go`

```go
type BillingGenerationTaskPO struct {
    TaskID                string     `gorm:"column:task_id;type:varchar(36);primaryKey"`
    BillingCycleID        int64      `gorm:"column:billing_cycle_id;type:bigint;not null"`
    TargetCustomerIds     *string    `gorm:"column:target_customer_ids;type:text"`
    Status                string     `gorm:"column:status;type:varchar(30);not null;default:'PENDING'"`
    ProgressPercentage    int        `gorm:"column:progress_percentage;type:int;not null;default:0"`
    TotalItemsToProcess   *int       `gorm:"column:total_items_to_process;type:int"`
    ItemsProcessedCount   int        `gorm:"column:items_processed_count;type:int;not null;default:0"`
    ErrorMessage          *string    `gorm:"column:error_message;type:text"`
    SubmittedByUserID     *int64     `gorm:"column:submitted_by_user_id;type:bigint"`
    SubmitTime            time.Time  `gorm:"column:submit_time;type:datetime;not null;default:CURRENT_TIMESTAMP"`
    StartTime             *time.Time `gorm:"column:start_time;type:datetime"`
    EndTime               *time.Time `gorm:"column:end_time;type:datetime"`
}
```

#### 仓储实现 `internal/adapter/persistence/billing_generation_task_repository_impl.go`

**主要功能：**

- 支持任务的 CRUD 操作
- 支持按账期批次分页查询
- 支持按用户分页查询
- 支持按账期批次+用户组合查询（支持用户 ID 为空的情况）
- 按提交时间降序排列

### 3. 应用层 (Application Layer)

#### 服务接口和实现

在 `internal/app/service/billing_service.go` 中扩展了现有的 `BillingService`：

**请求 DTO：**

```go
type ListBillingGenerationTasksRequest struct {
    BillingCycleID int64 `form:"billingCycleId" json:"billingCycleId" binding:"required"` // 账期批次ID（必须）
    Page           int   `form:"page" json:"page" default:"1"`                           // 页码
    PageSize       int   `form:"pageSize" json:"pageSize" binding:"max=100" default:"20"` // 每页数量
}
```

**响应 DTO：**

```go
type BillingGenerationTaskDTO struct {
    TaskID              string  `json:"taskId"`              // 任务UUID
    BillingCycleID      int64   `json:"billingCycleId"`      // 关联的账期批次ID
    TargetCustomerIds   *string `json:"targetCustomerIds"`   // 目标客户ID列表
    Status              string  `json:"status"`              // 任务状态
    StatusName          string  `json:"statusName"`          // 任务状态名称
    ProgressPercentage  int     `json:"progressPercentage"`  // 进度百分比（0-100）
    TotalItemsToProcess *int    `json:"totalItemsToProcess"` // 预计总处理项数
    ItemsProcessedCount int     `json:"itemsProcessedCount"` // 已处理项数
    ErrorMessage        *string `json:"errorMessage"`        // 错误信息
    SubmittedByUserID   *int64  `json:"submittedByUserId"`   // 任务提交者ID
    SubmittedByNickname *string `json:"submittedByNickname"` // 任务提交者昵称
    SubmitTime          string  `json:"submitTime"`          // 提交时间（yyyy-MM-dd HH:mm:ss格式）
    StartTime           *string `json:"startTime"`           // 处理开始时间（yyyy-MM-dd HH:mm:ss格式）
    EndTime             *string `json:"endTime"`             // 处理结束时间（yyyy-MM-dd HH:mm:ss格式）
    Duration            *int64  `json:"duration"`            // 执行耗时（秒）
}
```

**服务方法：**

```go
func (s *BillingServiceImpl) ListBillingGenerationTasks(ctx context.Context, req *ListBillingGenerationTasksRequest) (*ListBillingGenerationTasksResponse, int, error)

func (s *BillingServiceImpl) ListBillingGenerationTasksByUser(ctx context.Context, req *ListBillingGenerationTasksRequest, userID *int64) (*ListBillingGenerationTasksResponse, int, error)
```

**说明：**

- `ListBillingGenerationTasks`：查询所有用户的任务（userID 为 nil）
- `ListBillingGenerationTasksByUser`：查询指定用户的任务，接口层会传入当前登录用户 ID

### 4. 表现层 (Presentation Layer)

#### HTTP 处理器

在 `internal/handler/finance_handler.go` 中添加了：

```go
func (h *FinanceHandler) ListBillingGenerationTasks(c *gin.Context)
```

#### 路由配置

在 `internal/router/router.go` 中添加了路由：

```go
financeRoutes.GET("/billing/generation-tasks", financeHandler.ListBillingGenerationTasks)
```

## API 接口文档

### 查询账单生成任务列表

**请求方式：** `GET`

**请求路径：** `/api/v1/finance/billing/generation-tasks`

**请求头：**

```
Authorization: Bearer {JWT_TOKEN}
```

**查询参数：**
| 参数名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|-------|
| billingCycleId | int64 | 是 | 账期批次 ID | - |
| page | int | 否 | 页码 | 1 |
| pageSize | int | 否 | 每页数量（最大 100） | 20 |

**响应示例：**

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "total": 15,
    "list": [
      {
        "taskId": "550e8400-e29b-41d4-a716-446655440000",
        "billingCycleId": 1,
        "targetCustomerIds": "1,2,3",
        "status": "COMPLETED",
        "statusName": "已完成",
        "progressPercentage": 100,
        "totalItemsToProcess": 10,
        "itemsProcessedCount": 10,
        "errorMessage": null,
        "submittedByUserId": 1,
        "submittedByNickname": "管理员",
        "submitTime": "2024-01-15 10:30:00",
        "startTime": "2024-01-15 10:31:00",
        "endTime": "2024-01-15 10:35:00",
        "duration": 240
      }
    ]
  },
  "requestId": "req-12345",
  "timestamp": "2024-01-15T10:40:00Z"
}
```

## 特性说明

### 1. 灵活的查询条件

- **支持按账期批次查询：** 必须传入 `billingCycleId`
- **支持分页：** 默认每页 20 条，最大 100 条

### 2. 完整的任务信息

- **基础信息：** 任务 ID、账期批次 ID、目标客户 ID 列表
- **状态信息：** 任务状态及中文名称
- **进度信息：** 进度百分比、预计处理数、已处理数
- **用户信息：** 提交者 ID 和昵称（自动关联查询）
- **时间信息：** 提交时间、开始时间、结束时间、执行耗时

### 3. 用户信息关联

- 自动批量查询用户信息，避免 N+1 查询问题
- 即使用户查询失败也不影响主要功能
- 提供用户昵称显示

### 4. 错误处理

- 参数验证：账期批次 ID 必须大于 0
- 分页参数自动纠正：page 默认 1，pageSize 默认 20，最大 100
- 标准错误码返回：400/404/500 对应不同错误类型

## 代码变更汇总

### 新增文件

1. `internal/domain/entity/billing_generation_task.go` - 任务实体
2. `internal/domain/repository/billing_generation_task_repository.go` - 仓储接口
3. `internal/adapter/persistence/model/billing_generation_task.go` - PO 模型
4. `internal/adapter/persistence/billing_generation_task_repository_impl.go` - 仓储实现

### 修改文件

1. `internal/app/service/billing_service.go` - 添加任务管理功能
2. `internal/handler/finance_handler.go` - 添加 HTTP 处理器
3. `internal/router/router.go` - 添加路由配置
4. `cmd/api-server/main.go` - 添加仓储依赖注入

## 测试建议

### 1. 单元测试

- 测试仓储层的各种查询方法
- 测试服务层的参数验证和业务逻辑
- 测试 DTO 转换的正确性

### 2. 集成测试

- 测试完整的 API 调用流程
- 测试不同查询条件的组合
- 测试分页功能的边界情况

### 3. 性能测试

- 测试大量任务记录的查询性能
- 测试用户信息批量查询的性能
- 测试分页查询的效率

## 使用示例

### 查询当前用户的任务

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/generation-tasks?billingCycleId=1&page=1&pageSize=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 查询指定页码的任务

```bash
curl -X GET "http://localhost:8080/api/v1/finance/billing/generation-tasks?billingCycleId=1&page=2&pageSize=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 后续扩展建议

1. **任务状态变更接口：** 支持手动更新任务状态
2. **任务重试机制：** 对失败的任务提供重试功能
3. **任务详情查询：** 根据 taskId 查询单个任务的详细信息
4. **任务删除接口：** 支持删除已完成或失败的任务
5. **WebSocket 推送：** 实时推送任务状态变更
6. **任务统计报表：** 提供任务执行统计和分析功能
