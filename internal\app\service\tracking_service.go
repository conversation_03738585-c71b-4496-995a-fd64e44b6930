package service

import (
	"context"
	"errors"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"
)

// TrackingService 轨迹服务
type TrackingService struct {
	trackingRepository repository.TrackingRepository
	manifestRepository repository.ManifestRepository
}

// NewTrackingService 创建轨迹服务实例
func NewTrackingService(
	trackingRepository repository.TrackingRepository,
	manifestRepository repository.ManifestRepository,
) *TrackingService {
	return &TrackingService{
		trackingRepository: trackingRepository,
		manifestRepository: manifestRepository,
	}
}

// GetTrackingsByManifestIDRequest 根据运单ID查询轨迹请求
type GetTrackingsByManifestIDRequest struct {
	ManifestID int64 `json:"manifestId" uri:"manifestId" binding:"required"` // 运单ID
}

// TrackingDTO 轨迹数据传输对象
type TrackingDTO struct {
	*entity.Tracking
	OperatorName string `json:"operatorName,omitempty"` // 操作人名称，预留字段，暂不实�?
}

// GetTrackingsByManifestIDResponse 查询轨迹响应
type GetTrackingsByManifestIDResponse struct {
	ManifestID    int64         `json:"manifestId"`    // 运单ID
	TrackingCount int           `json:"trackingCount"` // 轨迹数量
	Trackings     []*TrackingDTO `json:"trackings"`     // 轨迹列表
}

// GetTrackingsByManifestID 根据运单ID查询轨迹列表
func (s *TrackingService) GetTrackingsByManifestID(ctx context.Context, req *GetTrackingsByManifestIDRequest) (*GetTrackingsByManifestIDResponse, int, error) {
	// 验证运单是否存在
	manifest, err := s.manifestRepository.GetManifestByID(ctx, req.ManifestID)
	if err != nil {
		return nil, valueobject.ERROR_RESOURCE_NOT_FOUND, errors.New("运单不存在")
	}
	
	// 查询轨迹列表
	trackings, err := s.trackingRepository.FindByManifestID(ctx, req.ManifestID)
	if err != nil {
		return nil, valueobject.ERROR_UNKNOWN, err
	}
	
	// 转换为DTO
	trackingDTOs := make([]*TrackingDTO, 0, len(trackings))
	for _, tracking := range trackings {
		trackingDTOs = append(trackingDTOs, &TrackingDTO{
			Tracking: tracking,
			// 如果需要填充操作人信息，可以在这里实现
		})
	}
	
	return &GetTrackingsByManifestIDResponse{
		ManifestID:    manifest.ID,
		TrackingCount: len(trackingDTOs),
		Trackings:     trackingDTOs,
	}, valueobject.SUCCESS, nil
} 

