package message_queue

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"zebra-hub-system/internal/config"
	"zebra-hub-system/internal/domain/entity"

	"github.com/google/uuid"
	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// BillingProducer 账单生成消息生产者
// 实现 repository.MessageProducer 接口
type BillingProducer struct {
	conn   *amqp.Connection
	logger *zap.Logger
}

// NewBillingProducer 创建账单生成消息生产者
func NewBillingProducer(conn *amqp.Connection, logger *zap.Logger) *BillingProducer {
	return &BillingProducer{
		conn:   conn,
		logger: logger,
	}
}

// PublishBillingTask 发布账单生成任务
// 实现 repository.MessageProducer 接口
func (p *BillingProducer) PublishBillingTask(ctx context.Context, message *entity.BillingGenerationMessage) error {
	// 创建通道
	ch, err := p.conn.Channel()
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to open channel", zap.Error(err))
		}
		return fmt.Errorf("failed to open channel: %w", err)
	}
	defer ch.Close()

	// 声明交换机
	err = ch.ExchangeDeclare(
		config.BillingGenerationExchange, // 交换机名称
		"direct",                         // 交换机类型
		true,                             // 持久化
		false,                            // 不自动删除
		false,                            // 不排他
		false,                            // 不等待服务器确认
		nil,                              // 额外参数
	)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to declare exchange", zap.Error(err))
		}
		return fmt.Errorf("failed to declare exchange: %w", err)
	}

	// 声明队列
	queue, err := ch.QueueDeclare(
		config.BillingGenerationQueue, // 队列名称
		true,                          // 持久化
		false,                         // 不自动删除
		false,                         // 不排他
		false,                         // 不等待服务器确认
		nil,                           // 额外参数
	)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to declare queue", zap.Error(err))
		}
		return fmt.Errorf("failed to declare queue: %w", err)
	}

	// 绑定队列到交换机
	err = ch.QueueBind(
		queue.Name,                         // 队列名称
		config.BillingGenerationRoutingKey, // 路由键
		config.BillingGenerationExchange,   // 交换机名称
		false,                              // 不等待服务器确认
		nil,                                // 额外参数
	)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to bind queue", zap.Error(err))
		}
		return fmt.Errorf("failed to bind queue: %w", err)
	}

	// 设置消息元信息
	if message.MessageID == "" {
		message.MessageID = uuid.New().String()
	}
	message.CreatedAt = time.Now()

	// 序列化消息
	body, err := json.Marshal(message)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to marshal message", zap.Error(err))
		}
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// 发布消息
	err = ch.Publish(
		config.BillingGenerationExchange,   // 交换机
		config.BillingGenerationRoutingKey, // 路由键
		false,                              // 不强制
		false,                              // 不立即
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			MessageId:    message.MessageID,
			Timestamp:    time.Now(),
			DeliveryMode: amqp.Persistent, // 持久化消息
			Headers: amqp.Table{
				"taskId":         message.TaskID,
				"billingCycleId": message.BillingCycleID,
				"customerCount":  len(message.CustomerIDs),
				"retryCount":     message.RetryCount,
			},
		},
	)
	if err != nil {
		if p.logger != nil {
			p.logger.Error("Failed to publish message",
				zap.String("taskId", message.TaskID),
				zap.String("messageId", message.MessageID),
				zap.Error(err))
		}
		return fmt.Errorf("failed to publish message: %w", err)
	}

	if p.logger != nil {
		p.logger.Info("Successfully published billing task",
			zap.String("taskId", message.TaskID),
			zap.String("messageId", message.MessageID),
			zap.Int64("billingCycleId", message.BillingCycleID),
			zap.Int("customerCount", len(message.CustomerIDs)))
	}

	return nil
} 