# 外部系统创建订单接口测试文档

## 接口信息

- **URL**: `POST /api/v1/external/orders`
- **功能**: 供外部系统对接的创建订单接口
- **认证**: 需要 Bearer Token 认证

## 请求参数

```json
{
  "orderNumber": "MERCHANT_ORDER_20241217001",
  "shipmentTypeId": 1,
  "receiverName": "田中太郎",
  "receiverPhone": "090-1234-5678",
  "receiverZipCode": "100-0001",
  "receiverAddress": "東京都千代田区千代田1-1",
  "items": [
    {
      "name": "手机壳",
      "quantity": 2
    },
    {
      "name": "数据线",
      "quantity": 1
    }
  ]
}
```

## 参数说明

| 字段名           | 类型   | 必填 | 说明                                             |
| ---------------- | ------ | ---- | ------------------------------------------------ |
| orderNumber      | string | 是   | 商家订单号                                       |
| shipmentTypeId   | int64  | 是   | 货物类型 ID (1-普通货物, 2-带电货物, 3-投函货物) |
| receiverName     | string | 是   | 收件人姓名                                       |
| receiverPhone    | string | 是   | 收件人电话（日本格式）                           |
| receiverZipCode  | string | 是   | 收件人邮编（日本格式）                           |
| receiverAddress  | string | 是   | 收件人地址                                       |
| items            | array  | 是   | 物品列表                                         |
| items[].name     | string | 是   | 物品名称                                         |
| items[].quantity | int    | 是   | 物品数量（最小值为 1）                           |

## 成功响应

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "manifestId": 12345,
    "expressNumber": "JP123456789",
    "orderNumber": "MERCHANT_ORDER_20241217001",
    "systemOrderNo": "BM1217123456"
  }
}
```

## 业务逻辑说明

### 1. 系统订单号生成规则

- **普通货物/带电货物**: `BM + 月日 + 6位随机数`
  - 例如：12 月 17 日 → `BM1217123456`
- **投函货物**: `SF + 6位随机数`
  - 例如：`SF123456`

### 2. 偏远费计算

- 如果不是投函货物且收件人邮编为冲绳县（900-906 开头），自动添加 100 元偏远费

### 3. 校验规则

- 收件人电话：必须符合日本电话号码格式
- 收件人邮编：必须存在于日本邮编库中
- 货物类型：必须有对应的运单号渠道且渠道为启用状态
- 运单号：从对应渠道的号池中自动分配

## 错误响应示例

### 电话号码格式错误

```json
{
  "success": false,
  "errorCode": 201011,
  "errorMessage": "电话号码格式错误: invalid phone number format"
}
```

### 邮编不存在

```json
{
  "success": false,
  "errorCode": 201010,
  "errorMessage": "该邮编不存在于日本邮编库中，请核对后重新输入"
}
```

### 渠道不存在

```json
{
  "success": false,
  "errorCode": 202001,
  "errorMessage": "未找到匹配的运单号渠道"
}
```

### 单号不足

```json
{
  "success": false,
  "errorCode": 202005,
  "errorMessage": "所选渠道可用单号不足"
}
```

## 测试用例

### 测试用例 1：普通货物 + 东京邮编

```bash
curl -X POST http://localhost:8080/api/v1/external/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "orderNumber": "TEST_ORDER_001",
    "shipmentTypeId": 1,
    "receiverName": "田中太郎",
    "receiverPhone": "090-1234-5678",
    "receiverZipCode": "100-0001",
    "receiverAddress": "東京都千代田区千代田1-1",
    "items": [
      {
        "name": "手机壳",
        "quantity": 2
      }
    ]
  }'
```

### 测试用例 2：投函货物

```bash
curl -X POST http://localhost:8080/api/v1/external/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "orderNumber": "TEST_ORDER_002",
    "shipmentTypeId": 3,
    "receiverName": "山田花子",
    "receiverPhone": "080-9876-5432",
    "receiverZipCode": "150-0001",
    "receiverAddress": "東京都渋谷区神宮前1-1-1",
    "items": [
      {
        "name": "小商品",
        "quantity": 1
      }
    ]
  }'
```

### 测试用例 3：冲绳县邮编（会产生偏远费）

```bash
curl -X POST http://localhost:8080/api/v1/external/orders \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "orderNumber": "TEST_ORDER_003",
    "shipmentTypeId": 1,
    "receiverName": "沖縄太郎",
    "receiverPhone": "************",
    "receiverZipCode": "900-0001",
    "receiverAddress": "沖縄県那覇市港町1-1-1",
    "items": [
      {
        "name": "商品",
        "quantity": 1
      }
    ]
  }'
```

## 相关接口

### 地址查询接口

在创建订单前，可以使用地址查询接口验证邮编并获取详细地址信息：

**URL**: `GET /api/v1/address/info`

**请求示例**:

```bash
curl -X GET "http://localhost:8080/api/v1/address/info?zipCode=100-0001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "zipCode": "1000001",
    "prefectureJA": "東京都",
    "prefectureEN": "Tokyo",
    "cityJA": "千代田区",
    "cityEN": "Chiyoda-ku",
    "townJA": "千代田",
    "townEN": "Chiyoda",
    "fullAddressJA": "東京都千代田区千代田",
    "fullAddressEN": "Chiyoda, Chiyoda-ku, Tokyo"
  }
}
```

## 注意事项

1. 接口需要 Bearer Token 认证
2. 当前用户 ID 从 Token 中获取，作为运单的所属用户
3. 创建的运单初始状态为待审核（status=0）
4. 创建成功后会同时创建运单记录和物品记录
5. 系统会自动分配运单号并将其状态标记为已分配
6. 邮编服务现已支持完整地址信息存储，可提供详细的地址数据
