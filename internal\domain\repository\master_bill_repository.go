package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// MasterBillRepository 主提单仓储接口
type MasterBillRepository interface {
	// ListForOptions 获取主提单列表，用于前端下拉框
	// @param ctx - 上下文
	// @param keyword - 可选的关键字过滤(提单号)
	// @param page - 页码
	// @param pageSize - 每页数量
	// @return 主提单列表、总数量和可能的错误
	ListForOptions(ctx context.Context, keyword string, page, pageSize int) ([]*entity.MasterBill, int64, error)
	
	// FindByID 根据ID查询主提单
	FindByID(ctx context.Context, id int64) (*entity.MasterBill, error)
} 