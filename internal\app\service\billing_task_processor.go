package service

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"go.uber.org/zap"
)

// BillingTaskProcessorConfig 账单任务处理器配置
type BillingTaskProcessorConfig struct {
	MaxConcurrency int // 最大并发数，默认为 5
	ProgressUpdateInterval time.Duration // 进度更新间隔，默认为 1 秒
}

// DefaultBillingTaskProcessorConfig 默认配置
func DefaultBillingTaskProcessorConfig() *BillingTaskProcessorConfig {
	return &BillingTaskProcessorConfig{
		MaxConcurrency:         5,
		ProgressUpdateInterval: time.Second,
	}
}

// BillingTaskProcessor 账单生成任务处理器
type BillingTaskProcessor struct {
	billingRepo                     repository.BillingRepository
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository
	userRepo                        repository.UserRepository
	shippingFeeTemplateRepo         repository.ShippingFeeTemplateRepository
	billingGenerationTaskRepo       repository.BillingGenerationTaskRepository
	billingCycleRepo                repository.BillingCycleRepository
	logger                          *zap.Logger
	config                          *BillingTaskProcessorConfig
}

// NewBillingTaskProcessor 创建账单生成任务处理器
func NewBillingTaskProcessor(
	billingRepo repository.BillingRepository,
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository,
	userRepo repository.UserRepository,
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository,
	billingGenerationTaskRepo repository.BillingGenerationTaskRepository,
	billingCycleRepo repository.BillingCycleRepository,
	logger *zap.Logger,
) *BillingTaskProcessor {
	return &BillingTaskProcessor{
		billingRepo:                     billingRepo,
		manifestFinancialAdjustmentRepo: manifestFinancialAdjustmentRepo,
		userRepo:                        userRepo,
		shippingFeeTemplateRepo:         shippingFeeTemplateRepo,
		billingGenerationTaskRepo:       billingGenerationTaskRepo,
		billingCycleRepo:                billingCycleRepo,
		logger:                          logger,
		config:                          DefaultBillingTaskProcessorConfig(),
	}
}

// NewBillingTaskProcessorWithConfig 使用自定义配置创建账单生成任务处理器
func NewBillingTaskProcessorWithConfig(
	billingRepo repository.BillingRepository,
	manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository,
	userRepo repository.UserRepository,
	shippingFeeTemplateRepo repository.ShippingFeeTemplateRepository,
	billingGenerationTaskRepo repository.BillingGenerationTaskRepository,
	billingCycleRepo repository.BillingCycleRepository,
	logger *zap.Logger,
	config *BillingTaskProcessorConfig,
) *BillingTaskProcessor {
	if config == nil {
		config = DefaultBillingTaskProcessorConfig()
	}
	return &BillingTaskProcessor{
		billingRepo:                     billingRepo,
		manifestFinancialAdjustmentRepo: manifestFinancialAdjustmentRepo,
		userRepo:                        userRepo,
		shippingFeeTemplateRepo:         shippingFeeTemplateRepo,
		billingGenerationTaskRepo:       billingGenerationTaskRepo,
		billingCycleRepo:                billingCycleRepo,
		logger:                          logger,
		config:                          config,
	}
}

// CustomerProcessingResult 客户处理结果
type CustomerProcessingResult struct {
	CustomerID int64
	Success    bool
	Error      error
	Index      int // 原始索引，用于进度计算
}

// ProcessBillingTask 处理账单生成任务（多线程版本）
func (p *BillingTaskProcessor) ProcessBillingTask(ctx context.Context, message *entity.BillingGenerationMessage) error {
	taskID := message.TaskID
	totalCustomers := len(message.CustomerIDs)

	p.logger.Info("开始处理账单生成任务",
		zap.String("taskId", taskID),
		zap.Int("customerCount", totalCustomers),
		zap.Int64("billingCycleId", message.BillingCycleID),
		zap.Int("maxConcurrency", p.config.MaxConcurrency))

	// 1. 更新任务状态为处理中
	now := time.Now()
	if err := p.updateTaskStatusToProcessing(ctx, taskID, now); err != nil {
		p.logger.Error("更新任务状态为处理中失败",
			zap.String("taskId", taskID),
			zap.Error(err))
		return err
	}

	// 2. 使用 goroutine pool 并发处理客户账单
	results, err := p.processBillingConcurrently(ctx, message)
	if err != nil {
		p.logger.Error("并发处理客户账单失败",
			zap.String("taskId", taskID),
			zap.Error(err))
		endTime := time.Now()
		p.updateTaskStatusToFailed(ctx, taskID, endTime, err.Error())
		return err
	}

	// 3. 统计处理结果
	successCount := 0
	errorMessages := make([]string, 0)

	for _, result := range results {
		if result.Success {
			successCount++
			p.logger.Info("客户账单生成成功",
				zap.String("taskId", taskID),
				zap.Int64("customerId", result.CustomerID))
		} else {
			errorMsg := fmt.Sprintf("客户 %d 账单生成失败: %v", result.CustomerID, result.Error)
			errorMessages = append(errorMessages, errorMsg)
			p.logger.Error("客户账单生成失败",
				zap.String("taskId", taskID),
				zap.Int64("customerId", result.CustomerID),
				zap.Error(result.Error))
		}
	}

	// 4. 更新最终任务状态
	endTime := time.Now()
	if successCount == totalCustomers {
		// 全部成功
		if err := p.updateTaskStatusToCompleted(ctx, taskID, endTime, nil); err != nil {
			p.logger.Error("更新任务状态为完成失败",
				zap.String("taskId", taskID),
				zap.Error(err))
		}
		p.logger.Info("账单生成任务完成",
			zap.String("taskId", taskID),
			zap.Int("successCount", successCount),
			zap.Int("totalCustomers", totalCustomers))
	} else {
		// 部分或全部失败
		combinedErrorMsg := strings.Join(errorMessages, "; ")
		if err := p.updateTaskStatusToFailed(ctx, taskID, endTime, combinedErrorMsg); err != nil {
			p.logger.Error("更新任务状态为失败失败",
				zap.String("taskId", taskID),
				zap.Error(err))
		}
		p.logger.Warn("账单生成任务部分失败",
			zap.String("taskId", taskID),
			zap.Int("successCount", successCount),
			zap.Int("totalCustomers", totalCustomers),
			zap.Strings("errors", errorMessages))

		// 如果部分成功，我们认为任务整体还是成功的，只记录警告
		if successCount > 0 {
			return nil // 不返回错误，避免消息重复消费
		} else {
			return fmt.Errorf("所有客户账单生成都失败: %s", combinedErrorMsg)
		}
	}

	return nil
}

// processBillingConcurrently 并发处理所有客户的账单生成
func (p *BillingTaskProcessor) processBillingConcurrently(ctx context.Context, message *entity.BillingGenerationMessage) ([]CustomerProcessingResult, error) {
	taskID := message.TaskID
	totalCustomers := len(message.CustomerIDs)
	maxConcurrency := p.config.MaxConcurrency

	// 限制并发数不超过客户总数
	if maxConcurrency > totalCustomers {
		maxConcurrency = totalCustomers
	}

	p.logger.Info("开始并发处理客户账单",
		zap.String("taskId", taskID),
		zap.Int("totalCustomers", totalCustomers),
		zap.Int("concurrency", maxConcurrency))

	// 创建工作通道和结果通道
	customerJobs := make(chan CustomerJob, totalCustomers)
	results := make(chan CustomerProcessingResult, totalCustomers)

	// 启动进度更新 goroutine
	var processedCount int64
	progressCtx, progressCancel := context.WithCancel(ctx)
	defer progressCancel()

	go p.progressUpdater(progressCtx, taskID, &processedCount, totalCustomers)

	// 启动 worker goroutines
	var wg sync.WaitGroup
	for i := 0; i < maxConcurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			p.customerBillingWorker(ctx, message, customerJobs, results, &processedCount, workerID)
		}(i)
	}

	// 发送所有客户任务到工作通道
	for i, customerID := range message.CustomerIDs {
		customerJobs <- CustomerJob{
			CustomerID: customerID,
			Index:      i,
		}
	}
	close(customerJobs)

	// 等待所有 worker 完成
	go func() {
		wg.Wait()
		close(results)
	}()

	// 收集结果
	customerResults := make([]CustomerProcessingResult, 0, totalCustomers)
	for result := range results {
		customerResults = append(customerResults, result)
	}

	// 按原始索引排序，保持处理顺序的一致性（主要用于日志和调试）
	sort.Slice(customerResults, func(i, j int) bool {
		return customerResults[i].Index < customerResults[j].Index
	})

	p.logger.Info("并发处理完成",
		zap.String("taskId", taskID),
		zap.Int("totalResults", len(customerResults)))

	return customerResults, nil
}

// CustomerJob 客户处理任务
type CustomerJob struct {
	CustomerID int64
	Index      int
}

// customerBillingWorker 客户账单处理工作协程
func (p *BillingTaskProcessor) customerBillingWorker(
	ctx context.Context,
	message *entity.BillingGenerationMessage,
	jobs <-chan CustomerJob,
	results chan<- CustomerProcessingResult,
	processedCount *int64,
	workerID int,
) {
	for job := range jobs {
		customerID := job.CustomerID

		p.logger.Debug("Worker开始处理客户账单",
			zap.Int("workerId", workerID),
			zap.String("taskId", message.TaskID),
			zap.Int64("customerId", customerID),
			zap.Int("index", job.Index))

		// 处理单个客户的账单
		err := p.processCustomerBilling(ctx, message, customerID)

		// 记录结果
		result := CustomerProcessingResult{
			CustomerID: customerID,
			Success:    err == nil,
			Error:      err,
			Index:      job.Index,
		}

		results <- result

		// 原子性地增加已处理计数
		atomic.AddInt64(processedCount, 1)

		if err != nil {
			p.logger.Debug("Worker处理客户账单失败",
				zap.Int("workerId", workerID),
				zap.String("taskId", message.TaskID),
				zap.Int64("customerId", customerID),
				zap.Error(err))
		} else {
			p.logger.Debug("Worker处理客户账单成功",
				zap.Int("workerId", workerID),
				zap.String("taskId", message.TaskID),
				zap.Int64("customerId", customerID))
		}
	}

	p.logger.Debug("Worker完成所有任务",
		zap.Int("workerId", workerID),
		zap.String("taskId", message.TaskID))
}

// progressUpdater 进度更新器
func (p *BillingTaskProcessor) progressUpdater(ctx context.Context, taskID string, processedCount *int64, totalCustomers int) {
	ticker := time.NewTicker(p.config.ProgressUpdateInterval)
	defer ticker.Stop()

	lastProgress := 0

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			currentCount := atomic.LoadInt64(processedCount)
			progress := int((float64(currentCount) / float64(totalCustomers)) * 100)

			// 只在进度有变化时更新
			if progress != lastProgress {
				p.updateTaskProgress(ctx, taskID, progress, int(currentCount))
				lastProgress = progress

				p.logger.Debug("进度更新",
					zap.String("taskId", taskID),
					zap.Int64("currentCount", currentCount),
					zap.Int("totalCustomers", totalCustomers),
					zap.Int("progress", progress))
			}

			// 如果已完成所有任务，退出进度更新
			if currentCount >= int64(totalCustomers) {
				return
			}
		}
	}
}

// processCustomerBilling 处理单个客户的账单生成
func (p *BillingTaskProcessor) processCustomerBilling(ctx context.Context, message *entity.BillingGenerationMessage, customerID int64) error {
	// 1. 获取客户的运费模板配置
	templates, err := p.getCustomerTemplates(ctx, message, customerID)
	if err != nil {
		return fmt.Errorf("获取客户运费模板失败: %w", err)
	}

	// 2. 查询符合条件的运单（固定使用发货时间）
	manifests, err := p.billingRepo.FindManifestsForBilling(ctx, entity.BillingTimeTypeShipmentTime, message.StartTime, message.EndTime, customerID)
	if err != nil {
		return fmt.Errorf("查询运单数据失败: %w", err)
	}

	if len(manifests) == 0 {
		p.logger.Info("该客户在指定时间范围内没有运单记录",
			zap.Int64("customerId", customerID),
			zap.Time("startTime", message.StartTime),
			zap.Time("endTime", message.EndTime))
		return nil // 没有运单不算错误
	}

	// 2.1 智能检查模板配置 - 只检查实际需要的模板类型
	var isTemplateError bool
	var templateErrorMsg string
	
	// 分析运单实际需要的模板类型
	requiredTemplateTypes := p.analyzeRequiredTemplateTypes(manifests)
	missingTemplates := p.checkMissingTemplates(templates, requiredTemplateTypes)
	
	if len(missingTemplates) > 0 {
		isTemplateError = true
		templateErrorMsg = fmt.Sprintf("用户缺少必需的运费模板：%s", strings.Join(missingTemplates, "、"))
		p.logger.Warn("用户缺少必需的运费模板，将创建异常状态账单",
			zap.Int64("customerId", customerID),
			zap.Strings("missingTemplates", missingTemplates),
			zap.Ints("requiredTypes", requiredTemplateTypes))
	}

	// 3. 生成账单编号
	billNumber, err := p.billingRepo.GenerateBillNumber(ctx)
	if err != nil {
		return fmt.Errorf("生成账单编号失败: %w", err)
	}

	// 4. 创建账单主记录
	// 确定账单状态，如果模板有问题则设为异常状态
	billingStatus := entity.BillingStatusUnpaid
	var notes *string
	if isTemplateError {
		billingStatus = entity.BillingStatusError
		notes = &templateErrorMsg
	} else if message.Notes != nil {
		notes = message.Notes
	}

	billingRecord := &entity.BillingRecord{
		BillNumber:              billNumber,
		CustomerAccountID:       customerID,
		BillingCycleID:          message.BillingCycleID,
		BillDate:                time.Now(),
		DueDate:                 message.DueDate,
		BillingPeriodStart:      message.StartTime,
		BillingPeriodEnd:        message.EndTime,
		AppliedFreightTemplates: templates,
		FreightChargesTotal:     0, // 将在后续计算
		AdjustmentChargesTotal:  0, // 将在后续计算
		TotalAmount:             0, // 将在后续计算
		AmountPaid:              0,
		Currency:                message.Currency,
		Status:                  billingStatus,
		Notes:                   notes,
		GeneratedByUserID:       message.GeneratedByUserID,
		CreateTime:              time.Now(),
		UpdateTime:              time.Now(),
	}

	// 5. 保存账单主记录
	if err := p.billingRepo.SaveBillingRecord(ctx, billingRecord); err != nil {
		return fmt.Errorf("保存账单记录失败: %w", err)
	}

	// 6. 生成账单明细
	var billingItems []*entity.BillingRecordItem
	var totalAmount float64

	if isTemplateError {
		// 如果是模板错误导致的异常状态，不生成明细项，但记录错误信息
		p.logger.Warn("账单因模板配置异常跳过明细生成",
			zap.Int64("billingRecordId", billingRecord.ID),
			zap.String("billNumber", billNumber),
			zap.String("errorMessage", templateErrorMsg))
		billingItems = []*entity.BillingRecordItem{} // 空的明细列表
		totalAmount = 0
	} else {
		// 正常生成账单明细
		billingItems, totalAmount, err = p.generateBillingItems(ctx, billingRecord.ID, manifests, templates)
		if err != nil {
			return fmt.Errorf("生成账单明细失败: %w", err)
		}
	}

	// 7. 保存账单明细
	if err := p.billingRepo.SaveBillingRecordItems(ctx, billingItems); err != nil {
		return fmt.Errorf("保存账单明细失败: %w", err)
	}

	// 8. 查询并保存财务调整快照
	adjustmentTotalAmount, err := p.generateFinancialAdjustmentSnapshots(ctx, billingRecord.ID, customerID, message.StartTime, message.EndTime)
	if err != nil {
		return fmt.Errorf("生成财务调整快照失败: %w", err)
	}

	// 9. 更新账单总金额
	billingRecord.FreightChargesTotal = totalAmount
	billingRecord.AdjustmentChargesTotal = adjustmentTotalAmount
	finalTotalAmount := totalAmount + adjustmentTotalAmount
	billingRecord.TotalAmount = finalTotalAmount
	billingRecord.BalanceDue = finalTotalAmount
	if err := p.billingRepo.SaveBillingRecord(ctx, billingRecord); err != nil {
		return fmt.Errorf("更新账单总金额失败: %w", err)
	}

	p.logger.Info("客户账单生成成功",
		zap.Int64("customerId", customerID),
		zap.String("billNumber", billNumber),
		zap.Int64("billingRecordId", billingRecord.ID),
		zap.Float64("manifestTotalAmount", totalAmount),
		zap.Float64("adjustmentTotalAmount", adjustmentTotalAmount),
		zap.Float64("finalTotalAmount", finalTotalAmount),
		zap.Int("itemsCount", len(billingItems)))

	// 10. 更新账期批次统计信息
	if err := p.billingCycleRepo.UpdateBillingCycleStatistics(ctx, message.BillingCycleID, customerID, 1, finalTotalAmount); err != nil {
		p.logger.Warn("更新账期批次统计信息失败",
			zap.Int64("billingCycleId", message.BillingCycleID),
			zap.Int64("customerId", customerID),
			zap.Float64("amount", finalTotalAmount),
			zap.Error(err))
		// 统计信息更新失败不影响账单生成的成功，只记录警告
	}

	return nil
}

// getCustomerTemplates 获取客户的运费模板配置
func (p *BillingTaskProcessor) getCustomerTemplates(ctx context.Context, message *entity.BillingGenerationMessage, customerID int64) (*entity.AppliedFreightTemplate, error) {
	// 查找用户特定模板
	for _, userTemplate := range message.UserTemplates {
		if userTemplate.UserID == customerID {
			return &entity.AppliedFreightTemplate{
				GeneralTemplate: userTemplate.GeneralTemplate,
				BatteryTemplate: userTemplate.BatteryTemplate,
				PostBoxTemplate: userTemplate.PostBoxTemplate,
			}, nil
		}
	}

	// 如果没有用户特定模板，查询系统配置的用户默认模板（通过ShippingFeeTemplateUser表）
	userTemplates, err := p.shippingFeeTemplateRepo.GetUserAllTemplates(ctx, customerID)
	if err != nil {
		return nil, fmt.Errorf("查询用户系统模板失败: %w", err)
	}

	appliedTemplates := &entity.AppliedFreightTemplate{}
	// 由于删除了Type字段，这里简化处理：使用第一个可用模板作为通用模板
	if len(userTemplates) > 0 && userTemplates[0] != nil {
		appliedTemplates.GeneralTemplate = userTemplates[0]
	}

	return appliedTemplates, nil
}

// generateBillingItems 生成账单明细
func (p *BillingTaskProcessor) generateBillingItems(ctx context.Context, billingRecordID int64, manifests []*entity.Manifest, templates *entity.AppliedFreightTemplate) ([]*entity.BillingRecordItem, float64, error) {
	// 创建临时的BillingService来复用现有逻辑
	tempService := &BillingServiceImpl{
		billingRepo:                     p.billingRepo,
		manifestFinancialAdjustmentRepo: p.manifestFinancialAdjustmentRepo,
		userRepo:                        p.userRepo,
		shippingFeeTemplateRepo:         p.shippingFeeTemplateRepo,
		billingGenerationTaskRepo:       p.billingGenerationTaskRepo,
		billingCycleRepo:                p.billingCycleRepo,
		billingProducer:                 nil, // 不需要生产者
	}

	// 调用现有的生成账单明细方法
	return tempService.generateBillingItems(ctx, billingRecordID, manifests, templates)
}

// generateFinancialAdjustmentSnapshots 生成财务调整快照
func (p *BillingTaskProcessor) generateFinancialAdjustmentSnapshots(ctx context.Context, billingRecordID int64, userID int64, startDate, endDate time.Time) (float64, error) {
	// 创建临时的BillingService来复用现有逻辑
	tempService := &BillingServiceImpl{
		billingRepo:                     p.billingRepo,
		manifestFinancialAdjustmentRepo: p.manifestFinancialAdjustmentRepo,
		userRepo:                        p.userRepo,
		shippingFeeTemplateRepo:         p.shippingFeeTemplateRepo,
		billingGenerationTaskRepo:       p.billingGenerationTaskRepo,
		billingCycleRepo:                p.billingCycleRepo,
		billingProducer:                 nil, // 不需要生产者
	}

	return tempService.generateFinancialAdjustmentSnapshots(ctx, billingRecordID, userID, startDate, endDate)
}

// 任务状态更新方法
func (p *BillingTaskProcessor) updateTaskStatusToProcessing(ctx context.Context, taskID string, startTime time.Time) error {
	task, err := p.billingGenerationTaskRepo.FindTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	task.Status = entity.TaskStatusProcessing
	task.StartTime = &startTime
	task.ProgressPercentage = 0

	return p.billingGenerationTaskRepo.SaveTask(ctx, task)
}

func (p *BillingTaskProcessor) updateTaskProgress(ctx context.Context, taskID string, progressPercentage, itemsProcessed int) {
	task, err := p.billingGenerationTaskRepo.FindTaskByID(ctx, taskID)
	if err != nil {
		p.logger.Warn("查询任务失败，无法更新进度",
			zap.String("taskId", taskID),
			zap.Error(err))
		return
	}

	task.ProgressPercentage = progressPercentage
	task.ItemsProcessedCount = itemsProcessed

	if err := p.billingGenerationTaskRepo.SaveTask(ctx, task); err != nil {
		p.logger.Warn("更新任务进度失败",
			zap.String("taskId", taskID),
			zap.Error(err))
	}
}

func (p *BillingTaskProcessor) updateTaskStatusToCompleted(ctx context.Context, taskID string, endTime time.Time, errorMessage *string) error {
	task, err := p.billingGenerationTaskRepo.FindTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	task.Status = entity.TaskStatusCompleted
	task.EndTime = &endTime
	task.ProgressPercentage = 100
	task.ErrorMessage = errorMessage

	return p.billingGenerationTaskRepo.SaveTask(ctx, task)
}

func (p *BillingTaskProcessor) updateTaskStatusToFailed(ctx context.Context, taskID string, endTime time.Time, errorMessage string) error {
	task, err := p.billingGenerationTaskRepo.FindTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	task.Status = entity.TaskStatusFailed
	task.EndTime = &endTime
	task.ErrorMessage = &errorMessage

	return p.billingGenerationTaskRepo.SaveTask(ctx, task)
}

// parseCustomerIdsFromString 从字符串解析客户ID列表
func (p *BillingTaskProcessor) parseCustomerIdsFromString(customerIdsStr *string) ([]int64, error) {
	if customerIdsStr == nil || *customerIdsStr == "" {
		return []int64{}, nil
	}

	idStrings := strings.Split(*customerIdsStr, ",")
	customerIDs := make([]int64, len(idStrings))

	for i, idStr := range idStrings {
		id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
		if err != nil {
			return nil, fmt.Errorf("解析客户ID失败: %s", idStr)
		}
		customerIDs[i] = id
	}

	return customerIDs, nil
}

// analyzeRequiredTemplateTypes 分析运单实际需要的模板类型
func (p *BillingTaskProcessor) analyzeRequiredTemplateTypes(manifests []*entity.Manifest) []int {
	requiredTemplateTypes := make(map[int]bool)

	for _, manifest := range manifests {
		// 根据运单的运费模板类型判断需要哪种模板
		templateType := manifest.ShippingFeeTemplateType
		if templateType == 0 {
			templateType = 1 // 默认为普通货物
		}
		requiredTemplateTypes[templateType] = true
	}

	// 转换为切片并排序
	templateTypes := make([]int, 0, len(requiredTemplateTypes))
	for templateType := range requiredTemplateTypes {
		templateTypes = append(templateTypes, templateType)
	}
	
	// 排序以确保一致性
	sort.Ints(templateTypes)
	return templateTypes
}

// checkMissingTemplates 检查缺少的必需模板
func (p *BillingTaskProcessor) checkMissingTemplates(templates *entity.AppliedFreightTemplate, requiredTypes []int) []string {
	missingTemplates := make([]string, 0)

	for _, templateType := range requiredTypes {
		switch templateType {
		case 1:
			if templates.GeneralTemplate == nil {
				missingTemplates = append(missingTemplates, "普货模板")
			}
		case 2:
			if templates.BatteryTemplate == nil {
				missingTemplates = append(missingTemplates, "带电模板")
			}
		case 3:
			if templates.PostBoxTemplate == nil {
				missingTemplates = append(missingTemplates, "投函模板")
			}
		}
	}

	return missingTemplates
}
