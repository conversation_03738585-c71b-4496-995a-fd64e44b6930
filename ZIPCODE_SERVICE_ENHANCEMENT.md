# 邮编服务增强 - 支持完整地址信息存储

## 概述

原有的 ZipCodeService 只存储邮编作为 key，value 为空的 struct{}。现在已增强为存储完整的地址信息，包括中日英文地址数据，提供更丰富的功能。

## 主要改进

### 1. 数据结构增强

#### 新增 AddressInfo 结构体

```go
type AddressInfo struct {
    ZipCode           string `json:"zipCode"`           // 邮编
    PrefectureJA      string `json:"prefectureJA"`      // 都道府县（日文）
    PrefectureEN      string `json:"prefectureEN"`      // 都道府县（英文）
    CityJA            string `json:"cityJA"`            // 市区町村（日文）
    CityEN            string `json:"cityEN"`            // 市区町村（英文）
    TownJA            string `json:"townJA"`            // 町域名（日文）
    TownEN            string `json:"townEN"`            // 町域名（英文）
    FullAddressJA     string `json:"fullAddressJA"`     // 完整地址（日文）
    FullAddressEN     string `json:"fullAddressEN"`     // 完整地址（英文）
}
```

#### 存储方式变更

- **之前**: `map[string]struct{}` - 只存储邮编
- **现在**: `map[string]*AddressInfo` - 存储完整地址信息

### 2. 新增接口方法

#### ZipCodeService 接口扩展

```go
type ZipCodeService interface {
    // 原有方法保持不变
    IsValidFormat(zipCode string) bool
    IsExist(zipCode string) bool
    NormalizeZipCode(zipCode string) string
    FormatZipCode(zipCode string) string

    // 新增方法
    GetAddressInfo(zipCode string) (*AddressInfo, error)
    GetAddressByZipCode(zipCode string) string
    IsOkinawaPrefecture(zipCode string) bool
}
```

### 3. CSV 解析增强

#### 支持的 CSV 格式

假设 CSV 格式为：`邮编,都道府县(日),都道府県(英),市区町村(日),市区町村(英),町域(日),町域(英)`

#### 解析逻辑

- 自动解析各个地址字段
- 生成完整的日文和英文地址
- 支持字段缺失的情况

### 4. 冲绳县判断优化

#### 智能判断逻辑

```go
func (s *JapaneseZipCodeService) IsOkinawaPrefecture(zipCode string) bool {
    info, err := s.GetAddressInfo(zipCode)
    if err != nil {
        // 备用方案：通过邮编前缀判断（900-906）
        normalized := s.NormalizeZipCode(zipCode)
        if len(normalized) >= 3 {
            prefix := normalized[:3]
            return prefix >= "900" && prefix <= "906"
        }
        return false
    }

    // 优先方案：通过地址信息判断
    return strings.Contains(info.PrefectureJA, "沖縄") ||
           strings.Contains(info.PrefectureEN, "Okinawa")
}
```

## 新增功能

### 1. 地址查询 API

#### 接口信息

- **URL**: `GET /api/v1/address/info`
- **功能**: 根据邮编获取详细地址信息
- **认证**: 需要 Bearer Token 认证

#### 请求参数

```bash
curl -X GET "http://localhost:8080/api/v1/address/info?zipCode=100-0001" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### 响应示例

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "data": {
    "zipCode": "1000001",
    "prefectureJA": "東京都",
    "prefectureEN": "Tokyo",
    "cityJA": "千代田区",
    "cityEN": "Chiyoda-ku",
    "townJA": "千代田",
    "townEN": "Chiyoda",
    "fullAddressJA": "東京都千代田区千代田",
    "fullAddressEN": "Chiyoda, Chiyoda-ku, Tokyo"
  }
}
```

### 2. 创建订单接口增强

#### 偏远费计算优化

现在使用更精确的冲绳县判断逻辑：

```go
// 6. 计算偏远费（如果不是投函货物且为冲绳县邮编）
var remoteAreaSurcharge float64 = 0
if !isPostBoxShipmentType(req.ShipmentTypeID) && s.isOkinawaZipCode(normalizedZipCode) {
    remoteAreaSurcharge = 100.0
    logger.Debug("Applied Okinawa remote area surcharge", zap.Float64("amount", remoteAreaSurcharge))
}
```

## 文件变更列表

### 修改的文件

- `internal/domain/service/zipcode_service.go` - 主要增强
- `internal/app/service/manifest_service.go` - 使用新的冲绳县判断方法

### 新增的文件

- `internal/app/service/address_service.go` - 地址服务
- `internal/handler/address_handler.go` - 地址处理器
- `internal/router/router.go` - 添加地址路由

## 数据迁移建议

### CSV 文件格式要求

确保您的日本邮编 CSV 文件包含以下列（按顺序）：

1. 邮编
2. 都道府县（日文）
3. 都道府县（英文）
4. 市区町村（日文）
5. 市区町村（英文）
6. 町域名（日文）
7. 町域名（英文）

### 配置文件设置

在配置文件中设置 CSV 文件路径：

```yaml
zipcode:
  japan:
    filepath: "data/zipcode/japan_zipcode.csv"
```

## 使用示例

### 1. 获取地址信息

```go
// 通过ZipCodeService获取地址信息
addressInfo, err := zipCodeService.GetAddressInfo("100-0001")
if err == nil {
    fmt.Printf("完整地址: %s\n", addressInfo.FullAddressJA)
    fmt.Printf("都道府县: %s\n", addressInfo.PrefectureJA)
}
```

### 2. 判断是否为冲绳县

```go
// 判断邮编是否属于冲绳县
isOkinawa := zipCodeService.IsOkinawaPrefecture("900-0001")
if isOkinawa {
    fmt.Println("这是冲绳县的邮编")
}
```

### 3. 获取完整地址字符串

```go
// 获取完整的日文地址
fullAddress := zipCodeService.GetAddressByZipCode("150-0001")
fmt.Printf("地址: %s\n", fullAddress)
```

## 性能考虑

### 内存使用

- 原来只存储邮编字符串
- 现在存储完整的 AddressInfo 结构体
- 预计内存使用增加约 5-10 倍，但仍在可接受范围内

### 查询性能

- 查询性能保持 O(1)复杂度
- 新增的地址信息获取也是 O(1)复杂度

## 错误处理

### 错误码

- `201009`: 邮编格式无效
- `201010`: 邮编不存在
- `100001`: 系统错误

### 容错机制

- CSV 解析失败时跳过该行并记录日志
- 地址信息缺失时使用空字符串
- 冲绳县判断失败时使用邮编前缀备用方案

## 后续扩展建议

1. **地址补全功能**: 基于部分地址信息提供候选地址
2. **地址验证**: 验证用户输入的地址是否与邮编匹配
3. **区域分析**: 基于地址信息进行配送区域分析
4. **多语言支持**: 支持更多语言的地址信息

## 测试建议

1. **单元测试**: 测试各种邮编格式的解析和查询
2. **集成测试**: 测试地址查询 API 的完整流程
3. **性能测试**: 验证大量邮编数据的加载和查询性能
4. **容错测试**: 测试各种异常情况的处理
