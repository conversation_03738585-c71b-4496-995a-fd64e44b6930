package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// BillingCycleRepository 账期批次仓储接口
type BillingCycleRepository interface {
	// SaveBillingCycle 保存账期批次记录
	SaveBillingCycle(ctx context.Context, cycle *entity.BillingCycle) error
	
	// FindBillingCycleByID 根据ID查询账期批次
	FindBillingCycleByID(ctx context.Context, id int64) (*entity.BillingCycle, error)
	
	// FindBillingCycleByYearMonth 根据年月查询账期批次
	FindBillingCycleByYearMonth(ctx context.Context, year, month int) (*entity.BillingCycle, error)
	
	// CountBillingCycles 统计账期批次总数
	CountBillingCycles(ctx context.Context, filters interface{}) (int64, error)
	
	// FindBillingCycles 分页查询账期批次列表
	FindBillingCycles(ctx context.Context, filters interface{}, page, pageSize int) ([]*entity.BillingCycle, error)
	
	// CheckCycleExists 检查指定年月的账期批次是否已存在
	CheckCycleExists(ctx context.Context, year, month int) (bool, error)
	
	// UpdateBillingCycleStatistics 更新账期批次统计信息
	UpdateBillingCycleStatistics(ctx context.Context, cycleID int64, customerID int64, billCount int, billAmount float64) error
	
	// CalculateBillingCycleStatistics 重新计算账期批次的所有统计信息
	CalculateBillingCycleStatistics(ctx context.Context, cycleID int64) error
} 