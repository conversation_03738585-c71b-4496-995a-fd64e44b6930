package handler

import (
	"net/http"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ShipmentTypeHandler 货物类型处理器
type ShipmentTypeHandler struct {
	shipmentTypeService service.ShipmentTypeService
	logger              *zap.Logger
}

// NewShipmentTypeHandler 创建货物类型处理器实例
func NewShipmentTypeHandler(shipmentTypeService service.ShipmentTypeService, logger *zap.Logger) *ShipmentTypeHandler {
	return &ShipmentTypeHandler{
		shipmentTypeService: shipmentTypeService,
		logger:              logger,
	}
}

// GetAllShipmentTypes 获取所有货物类型
// @Summary 获取所有货物类型
// @Description 获取系统中的所有货物类型（包括启用和禁用的）
// @Tags 货物类型管理
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=service.GetAllShipmentTypesResponse} "成功"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/shipment-types [get]
func (h *ShipmentTypeHandler) GetAllShipmentTypes(c *gin.Context) {
	response, err := h.shipmentTypeService.GetAllShipmentTypes(c)
	if err != nil {
		h.logger.Error("获取所有货物类型失败", zap.Error(err))
		util.ResponseError(c, valueobject.ERROR_UNKNOWN, "获取货物类型失败", http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, response)
}

// GetActiveShipmentTypes 获取所有启用的货物类型
// @Summary 获取所有启用的货物类型
// @Description 获取系统中所有启用状态的货物类型，用于下拉框选择等场景
// @Tags 货物类型管理
// @Accept json
// @Produce json
// @Success 200 {object} util.Response{data=service.GetActiveShipmentTypesResponse} "成功"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/shipment-types/active [get]
func (h *ShipmentTypeHandler) GetActiveShipmentTypes(c *gin.Context) {
	response, err := h.shipmentTypeService.GetActiveShipmentTypes(c)
	if err != nil {
		h.logger.Error("获取启用货物类型失败", zap.Error(err))
		util.ResponseError(c, valueobject.ERROR_UNKNOWN, "获取启用货物类型失败", http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, response)
} 