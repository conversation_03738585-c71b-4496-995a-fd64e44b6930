package config

// OSSConfig 阿里云OSS配置
type OSSConfig struct {
	AccessKeyID     string `mapstructure:"access_key_id" yaml:"access_key_id"`
	AccessKeySecret string `mapstructure:"access_key_secret" yaml:"access_key_secret"`
	Endpoint        string `mapstructure:"endpoint" yaml:"endpoint"`
	Region          string `mapstructure:"region" yaml:"region"`
	BucketName      string `mapstructure:"bucket_name" yaml:"bucket_name"`
} 