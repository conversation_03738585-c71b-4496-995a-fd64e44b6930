# 财务模块 - 调整类型名称映射修正

## 修改概述

根据系统中定义的 `AdjustmentType` 常量，修正了财务调整快照查询接口中调整类型名称的映射逻辑，确保返回的中文名称与系统定义保持一致。

## 修改内容

### 1. 服务层调整类型映射修正

**文件**: `internal/app/service/billing_service.go`

**修改方法**: `getAdjustmentTypeName`

**修改前**:

```go
func (s *BillingServiceImpl) getAdjustmentTypeName(adjustmentType string) string {
	switch adjustmentType {
	case "ADDITION":
		return "增加"
	case "REDUCTION":
		return "减少"
	default:
		return fmt.Sprintf("未知类型(%s)", adjustmentType)
	}
}
```

**修改后**:

```go
func (s *BillingServiceImpl) getAdjustmentTypeName(adjustmentType string) string {
	switch adjustmentType {
	case "COMPENSATION":
		return "赔偿"
	case "REASSIGNMENT":
		return "改派"
	case "DESTRUCTION":
		return "销毁"
	case "RETURN":
		return "退回"
	case "FEE":
		return "费用"
	case "REBATE":
		return "返款"
	case "OTHER":
		return "其他"
	default:
		return fmt.Sprintf("未知类型(%s)", adjustmentType)
	}
}
```

### 2. API 文档更新

**文件**: `docs/财务模块_根据账单记录ID分页查询调整明细_API文档.md`

#### 调整类型映射表更新

**修改前**:
| 代码 | 名称 | 说明 |
| --------- | ---- | -------------- |
| ADDITION | 增加 | 增加费用的调整 |
| REDUCTION | 减少 | 减少费用的调整 |

**修改后**:
| 代码 | 名称 | 说明 |
| ------------ | ---- | ------------------------------ |
| COMPENSATION | 赔偿 | 赔偿类调整 |
| REASSIGNMENT | 改派 | 改派类调整 |
| DESTRUCTION | 销毁 | 销毁类调整 |
| RETURN | 退回 | 退回类调整 |
| FEE | 费用 | 费用类调整（如操作费、附加费） |
| REBATE | 返款 | 返款、折扣类调整 |
| OTHER | 其他 | 其他类型的调整 |

#### 示例响应数据更新

**修改前**:

```json
"adjustmentType": "ADDITION",
"adjustmentTypeName": "增加",
```

**修改后**:

```json
"adjustmentType": "COMPENSATION",
"adjustmentTypeName": "赔偿",
```

## 调整类型常量定义

根据 `internal/domain/valueobject/adjustment_type.go` 中的定义：

```go
const (
	// AdjustmentTypeCompensation 表示赔偿类调整
	AdjustmentTypeCompensation AdjustmentType = "COMPENSATION"
	// AdjustmentTypeReassignment 表示改派类调整
	AdjustmentTypeReassignment AdjustmentType = "REASSIGNMENT"
	// AdjustmentTypeDestruction 表示销毁类调整
	AdjustmentTypeDestruction AdjustmentType = "DESTRUCTION"
	// AdjustmentTypeReturn 表示退回类调整
	AdjustmentTypeReturn AdjustmentType = "RETURN"
	// AdjustmentTypeFee 表示费用类调整 (例如，操作费、附加费)
	AdjustmentTypeFee AdjustmentType = "FEE"
	// AdjustmentTypeRebate 表示返款、折扣类调整
	AdjustmentTypeRebate AdjustmentType = "REBATE"
	// AdjustmentTypeOther 表示其他类型的调整
	AdjustmentTypeOther AdjustmentType = "OTHER"
)
```

## 影响范围

### 直接影响

- `ListBillingAdjustmentSnapshots` 接口返回的调整类型名称
- API 文档中的调整类型说明和示例

### 间接影响

- 前端显示的调整类型中文名称
- 财务报表中的调整类型描述
- 用户界面的可读性提升

## 业务价值

### 1. 数据一致性

- 确保调整类型名称在整个系统中保持一致
- 避免前端显示错误的调整类型名称

### 2. 用户体验

- 提供准确的中文调整类型名称
- 便于用户理解不同类型的财务调整

### 3. 系统维护性

- 调整类型映射逻辑与系统常量定义保持同步
- 降低因类型不匹配导致的维护成本

## 测试建议

### 1. 单元测试

```go
func TestGetAdjustmentTypeName(t *testing.T) {
	service := &BillingServiceImpl{}

	testCases := []struct {
		input    string
		expected string
	}{
		{"COMPENSATION", "赔偿"},
		{"REASSIGNMENT", "改派"},
		{"DESTRUCTION", "销毁"},
		{"RETURN", "退回"},
		{"FEE", "费用"},
		{"REBATE", "返款"},
		{"OTHER", "其他"},
		{"UNKNOWN", "未知类型(UNKNOWN)"},
	}

	for _, tc := range testCases {
		result := service.getAdjustmentTypeName(tc.input)
		assert.Equal(t, tc.expected, result)
	}
}
```

### 2. 集成测试

- 创建包含不同调整类型的财务调整记录
- 生成账单并验证调整快照中的类型名称
- 调用查询接口验证返回的调整类型名称

### 3. API 测试

```bash
# 测试调整明细查询接口
curl -X GET "http://localhost:8080/api/v1/finance/billing/records/123/adjustment-snapshots" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## 编译验证

修改完成后，代码编译成功：

```bash
go build -v ./cmd/api-server/
# 输出：
# zebra-hub-system/internal/handler
# zebra-hub-system/internal/adapter/persistence
# zebra-hub-system/internal/router
# zebra-hub-system/cmd/api-server
```

## 总结

本次修改确保了财务调整类型名称映射的准确性和一致性，提升了系统的数据质量和用户体验。通过与系统常量定义保持同步，降低了维护成本并提高了系统的可靠性。
