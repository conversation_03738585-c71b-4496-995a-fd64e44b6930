# 财务模块 - 财务调整快照字段完善修复说明

## 问题描述

在生成账单时，`billing_financial_adjustment_snapshots` 表中的很多字段没有被正确赋值，特别是运单费用构成相关的字段：

- `ManifestBaseFreightFee` - 基础运费
- `ManifestFirstWeightFee` - 首重费用
- `ManifestContinuedWeightFee` - 续重费用

这些字段在原来的实现中被留空，导致财务调整快照的数据不完整。

## 修复内容

### 1. 完善费用构成字段赋值

**文件**: `internal/app/service/billing_service.go`

**修复的方法**: `generateFinancialAdjustmentSnapshots`

#### 修复前的问题

```go
// 设置费用信息（这里需要根据运单的运费模板重新计算）
// 注意：这里的费用是运单的原始费用，不是调整后的费用
snapshot.ManifestOverLengthSurcharge = &manifest.OverLengthSurcharge
snapshot.ManifestRemoteAreaSurcharge = &manifest.RemoteAreaSurcharge
snapshot.ManifestOverweightSurcharge = &manifest.OtherCost

// 计算原始总费用
originalTotalFee := manifest.Cost + manifest.OverLengthSurcharge + manifest.RemoteAreaSurcharge + manifest.OtherCost
snapshot.ManifestOriginalTotalFee = &originalTotalFee

// 这里可以根据需要计算基础运费、首重费用、续重费用等
// 但需要知道当时使用的运费模板，这里暂时留空
```

#### 修复后的实现

```go
// 尝试获取用户的默认运费模板来重新计算费用构成
var template *entity.ShippingFeeTemplate

// 根据货物类型获取用户的默认运费模板
templateType := int(cargoType)
templateID, err := s.shippingFeeTemplateRepo.GetUserTemplateID(ctx, userID, templateType)
if err == nil && templateID > 0 {
    // 获取模板详情
    templateDetail, templateErr := s.shippingFeeTemplateRepo.GetTemplateByID(ctx, templateID)
    if templateErr == nil {
        template = &templateDetail
    }
}

// 如果能获取到运费模板，重新计算首重费用和续重费用
if template != nil {
    fees := s.calculateFreightFees(manifest, template)
    snapshot.ManifestBaseFreightFee = &fees.BaseFreightFee
    snapshot.ManifestFirstWeightFee = &fees.FirstWeightFee
    snapshot.ManifestContinuedWeightFee = &fees.ContinuedWeightFee
} else {
    // 如果没有模板信息，使用运单的Cost作为基础运费
    snapshot.ManifestBaseFreightFee = &manifest.Cost

    // 暂时将基础运费作为首重费用，续重费用设为0
    snapshot.ManifestFirstWeightFee = &manifest.Cost
    continuedFee := 0.0
    snapshot.ManifestContinuedWeightFee = &continuedFee
}

// 设置其他费用信息
snapshot.ManifestOverLengthSurcharge = &manifest.OverLengthSurcharge
snapshot.ManifestRemoteAreaSurcharge = &manifest.RemoteAreaSurcharge
snapshot.ManifestOverweightSurcharge = &manifest.OtherCost

// 计算原始总费用
originalTotalFee := manifest.Cost + manifest.OverLengthSurcharge + manifest.RemoteAreaSurcharge + manifest.OtherCost
snapshot.ManifestOriginalTotalFee = &originalTotalFee
```

### 2. 运费模板获取逻辑

#### 获取流程

1. **确定货物类型**: 根据运单的 `ShippingFeeTemplateType` 字段确定货物类型
2. **查询用户模板**: 使用 `ShippingFeeTemplateRepository.GetUserTemplateID()` 获取用户配置的对应类型模板 ID
3. **获取模板详情**: 使用 `ShippingFeeTemplateRepository.GetTemplateByID()` 获取完整的模板信息
4. **重新计算费用**: 使用现有的 `calculateFreightFees()` 方法重新计算费用构成

#### 降级策略

如果无法获取到运费模板（用户未配置或模板不存在），则采用降级策略：

- `ManifestBaseFreightFee` = 运单的 `Cost` 字段
- `ManifestFirstWeightFee` = 运单的 `Cost` 字段
- `ManifestContinuedWeightFee` = 0

### 3. 字段完整性保证

修复后，财务调整快照将包含完整的运单费用构成信息：

| 字段名                        | 数据来源            | 说明       |
| ----------------------------- | ------------------- | ---------- |
| `ManifestBaseFreightFee`      | 重新计算或运单 Cost | 基础运费   |
| `ManifestFirstWeightFee`      | 重新计算或运单 Cost | 首重费用   |
| `ManifestContinuedWeightFee`  | 重新计算或 0        | 续重费用   |
| `ManifestOverLengthSurcharge` | 运单字段            | 超长费     |
| `ManifestRemoteAreaSurcharge` | 运单字段            | 偏远费     |
| `ManifestOverweightSurcharge` | 运单 OtherCost      | 超重费     |
| `ManifestOriginalTotalFee`    | 计算得出            | 原始总费用 |

## 技术实现细节

### 1. 依赖的仓储方法

- `ShippingFeeTemplateRepository.GetUserTemplateID(ctx, userID, templateType)` - 获取用户指定类型的模板 ID
- `ShippingFeeTemplateRepository.GetTemplateByID(ctx, templateID)` - 根据 ID 获取模板详情

### 2. 复用的计算逻辑

- `calculateFreightFees(manifest, template)` - 计算运费构成，返回 `FreightFees` 结构体
- `determineCargoType(manifest)` - 根据运单信息判断货物类型

### 3. 错误处理

- 如果获取模板失败，不会中断快照生成流程
- 采用降级策略确保快照数据的完整性
- 所有费用字段都有合理的默认值

## 影响范围

### 直接影响

- ✅ 财务调整快照生成功能
- ✅ 账单记录导出 Excel 功能（包含调整明细）
- ✅ 财务调整快照查询接口

### 数据完整性

- ✅ 新生成的快照将包含完整的费用构成信息
- ✅ 历史快照数据不受影响（保持向后兼容）
- ✅ Excel 导出时能正确显示费用明细

## 验证建议

1. **功能测试**:

   - 生成包含财务调整的账单
   - 验证快照中的费用字段是否正确填充
   - 导出 Excel 验证调整明细显示

2. **边界测试**:

   - 用户未配置运费模板的情况
   - 运费模板不存在的情况
   - 运单费用为 0 的情况

3. **数据一致性测试**:
   - 验证重新计算的费用与账单明细中的费用一致性
   - 验证快照中的总费用计算正确性

## 总结

这次修复解决了财务调整快照中费用构成字段缺失的问题，通过重新获取用户的运费模板并计算费用构成，确保了快照数据的完整性和准确性。同时保持了良好的错误处理和向后兼容性。
