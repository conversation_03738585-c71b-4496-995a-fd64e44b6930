package util

import (
	"time"

	"github.com/gin-gonic/gin"
	// "github.com/google/uuid" // 不再直接生成 uuid
	"go.uber.org/zap"
	// "zebra-hub-system/internal/middleware" // 移除对 middleware 的导入
)

// Response 统一响应结构
type Response struct {
	Success      bool        `json:"success"`      // 是否成功
	ErrorCode    int         `json:"errorCode"`    // 错误码
	ErrorMessage string      `json:"errorMessage"` // 错误信息
	RequestID    string      `json:"requestId"`    // 请求ID
	Timestamp    string      `json:"timestamp"`    // 时间戳
	Data         interface{} `json:"data"`         // 数据
}

// ResponseSuccess 成功响应
func ResponseSuccess(c *gin.Context, data interface{}) {
	requestID := c.GetString(RequestIDContextKey) // 直接使用 util.RequestIDContextKey (同包)
	resp := Response{
		Success:      true,
		ErrorCode:    100000,
		ErrorMessage: "操作成功",
		RequestID:    requestID,
		Timestamp:    time.Now().Format(DefaultTimeFormat),
		Data:         data,
	}
	zap.L().Info("Request success",
		zap.String(RequestIDContextKey, requestID), // 直接使用 util.RequestIDContextKey (同包)
		zap.String("path", c.Request.URL.Path),
	)
	c.JSON(200, resp) // 成功响应使用标准的200状态码
}

// ResponseError 错误响应
func ResponseError(c *gin.Context, errorCode int, errorMessage string, httpStatusCode int) {
	requestID := c.GetString(RequestIDContextKey)
	resp := Response{
		Success:      false,
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
		RequestID:    requestID,
		Timestamp:    time.Now().Format(DefaultTimeFormat),
		Data:         nil,
	}
	zap.L().Error("Request error",
		zap.String(RequestIDContextKey, requestID),
		zap.Int("errorCode", errorCode),
		zap.String("errorMessage", errorMessage),
		zap.Int("httpStatus", httpStatusCode),
		zap.String("path", c.Request.URL.Path),
	)
	c.JSON(httpStatusCode, resp) // 使用传入的HTTP状态码
} 

// ResponseSuccessWithStatus 带状态码的成功响应
func ResponseSuccessWithStatus(c *gin.Context, data interface{}, httpStatusCode int) {
	requestID := c.GetString(RequestIDContextKey)
	resp := Response{
		Success:      true,
		ErrorCode:    100000, // 假设 100000 仍然是通用成功码
		ErrorMessage: "操作成功",
		RequestID:    requestID,
		Timestamp:    time.Now().Format(DefaultTimeFormat),
		Data:         data,
	}
	zap.L().Info("Request success with status",
		zap.String(RequestIDContextKey, requestID),
		zap.Int("httpStatus", httpStatusCode),
		zap.String("path", c.Request.URL.Path),
	)
	c.JSON(httpStatusCode, resp)
}