package model

import (
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/util"
)

// UserPO 用户持久化对象
type UserPO struct {
	ID          int64      `gorm:"column:id;primaryKey;autoIncrement"`
	Username    string     `gorm:"column:username"`
	Password    string     `gorm:"column:password"`
	OpenID      string     `gorm:"column:openid"`
	SocialType  int        `gorm:"column:social_type"`
	Nickname    string     `gorm:"column:nickname"`
	Remark      string     `gorm:"column:remark"`
	City        string     `gorm:"column:city"`
	Province    string     `gorm:"column:province"`
	Country     string     `gorm:"column:country"`
	Email       string     `gorm:"column:email"`
	Phone       string     `gorm:"column:phone"`
	Gender      int        `gorm:"column:gender"`
	Avatar      string     `gorm:"column:avatar"`
	Status      int        `gorm:"column:status;default:1"`
	RoleID      int64      `gorm:"column:role_id"`
	LoginIP     string     `gorm:"column:login_ip"`
	LoginDate   *time.Time `gorm:"column:login_date"`
	CreateTime  *time.Time `gorm:"column:create_time"`
	UpdateTime  *time.Time `gorm:"column:update_time"`
}

// TableName 表名
func (UserPO) TableName() string {
	return "tb_user"
}

// ToEntity 转换为实体
func (u *UserPO) ToEntity() *entity.User {
	return &entity.User{
		ID:         u.ID,
		Username:   u.Username,
		Password:   u.Password,
		OpenID:     u.OpenID,
		SocialType: u.SocialType,
		Nickname:   u.Nickname,
		Remark:     u.Remark,
		City:       u.City,
		Province:   u.Province,
		Country:    u.Country,
		Email:      u.Email,
		Phone:      u.Phone,
		Gender:     u.Gender,
		Avatar:     u.Avatar,
		Status:     u.Status,
		RoleID:     u.RoleID,
		LoginIP:    u.LoginIP,
		LoginDate:  util.NewPointerFormattedTime(u.LoginDate),
		CreateTime: util.NewPointerFormattedTime(u.CreateTime),
		UpdateTime: util.NewPointerFormattedTime(u.UpdateTime),
	}
}

// FromEntity 从实体转换
func (u *UserPO) FromEntity(user *entity.User) {
	u.ID = user.ID
	u.Username = user.Username
	u.Password = user.Password
	u.OpenID = user.OpenID
	u.SocialType = user.SocialType
	u.Nickname = user.Nickname
	u.Remark = user.Remark
	u.City = user.City
	u.Province = user.Province
	u.Country = user.Country
	u.Email = user.Email
	u.Phone = user.Phone
	u.Gender = user.Gender
	u.Avatar = user.Avatar
	u.Status = user.Status
	u.RoleID = user.RoleID
	u.LoginIP = user.LoginIP
	if user.LoginDate.Time != nil {
		u.LoginDate = user.LoginDate.Time
	}
	if user.CreateTime.Time != nil {
		u.CreateTime = user.CreateTime.Time
	}
	if user.UpdateTime.Time != nil {
		u.UpdateTime = user.UpdateTime.Time
	}
} 