<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2adc8084-0138-47f6-8169-8e6f1445fbef" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/go/pkg/mod/golang.org/<EMAIL>-amd64" />
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2wq9TD0VIM8hZ0P7TpCnSmVCvDa" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Go Build.go build zebra-hub-system/cmd/api-server.executor": "Debug",
    "Go Build.go build zebra-hub-system/cmd/billing-consumer.executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.automatic.dependencies.download": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "D:/workspace/zebra-hub-system/data/zipcode",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\zebra-hub-system\data\zipcode" />
    </key>
  </component>
  <component name="RunManager" selected="Go Build.go build zebra-hub-system/cmd/billing-consumer">
    <configuration name="go build zebra-hub-system/cmd/api-server" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="zebra-hub-system" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="zebra-hub-system/cmd/api-server" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/cmd/api-server/main.go" />
      <method v="2" />
    </configuration>
    <configuration name="go build zebra-hub-system/cmd/billing-consumer" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="zebra-hub-system" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="zebra-hub-system/cmd/billing-consumer" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/cmd/billing-consumer/main.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build zebra-hub-system/cmd/billing-consumer" />
        <item itemvalue="Go Build.go build zebra-hub-system/cmd/api-server" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-266795519c66-911b893a1778-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-233.13135.104" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/app/service/manifest_service.go</url>
          <line>430</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/adapter/external_service/google_translate_service.go</url>
          <line>113</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DlvLineBreakpoint">
          <url>file://$PROJECT_DIR$/internal/app/service/billing_service.go</url>
          <line>1936</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="DlvErrorBreakpoint" />
      </default-breakpoints>
    </breakpoint-manager>
  </component>
</project>