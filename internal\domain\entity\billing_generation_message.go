package entity

import "time"

// UserShippingTemplate 用户特定的运费模板配置
type UserShippingTemplate struct {
	UserID          int64                 `json:"userId"`          // 用户ID
	GeneralTemplate *ShippingFeeTemplate  `json:"generalTemplate"` // 普货模板
	BatteryTemplate *ShippingFeeTemplate  `json:"batteryTemplate"` // 带电模板
	PostBoxTemplate *ShippingFeeTemplate  `json:"postBoxTemplate"` // 投函模板
}

// BillingGenerationMessage 账单生成消息体
type BillingGenerationMessage struct {
	TaskID             string                   `json:"taskId"`             // 任务ID
	BillingCycleID     int64                    `json:"billingCycleId"`     // 账期批次ID
	CustomerIDs        []int64                  `json:"customerIds"`        // 客户ID列表
	StartTime          time.Time                `json:"startTime"`          // 开始时间
	EndTime            time.Time                `json:"endTime"`            // 结束时间
	DueDate            *time.Time               `json:"dueDate,omitempty"`  // 付款截止日期
	Currency           string                   `json:"currency"`           // 货币单位
	Notes              *string                  `json:"notes,omitempty"`    // 备注
	GeneratedByUserID  *int64                   `json:"generatedByUserId,omitempty"` // 生成操作员ID
	
	// 模板配置
	UserTemplates      []UserShippingTemplate  `json:"userTemplates"`      // 用户特定模板列表
	
	// 消息元信息
	MessageID          string                   `json:"messageId"`          // 消息ID
	CreatedAt          time.Time                `json:"createdAt"`          // 消息创建时间
	RetryCount         int                      `json:"retryCount"`         // 重试次数
} 