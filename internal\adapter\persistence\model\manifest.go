package model

import (
	"time"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/util"
)

// ManifestPO 运单持久化对象
type ManifestPO struct {
	ID                       int64      `gorm:"column:id;primaryKey;autoIncrement"`
	ExpressNumber            string     `gorm:"column:express_number"`
	SawagaNumber             string     `gorm:"column:sawaga_number"`
	OrderNumber              string     `gorm:"column:order_number"`
	TransferredTrackingNumber string     `gorm:"column:transferred_tracking_number"`
	OrderNo                  string     `gorm:"column:order_no"`
	Weight                   float64    `gorm:"column:weight;type:decimal(19,2)"`
	Length                   float64    `gorm:"column:length;type:decimal(19,2)"`
	Width                    float64    `gorm:"column:width;type:decimal(19,2)"`
	Height                   float64    `gorm:"column:height;type:decimal(19,2)"`
	DimensionalWeight        float64    `gorm:"column:dimensional_weight;type:decimal(19,2)"`
	Cost                     float64    `gorm:"column:cost;type:decimal(19,2)"`
	OverLengthSurcharge      float64    `gorm:"column:over_length_surcharge;type:decimal(19,2)"`
	RemoteAreaSurcharge      float64    `gorm:"column:remote_area_surcharge;type:decimal(19,2)"`
	OtherCostName            string     `gorm:"column:other_cost_name"`
	OtherCost                float64    `gorm:"column:other_cost;type:decimal(19,2)"`
	FeeTotal                 float64    `gorm:"column:fee_total;type:decimal(19,2)"`
	Value                    float64    `gorm:"column:value;type:decimal(19,2)"`
	Description              string     `gorm:"column:description"`
	ReceiverPhone            string     `gorm:"column:receiver_phone"`
	ReceiverZipCode          string     `gorm:"column:receiver_zip_code"`
	ReceiverAddress          string     `gorm:"column:receiver_address"`
	ReceiverName             string     `gorm:"column:receiver_name"`
	ReceiverEnName           string     `gorm:"column:receiver_en_name"`
	ReceiverEnAddress        string     `gorm:"column:receiver_en_address"`
	PrefectureName           string     `gorm:"column:prefecture_name"`
	MunicipalName            string     `gorm:"column:municipal_name"`
	LocalitiesName           string     `gorm:"column:localities_name"`
	PrefectureEnName         string     `gorm:"column:prefecture_en_name"`
	MunicipalEnName          string     `gorm:"column:municipal_en_name"`
	LocalitiesEnName         string     `gorm:"column:localities_en_name"`
	PackageNo                string     `gorm:"column:package_no"`
	SawagaSiteCode           string     `gorm:"column:sawaga_site_code"`
	Remark                   string     `gorm:"column:remark"`
	UserID                   int64      `gorm:"column:user_id"`
	Status                   int        `gorm:"column:status"`
	TrackingStatus           int        `gorm:"column:tracking_status"`
	IsOnline                 bool       `gorm:"column:is_online;default:0"`
	LogisticsJourney         string     `gorm:"column:logistics_journey"`
	TrackingUpdateTime       *time.Time `gorm:"column:tracking_update_time"`
	SourceType               int        `gorm:"column:source_type"`
	CarrierCode              string     `gorm:"column:carrier_code"`
	DestinationCode          string     `gorm:"column:destination_code"`
	ShippingFeeTemplateType  int        `gorm:"column:shipping_fee_template_type"`
	CreatorID                int64      `gorm:"column:creator_id"`
	PickUpBy                 int64      `gorm:"column:pick_up_by"`
	IsDelete                 bool       `gorm:"column:is_delete"`
	MasterBillID             int64      `gorm:"column:master_bill_id;default:0"`
	MasterBillNumber         string     `gorm:"column:master_bill_number;default:''"`
	PreRegistrationBatchID   string     `gorm:"column:pre_registration_batch_id"`
	PickUpTime               *time.Time `gorm:"column:pick_up_time"`
	ShipmentTime             *time.Time `gorm:"column:shipment_time"`
	DeliveredTime            *time.Time `gorm:"column:delivered_time"`
	CreateTime               *time.Time `gorm:"column:create_time"`
	UpdateTime               *time.Time `gorm:"column:update_time"`
	PackingStatus            int        `gorm:"column:packing_status;default:0"`
	ParcelSortingRecordID    int64      `gorm:"column:parcel_sorting_record_id"`
	ValidationStatus         int        `gorm:"column:validation_status;default:0"`
	ValidationError          string     `gorm:"column:validation_error"`

	Items []ManifestItemPO `gorm:"foreignKey:ManifestID"`
}

// TableName 表名
func (ManifestPO) TableName() string {
	return "tb_manifest"
}

// ToEntity 转换为实体
func (po *ManifestPO) ToEntity() *entity.Manifest {
	var createTime, updateTime time.Time
	if po.CreateTime != nil {
		createTime = *po.CreateTime
	}
	if po.UpdateTime != nil {
		updateTime = *po.UpdateTime
	}

	manifest := &entity.Manifest{
		ID:                       po.ID,
		ExpressNumber:            po.ExpressNumber,
		SawagaNumber:             po.SawagaNumber,
		OrderNumber:              po.OrderNumber,
		TransferredTrackingNumber: po.TransferredTrackingNumber,
		OrderNo:                  po.OrderNo,
		Weight:                   po.Weight,
		Length:                   po.Length,
		Width:                    po.Width,
		Height:                   po.Height,
		DimensionalWeight:        po.DimensionalWeight,
		Cost:                     po.Cost,
		OverLengthSurcharge:      po.OverLengthSurcharge,
		RemoteAreaSurcharge:      po.RemoteAreaSurcharge,
		OtherCostName:            po.OtherCostName,
		OtherCost:                po.OtherCost,
		FeeTotal:                 po.FeeTotal,
		Value:                    po.Value,
		Description:              po.Description,
		ReceiverPhone:            po.ReceiverPhone,
		ReceiverZipCode:          po.ReceiverZipCode,
		ReceiverAddress:          po.ReceiverAddress,
		ReceiverName:             po.ReceiverName,
		ReceiverEnName:           po.ReceiverEnName,
		ReceiverEnAddress:        po.ReceiverEnAddress,
		PrefectureName:           po.PrefectureName,
		MunicipalName:            po.MunicipalName,
		LocalitiesName:           po.LocalitiesName,
		PrefectureEnName:         po.PrefectureEnName,
		MunicipalEnName:          po.MunicipalEnName,
		LocalitiesEnName:         po.LocalitiesEnName,
		PackageNo:                po.PackageNo,
		SawagaSiteCode:           po.SawagaSiteCode,
		Remark:                   po.Remark,
		UserID:                   po.UserID,
		Status:                   po.Status,
		TrackingStatus:           po.TrackingStatus,
		IsOnline:                 po.IsOnline,
		LogisticsJourney:         po.LogisticsJourney,
		SourceType:               po.SourceType,
		CarrierCode:              po.CarrierCode,
		DestinationCode:          po.DestinationCode,
		ShippingFeeTemplateType:  po.ShippingFeeTemplateType,
		CreatorID:                po.CreatorID,
		PickUpBy:                 po.PickUpBy,
		IsDelete:                 po.IsDelete,
		MasterBillID:             po.MasterBillID,
		MasterBillNumber:         po.MasterBillNumber,
		PreRegistrationBatchID:   po.PreRegistrationBatchID,
		PackingStatus:            po.PackingStatus,
		ParcelSortingRecordID:    po.ParcelSortingRecordID,
		ValidationStatus:         po.ValidationStatus,
		ValidationError:          po.ValidationError,

		// 时间字段转换 - PointerFormattedTime类型
		TrackingUpdateTime:       util.NewPointerFormattedTime(po.TrackingUpdateTime),
		PickUpTime:               util.NewPointerFormattedTime(po.PickUpTime),
		ShipmentTime:             util.NewPointerFormattedTime(po.ShipmentTime),
		DeliveredTime:            util.NewPointerFormattedTime(po.DeliveredTime),
		
		// 时间字段转换 - time.Time类型
		CreateTime:               createTime,
		UpdateTime:               updateTime,
	}

	if len(po.Items) > 0 {
		manifest.Items = make([]entity.ManifestItem, len(po.Items))
		for i, itemPO := range po.Items {
			manifest.Items[i] = *itemPO.ToEntity()
		}
	}

	return manifest
}

// ManifestItemPO 运单物品持久化对象
type ManifestItemPO struct {
	ID          int64      `gorm:"column:id;primaryKey;autoIncrement"`
	ManifestID  int64      `gorm:"column:manifest_id"`
	Name        string     `gorm:"column:name"`
	NameEn      string     `gorm:"column:name_en"`
	Weight      float64    `gorm:"column:weight;type:decimal(19,2)"`
	Quantity    int        `gorm:"column:quantity"`
	Price       float64    `gorm:"column:price;type:decimal(19,2)"`
	Value       float64    `gorm:"column:value;type:decimal(19,2)"`
	CreateTime  *time.Time `gorm:"column:create_time"`
	UpdateTime  *time.Time `gorm:"column:update_time"`
}

// TableName 表名
func (ManifestItemPO) TableName() string {
	return "tb_manifest_item"
}

// ToEntity 转换为实体
func (po *ManifestItemPO) ToEntity() *entity.ManifestItem {
	return &entity.ManifestItem{
		ID:         po.ID,
		ManifestID: po.ManifestID,
		Name:       po.Name,
		NameEn:     po.NameEn,
		Weight:     po.Weight,
		Quantity:   po.Quantity,
		Price:      po.Price,
		Value:      po.Value,
		CreateTime: util.NewPointerFormattedTime(po.CreateTime),
		UpdateTime: util.NewPointerFormattedTime(po.UpdateTime),
	}
} 