package persistence

import (
	"context"
	"fmt"
	"time"

	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"gorm.io/gorm"
)

// ManifestFinancialAdjustmentRepositoryImpl 运单财务调整记录仓储实现
type ManifestFinancialAdjustmentRepositoryImpl struct {
	db *gorm.DB
}

// NewManifestFinancialAdjustmentRepository 创建运单财务调整记录仓储
func NewManifestFinancialAdjustmentRepository(db *gorm.DB) repository.ManifestFinancialAdjustmentRepository {
	return &ManifestFinancialAdjustmentRepositoryImpl{db: db}
}

// FindByManifestID 根据运单ID查询财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) FindByManifestID(ctx context.Context, manifestID int64) ([]*entity.ManifestFinancialAdjustment, error) {
	var adjustmentPOs []*model.ManifestFinancialAdjustmentPO

	if err := r.db.WithContext(ctx).
		Where("manifest_id = ?", manifestID).
		Order("effective_date DESC, id DESC").
		Find(&adjustmentPOs).Error; err != nil {
		return nil, fmt.Errorf("failed to find financial adjustments by manifest ID: %w", err)
	}

	// 转换为实体
	adjustments := make([]*entity.ManifestFinancialAdjustment, 0, len(adjustmentPOs))
	for _, po := range adjustmentPOs {
		entity, err := po.ToEntity()
		if err != nil {
			return nil, fmt.Errorf("failed to convert PO to entity: %w", err)
		}
		adjustments = append(adjustments, entity)
	}

	return adjustments, nil
}

// FindByID 根据ID查询财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.ManifestFinancialAdjustment, error) {
	var po model.ManifestFinancialAdjustmentPO
	if err := r.db.WithContext(ctx).First(&po, id).Error; err != nil {
		return nil, err
	}
	return po.ToEntity()
}

// FindByIDs 根据ID列表查询财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) FindByIDs(ctx context.Context, ids []int64) ([]*entity.ManifestFinancialAdjustment, error) {
	if len(ids) == 0 {
		return []*entity.ManifestFinancialAdjustment{}, nil
	}

	var adjustmentPOs []*model.ManifestFinancialAdjustmentPO

	if err := r.db.WithContext(ctx).
		Where("id IN ?", ids).
		Find(&adjustmentPOs).Error; err != nil {
		return nil, fmt.Errorf("failed to find financial adjustments by IDs: %w", err)
	}

	// 转换为实体
	adjustments := make([]*entity.ManifestFinancialAdjustment, 0, len(adjustmentPOs))
	for _, po := range adjustmentPOs {
		entity, err := po.ToEntity()
		if err != nil {
			return nil, fmt.Errorf("failed to convert PO to entity: %w", err)
		}
		adjustments = append(adjustments, entity)
	}

	return adjustments, nil
}

// Save 保存财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) Save(ctx context.Context, adjustment *entity.ManifestFinancialAdjustment) error {
	// 先将实体转换为PO
	po := &model.ManifestFinancialAdjustmentPO{}
	if err := po.FromEntity(adjustment); err != nil {
		return fmt.Errorf("failed to convert entity to PO: %w", err)
	}

	// 执行创建操作
	if err := r.db.WithContext(ctx).Create(po).Error; err != nil {
		return fmt.Errorf("failed to create financial adjustment: %w", err)
	}

	// 设置自增ID
	adjustment.ID = po.ID
	return nil
}

// Update 更新财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) Update(ctx context.Context, adjustment *entity.ManifestFinancialAdjustment) error {
	// 先将实体转换为PO
	po := &model.ManifestFinancialAdjustmentPO{}
	if err := po.FromEntity(adjustment); err != nil {
		return fmt.Errorf("failed to convert entity to PO: %w", err)
	}

	// 构建更新字段
	updates := map[string]interface{}{
		"adjustment_type":     adjustment.AdjustmentType,
		"description":         adjustment.Description,
		"additional_details":  po.AdditionalDetails,
		"amount":              adjustment.Amount,
		"currency":            adjustment.Currency,
		"effective_date":      adjustment.EffectiveDate,
		"customer_account_id": adjustment.CustomerAccountID,
		"is_void":            adjustment.IsVoid,
		"void_reason":         adjustment.VoidReason,
		"voided_by":           adjustment.VoidedBy,
		"voided_time":         adjustment.VoidedTime,
	}

	// 执行更新
	result := r.db.WithContext(ctx).
		Model(&model.ManifestFinancialAdjustmentPO{}).
		Where("id = ?", adjustment.ID).
		Updates(updates)

	if result.Error != nil {
		return fmt.Errorf("failed to update financial adjustment: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}

// CountByFilters 根据过滤条件统计财务调整记录数量
func (r *ManifestFinancialAdjustmentRepositoryImpl) CountByFilters(
	ctx context.Context,
	manifestID *int64,
	adjustmentType *string,
	effectiveDateStart *time.Time,
	effectiveDateEnd *time.Time,
	customerAccountID *int64,
	trackingNumber *string,
) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).
		Model(&model.ManifestFinancialAdjustmentPO{}).
		Joins("LEFT JOIN tb_manifest ON tb_manifest.id = manifest_financial_adjustments.manifest_id")

	if manifestID != nil {
		query = query.Where("manifest_financial_adjustments.manifest_id = ?", *manifestID)
	}
	if adjustmentType != nil {
		query = query.Where("manifest_financial_adjustments.adjustment_type = ?", *adjustmentType)
	}
	// 处理生效日期范围查询
	if effectiveDateStart != nil || effectiveDateEnd != nil {
		if effectiveDateStart != nil && effectiveDateEnd != nil {
			// 同时提供开始和结束日期，查询这个范围内的记录
			query = query.Where("manifest_financial_adjustments.effective_date BETWEEN ? AND ?", 
				effectiveDateStart, effectiveDateEnd)
		} else if effectiveDateStart != nil {
			// 只提供开始日期，查询该日期之后的记录
			query = query.Where("manifest_financial_adjustments.effective_date >= ?", effectiveDateStart)
		} else if effectiveDateEnd != nil {
			// 只提供结束日期，查询该日期之前的记录
			query = query.Where("manifest_financial_adjustments.effective_date <= ?", effectiveDateEnd)
		}
	}
	if customerAccountID != nil {
		query = query.Where("manifest_financial_adjustments.customer_account_id = ?", *customerAccountID)
	}
	if trackingNumber != nil && *trackingNumber != "" {
		searchPattern := "%" + *trackingNumber + "%"
		query = query.Where("tb_manifest.express_number LIKE ? OR tb_manifest.transferred_tracking_number LIKE ?",
			searchPattern, searchPattern)
	}

	err := query.Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to count manifest financial adjustments: %w", err)
	}

	return count, nil
}

// adjustmentQueryResult 查询结果结构
type adjustmentQueryResult struct {
	model.ManifestFinancialAdjustmentPO
	ExpressNumber             string `gorm:"column:express_number"`
	TransferredTrackingNumber string `gorm:"column:transferred_tracking_number"`
}

// FindByFilters 根据过滤条件查询财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) FindByFilters(
	ctx context.Context,
	manifestID *int64,
	adjustmentType *string,
	effectiveDateStart *time.Time,
	effectiveDateEnd *time.Time,
	customerAccountID *int64,
	trackingNumber *string,
	orderBy string,
	isAsc bool,
	page, pageSize int,
) ([]*entity.ManifestFinancialAdjustment, error) {
	var results []*adjustmentQueryResult

	// 构建基础查询
	query := r.db.WithContext(ctx).
		Model(&model.ManifestFinancialAdjustmentPO{}).
		Select("manifest_financial_adjustments.*, tb_manifest.express_number, tb_manifest.transferred_tracking_number").
		Joins("LEFT JOIN tb_manifest ON tb_manifest.id = manifest_financial_adjustments.manifest_id")

	// 添加过滤条件
	if manifestID != nil {
		query = query.Where("manifest_financial_adjustments.manifest_id = ?", *manifestID)
	}
	if adjustmentType != nil {
		query = query.Where("manifest_financial_adjustments.adjustment_type = ?", *adjustmentType)
	}

	// 处理生效日期范围查询
	if effectiveDateStart != nil || effectiveDateEnd != nil {
		if effectiveDateStart != nil && effectiveDateEnd != nil {
			// 同时提供开始和结束日期，查询这个范围内的记录
			query = query.Where("manifest_financial_adjustments.effective_date BETWEEN ? AND ?", 
				effectiveDateStart, effectiveDateEnd)
		} else if effectiveDateStart != nil {
			// 只提供开始日期，查询该日期之后的记录
			query = query.Where("manifest_financial_adjustments.effective_date >= ?", effectiveDateStart)
		} else if effectiveDateEnd != nil {
			// 只提供结束日期，查询该日期之前的记录
			query = query.Where("manifest_financial_adjustments.effective_date <= ?", effectiveDateEnd)
		}
	}

	if customerAccountID != nil {
		query = query.Where("manifest_financial_adjustments.customer_account_id = ?", *customerAccountID)
	}

	if trackingNumber != nil && *trackingNumber != "" {
		searchPattern := "%" + *trackingNumber + "%"
		query = query.Where("tb_manifest.express_number LIKE ? OR tb_manifest.transferred_tracking_number LIKE ?",
			searchPattern, searchPattern)
	}

	// 添加排序和分页
	orderDirection := "DESC"
	if isAsc {
		orderDirection = "ASC"
	}

	// 执行查询
	err := query.
		Order(fmt.Sprintf("manifest_financial_adjustments.%s %s", orderBy, orderDirection)).
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&results).Error

	if err != nil {
		return nil, fmt.Errorf("failed to find manifest financial adjustments: %w", err)
	}

	// 转换为实体
	adjustments := make([]*entity.ManifestFinancialAdjustment, 0, len(results))
	for _, result := range results {
		// 转换为实体
		entity, err := result.ToEntity()
		if err != nil {
			return nil, fmt.Errorf("failed to convert PO to entity: %w", err)
		}
		adjustments = append(adjustments, entity)
	}

	return adjustments, nil
}

// Delete 删除财务调整记录
func (r *ManifestFinancialAdjustmentRepositoryImpl) Delete(ctx context.Context, id int64) error {
	result := r.db.WithContext(ctx).Delete(&model.ManifestFinancialAdjustmentPO{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete financial adjustment: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}

	return nil
}
