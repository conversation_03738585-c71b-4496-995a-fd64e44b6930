# 账期批次管理 API 文档

## 概述

账期批次管理模块提供了创建、查询和管理账期批次的功能。账期批次用于按月度对账单进行分组管理，支持统计信息和状态跟踪。

## 数据库表结构

```sql
CREATE TABLE billing_cycles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '账期批次ID',
    cycle_year INT NOT NULL COMMENT '账期年份 (例如: 2023)',
    cycle_month TINYINT NOT NULL COMMENT '账期月份 (1-12)',
    cycle_name VARCHAR(100) NULL COMMENT '账期名称 (例如: 2023年4月账单)',
    status VARCHAR(30) NOT NULL DEFAULT 'PENDING' COMMENT '账期批次状态',
    total_customers_billed INT NULL COMMENT '本周期内出账客户数',
    total_bills_generated INT NULL COMMENT '本周期内生成的账单总数',
    total_billed_amount DECIMAL(19, 2) NULL COMMENT '本周期内账单总金额合计',
    total_amount_paid_in_cycle DECIMAL(19, 2) NULL COMMENT '本周期内已付金额合计',
    total_balance_due_in_cycle DECIMAL(19, 2) NULL COMMENT '本周期内待支付金额合计',
    generated_by_user_id BIGINT NULL COMMENT '批次生成操作员ID',
    generation_start_time DATETIME NULL COMMENT '批次账单开始生成时间',
    generation_end_time DATETIME NULL COMMENT '批次账单完成生成时间',
    notes TEXT NULL COMMENT '批次备注',
    create_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_billing_cycle_month_type (cycle_year, cycle_month),
    INDEX idx_cycle_status (status)
) COMMENT = '月度账期批次/结算周期表';
```

## 状态枚举

- `PENDING`: 待处理
- `PROCESSING`: 处理中
- `COMPLETED`: 已完成
- `FAILED`: 失败
- `CANCELLED`: 已取消

## API 接口

### 1. 创建账期批次

**接口地址**: `POST /api/v1/billing/cycles`

**请求参数**:

```json
{
  "cycleYear": 2024, // 必填，账期年份 (2020-2100)
  "cycleMonth": 1, // 必填，账期月份 (1-12)
  "cycleName": "2024年1月账期", // 可选，账期名称（不填则自动生成）
  "notes": "备注信息" // 可选，批次备注
}
```

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "req-123456",
  "timestamp": "2024-01-15 10:30:00",
  "data": {
    "billingCycleId": 1,
    "cycleName": "2024年1月账期",
    "message": "成功创建2024年1月账期账期批次"
  }
}
```

**业务规则**:

- 同一年月的账期批次只能创建一次
- 账期名称如果不提供，会自动生成为"YYYY 年 MM 月账期"格式
- 创建时状态默认为`PENDING`
- 自动记录当前登录用户为操作员

### 2. 分页查询账期批次列表

**接口地址**: `GET /api/v1/billing/cycles`

**查询参数**:

- `page`: 页码，默认 1
- `pageSize`: 每页数量，默认 10，最大 100
- `cycleYear`: 账期年份（可选）
- `cycleMonth`: 账期月份（可选）
- `status`: 账期批次状态（可选）
- `cycleName`: 账期名称模糊查询（可选）
- `generatedByUserId`: 生成操作员 ID（可选）

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "req-123456",
  "timestamp": "2024-01-15 10:30:00",
  "data": {
    "total": 25,
    "list": [
      {
        "id": 1,
        "cycleYear": 2024,
        "cycleMonth": 1,
        "cycleName": "2024年1月账期",
        "status": "PENDING",
        "statusName": "待处理",
        "totalCustomersBilled": null,
        "totalBillsGenerated": null,
        "totalBilledAmount": null,
        "totalAmountPaidInCycle": null,
        "totalBalanceDueInCycle": null,
        "generatedByUserId": 1001,
        "generatedByNickname": "管理员",
        "generationStartTime": null,
        "generationEndTime": null,
        "notes": "备注信息",
        "createTime": "2024-01-15 10:30:00",
        "updateTime": "2024-01-15 10:30:00"
      }
    ]
  }
}
```

**排序规则**: 按年月倒序排列（最新的在前）

### 3. 获取账期批次详情

**接口地址**: `GET /api/v1/billing/cycles/{billingCycleId}`

**路径参数**:

- `billingCycleId`: 账期批次 ID

**响应示例**:

```json
{
  "success": true,
  "errorCode": 100000,
  "errorMessage": "操作成功",
  "requestId": "req-123456",
  "timestamp": "2024-01-15 10:30:00",
  "data": {
    "billingCycle": {
      "id": 1,
      "cycleYear": 2024,
      "cycleMonth": 1,
      "cycleName": "2024年1月账期",
      "status": "COMPLETED",
      "statusName": "已完成",
      "totalCustomersBilled": 150,
      "totalBillsGenerated": 150,
      "totalBilledAmount": 125000.5,
      "totalAmountPaidInCycle": 100000.0,
      "totalBalanceDueInCycle": 25000.5,
      "generatedByUserId": 1001,
      "generatedByNickname": "管理员",
      "generationStartTime": "2024-01-15 09:00:00",
      "generationEndTime": "2024-01-15 10:30:00",
      "notes": "2024年1月账期批次",
      "createTime": "2024-01-15 08:30:00",
      "updateTime": "2024-01-15 10:30:00"
    }
  }
}
```

## 错误码说明

- `100000`: 操作成功
- `100002`: 参数错误
- `100006`: 资源不存在
- `100001`: 系统错误

## 使用场景

1. **月度结算准备**: 在每月初创建新的账期批次，为该月的账单生成做准备
2. **账期管理**: 通过列表查询管理不同月份的账期批次状态
3. **统计分析**: 查看每个账期的客户数量、账单数量、金额统计等信息
4. **操作审计**: 记录账期批次的创建人员和时间，便于审计追踪

## 注意事项

1. 每个年月组合只能创建一个账期批次
2. 账期批次创建后，相关的统计信息需要通过其他业务流程更新
3. 账期批次的状态变更需要通过其他接口进行（如账单生成流程）
4. 删除账期批次功能暂未提供，如需要可联系管理员处理
