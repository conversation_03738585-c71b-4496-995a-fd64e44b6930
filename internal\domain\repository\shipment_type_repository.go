package repository

import (
	"context"
	"zebra-hub-system/internal/domain/entity"
)

// ShipmentTypeRepository 货物类型仓储接口
type ShipmentTypeRepository interface {
	// GetAllShipmentTypes 获取所有货物类型
	GetAllShipmentTypes(ctx context.Context) ([]*entity.ShipmentType, error)
	
	// GetShipmentTypeByID 根据ID获取货物类型
	GetShipmentTypeByID(ctx context.Context, id int64) (*entity.ShipmentType, error)
	
	// GetActiveShipmentTypes 获取所有启用的货物类型
	GetActiveShipmentTypes(ctx context.Context) ([]*entity.ShipmentType, error)
} 