package model

import (
	"time"
	"zebra-hub-system/internal/domain/entity"
)

// BillingGenerationTaskPO 账单生成任务数据库模型
type BillingGenerationTaskPO struct {
	TaskID                string     `gorm:"column:task_id;type:varchar(36);primaryKey;comment:任务UUID" json:"taskId"`
	BillingCycleID        int64      `gorm:"column:billing_cycle_id;type:bigint;not null;comment:关联的账期批次ID" json:"billingCycleId"`
	TargetCustomerIds     *string    `gorm:"column:target_customer_ids;type:text;comment:目标客户ID列表" json:"targetCustomerIds"`
	Status                string     `gorm:"column:status;type:varchar(30);not null;default:'PENDING';comment:任务状态" json:"status"`
	ProgressPercentage    int        `gorm:"column:progress_percentage;type:int;not null;default:0;comment:进度百分比" json:"progressPercentage"`
	TotalItemsToProcess   *int       `gorm:"column:total_items_to_process;type:int;comment:预计总处理项数" json:"totalItemsToProcess"`
	ItemsProcessedCount   int        `gorm:"column:items_processed_count;type:int;not null;default:0;comment:已处理项数" json:"itemsProcessedCount"`
	ErrorMessage          *string    `gorm:"column:error_message;type:text;comment:错误信息" json:"errorMessage"`
	SubmittedByUserID     *int64     `gorm:"column:submitted_by_user_id;type:bigint;comment:任务提交者ID" json:"submittedByUserId"`
	SubmitTime            time.Time  `gorm:"column:submit_time;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:提交时间" json:"submitTime"`
	StartTime             *time.Time `gorm:"column:start_time;type:datetime;comment:处理开始时间" json:"startTime"`
	EndTime               *time.Time `gorm:"column:end_time;type:datetime;comment:处理结束时间" json:"endTime"`
}

// TableName 指定表名
func (BillingGenerationTaskPO) TableName() string {
	return "billing_generation_tasks"
}

// ToEntity 转换为领域实体
func (po *BillingGenerationTaskPO) ToEntity() *entity.BillingGenerationTask {
	return &entity.BillingGenerationTask{
		TaskID:                po.TaskID,
		BillingCycleID:        po.BillingCycleID,
		TargetCustomerIds:     po.TargetCustomerIds,
		Status:                entity.TaskStatus(po.Status),
		ProgressPercentage:    po.ProgressPercentage,
		TotalItemsToProcess:   po.TotalItemsToProcess,
		ItemsProcessedCount:   po.ItemsProcessedCount,
		ErrorMessage:          po.ErrorMessage,
		SubmittedByUserID:     po.SubmittedByUserID,
		SubmitTime:            po.SubmitTime,
		StartTime:             po.StartTime,
		EndTime:               po.EndTime,
	}
}

// FromEntity 从领域实体转换
func (po *BillingGenerationTaskPO) FromEntity(task *entity.BillingGenerationTask) {
	po.TaskID = task.TaskID
	po.BillingCycleID = task.BillingCycleID
	po.TargetCustomerIds = task.TargetCustomerIds
	po.Status = string(task.Status)
	po.ProgressPercentage = task.ProgressPercentage
	po.TotalItemsToProcess = task.TotalItemsToProcess
	po.ItemsProcessedCount = task.ItemsProcessedCount
	po.ErrorMessage = task.ErrorMessage
	po.SubmittedByUserID = task.SubmittedByUserID
	po.SubmitTime = task.SubmitTime
	po.StartTime = task.StartTime
	po.EndTime = task.EndTime
} 