package config

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

// InitRedisClient 初始化Redis客户端
func InitRedisClient(cfg *Redis, logger *zap.Logger) (*redis.Client, error) {
	if cfg == nil {
		logger.Warn("Redis配置为空")
		return nil, fmt.Errorf("Redis配置为空")
	}

	addr := fmt.Sprintf("%s:%d", cfg.Host, cfg.Port)
	
	client := redis.NewClient(&redis.Options{
		Addr:         addr,
		Password:     cfg.Password,
		DB:           cfg.DB,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 2,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		logger.Error("Redis连接失败", zap.Error(err), zap.String("addr", addr))
		return nil, fmt.Errorf("Redis连接失败: %v", err)
	}

	logger.Info("Redis连接成功", zap.String("addr", addr))
	return client, nil
} 