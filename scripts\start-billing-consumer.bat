@echo off
REM 账单生成消费者启动脚本 (Windows版本)

setlocal enabledelayedexpansion

REM 设置工作目录
cd /d "%~dp0\.."

REM 环境变量设置
if "%GO_ENV%"=="" set GO_ENV=production

REM 创建必要的目录
if not exist "logs" mkdir logs
if not exist "bin" mkdir bin

REM 文件路径
set PID_FILE=logs\billing-consumer.pid
set LOG_FILE=logs\billing-consumer.log

REM 函数：检查进程是否运行
:is_running
if exist "%PID_FILE%" (
    set /p PID=<"%PID_FILE%"
    tasklist /FI "PID eq !PID!" 2>NUL | find /I "!PID!" >NUL
    if !ERRORLEVEL! equ 0 (
        exit /b 0
    ) else (
        del "%PID_FILE%" 2>NUL
        exit /b 1
    )
) else (
    exit /b 1
)

REM 主逻辑
if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
goto usage

:start
call :is_running
if !ERRORLEVEL! equ 0 (
    set /p RUNNING_PID=<"%PID_FILE%"
    echo 账单生成消费者已经在运行中 ^(PID: !RUNNING_PID!^)
    goto end
)

echo 正在启动账单生成消费者...

REM 构建程序
go build -o bin\billing-consumer.exe cmd\billing-consumer\main.go
if !ERRORLEVEL! neq 0 (
    echo 构建失败！
    goto end
)

REM 启动程序
start /B bin\billing-consumer.exe > "%LOG_FILE%" 2>&1

REM 获取进程ID (简化版本，在Windows中获取确切PID比较复杂)
timeout /t 2 /nobreak >NUL

REM 查找新启动的进程PID
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq billing-consumer.exe" /FO CSV ^| find /V "PID"') do (
    set "NEW_PID=%%i"
    set "NEW_PID=!NEW_PID:"=!"
)

if defined NEW_PID (
    echo !NEW_PID! > "%PID_FILE%"
    echo 账单生成消费者启动成功 ^(PID: !NEW_PID!^)
    echo 日志文件: %LOG_FILE%
) else (
    echo 账单生成消费者启动失败！
)
goto end

:stop
call :is_running
if !ERRORLEVEL! neq 0 (
    echo 账单生成消费者未运行
    goto end
)

set /p PID=<"%PID_FILE%"
echo 正在停止账单生成消费者 ^(PID: !PID!^)...

REM 杀死进程
taskkill /PID !PID! /F >NUL 2>&1
if !ERRORLEVEL! equ 0 (
    del "%PID_FILE%" 2>NUL
    echo 账单生成消费者已停止
) else (
    echo 停止账单生成消费者失败
)
goto end

:restart
call :stop
timeout /t 2 /nobreak >NUL
call :start
goto end

:status
call :is_running
if !ERRORLEVEL! equ 0 (
    set /p PID=<"%PID_FILE%"
    echo 账单生成消费者正在运行 ^(PID: !PID!^)
    
    REM 显示进程信息
    tasklist /FI "PID eq !PID!" /FO TABLE
    
    REM 显示最近的日志
    echo.
    echo 最近的日志 ^(最后10行^):
    echo ========================
    if exist "%LOG_FILE%" (
        powershell "Get-Content '%LOG_FILE%' -Tail 10"
    ) else (
        echo 无法读取日志文件
    )
) else (
    echo 账单生成消费者未运行
)
goto end

:logs
if exist "%LOG_FILE%" (
    if "%2"=="-f" (
        echo 实时查看日志 ^(Ctrl+C 退出^):
        echo ===========================
        powershell "Get-Content '%LOG_FILE%' -Wait"
    ) else (
        echo 显示日志文件内容:
        echo ==================
        type "%LOG_FILE%"
    )
) else (
    echo 日志文件不存在: %LOG_FILE%
)
goto end

:usage
echo 用法: %0 {start^|stop^|restart^|status^|logs [-f]}
echo.
echo 命令说明:
echo   start   - 启动账单生成消费者
echo   stop    - 停止账单生成消费者
echo   restart - 重启账单生成消费者
echo   status  - 查看运行状态
echo   logs    - 查看日志
echo   logs -f - 实时查看日志

:end
pause 