package service

import (
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"

	"github.com/gin-gonic/gin"
)

// ShipmentTypeService 货物类型服务接口
type ShipmentTypeService interface {
	// GetAllShipmentTypes 获取所有货物类型
	GetAllShipmentTypes(ginCtx *gin.Context) (*GetAllShipmentTypesResponse, error)
	
	// GetActiveShipmentTypes 获取所有启用的货物类型
	GetActiveShipmentTypes(ginCtx *gin.Context) (*GetActiveShipmentTypesResponse, error)
}

// ShipmentTypeDTO 货物类型DTO
type ShipmentTypeDTO struct {
	ID       int64  `json:"id"`       // 主键ID
	Code     string `json:"code"`     // 类型代码
	Name     string `json:"name"`     // 类型名称
	IsActive bool   `json:"isActive"` // 是否启用
}

// GetAllShipmentTypesResponse 获取所有货物类型响应
type GetAllShipmentTypesResponse struct {
	List []*ShipmentTypeDTO `json:"list"` // 货物类型列表
}

// GetActiveShipmentTypesResponse 获取所有启用货物类型响应
type GetActiveShipmentTypesResponse struct {
	List []*ShipmentTypeDTO `json:"list"` // 启用的货物类型列表
}

// ShipmentTypeServiceImpl 货物类型服务实现
type ShipmentTypeServiceImpl struct {
	shipmentTypeRepo repository.ShipmentTypeRepository
}

// NewShipmentTypeService 创建货物类型服务实例
func NewShipmentTypeService(shipmentTypeRepo repository.ShipmentTypeRepository) ShipmentTypeService {
	return &ShipmentTypeServiceImpl{
		shipmentTypeRepo: shipmentTypeRepo,
	}
}

// GetAllShipmentTypes 获取所有货物类型
func (s *ShipmentTypeServiceImpl) GetAllShipmentTypes(ginCtx *gin.Context) (*GetAllShipmentTypesResponse, error) {
	ctx := ginCtx.Request.Context()
	
	shipmentTypes, err := s.shipmentTypeRepo.GetAllShipmentTypes(ctx)
	if err != nil {
		return nil, err
	}
	
	dtos := make([]*ShipmentTypeDTO, len(shipmentTypes))
	for i, st := range shipmentTypes {
		dtos[i] = s.convertToDTO(st)
	}
	
	return &GetAllShipmentTypesResponse{
		List: dtos,
	}, nil
}

// GetActiveShipmentTypes 获取所有启用的货物类型
func (s *ShipmentTypeServiceImpl) GetActiveShipmentTypes(ginCtx *gin.Context) (*GetActiveShipmentTypesResponse, error) {
	ctx := ginCtx.Request.Context()
	
	shipmentTypes, err := s.shipmentTypeRepo.GetActiveShipmentTypes(ctx)
	if err != nil {
		return nil, err
	}
	
	dtos := make([]*ShipmentTypeDTO, len(shipmentTypes))
	for i, st := range shipmentTypes {
		dtos[i] = s.convertToDTO(st)
	}
	
	return &GetActiveShipmentTypesResponse{
		List: dtos,
	}, nil
}

// convertToDTO 将实体转换为DTO
func (s *ShipmentTypeServiceImpl) convertToDTO(shipmentType *entity.ShipmentType) *ShipmentTypeDTO {
	return &ShipmentTypeDTO{
		ID:       shipmentType.ID,
		Code:     shipmentType.Code,
		Name:     shipmentType.Name,
		IsActive: shipmentType.IsActive,
	}
} 