package persistence

import (
	"context"
	"fmt"
	"time"

	"zebra-hub-system/internal/adapter/persistence/model"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
	"zebra-hub-system/internal/domain/valueobject"

	"gorm.io/gorm"
)

// ManifestRepositoryImpl 运单仓储实现
type ManifestRepositoryImpl struct {
	db *gorm.DB
}

// NewManifestRepository 创建运单仓储
func NewManifestRepository(db *gorm.DB) repository.ManifestRepository {
	return &ManifestRepositoryImpl{db: db}
}

// FindPendingReview 分页查询待审核运单 (status=0)
func (r *ManifestRepositoryImpl) FindPendingReview(ctx context.Context, page, pageSize int) ([]*entity.Manifest, int64, error) {
	var manifestPOs []*model.ManifestPO
	var total int64

	offset := (page - 1) * pageSize

	// 查询总数
	if err := r.db.Model(&model.ManifestPO{}).Where("status = ? AND is_delete = ?", 0, 0).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询数据
	if err := r.db.Where("status = ? AND is_delete = ?", 0, 0).Order("create_time DESC").Offset(offset).Limit(pageSize).Find(&manifestPOs).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	manifests := make([]*entity.Manifest, 0, len(manifestPOs))
	for _, po := range manifestPOs {
		manifests = append(manifests, po.ToEntity())
	}

	// 如果有查询到运单，批量获取物品信息
	if len(manifests) > 0 {
		// 收集所有运单ID
		manifestIDs := make([]int64, 0, len(manifests))
		for _, m := range manifests {
			manifestIDs = append(manifestIDs, m.ID)
		}

		// 批量查询运单物品
		items, err := r.GetManifestItemsByManifestIDs(ctx, manifestIDs)
		if err != nil {
			return nil, 0, fmt.Errorf("批量获取运单物品失败: %w", err)
		}

		// 将物品与运单关联
		itemMap := make(map[int64][]*entity.ManifestItem)
		for _, item := range items {
			itemMap[item.ManifestID] = append(itemMap[item.ManifestID], item)
		}

		// 为每个运单设置物品列表
		for i, manifest := range manifests {
			if itemsPtr, exists := itemMap[manifest.ID]; exists {
				manifests[i].Items = make([]entity.ManifestItem, len(itemsPtr))
				for j, item := range itemsPtr {
					manifests[i].Items[j] = *item
				}
			} else {
				// 如果没有物品，设置为空数组
				manifests[i].Items = []entity.ManifestItem{}
			}
		}
	}

	return manifests, total, nil
}

// CountPendingReview 统计待审核运单数量
func (r *ManifestRepositoryImpl) CountPendingReview(ctx context.Context) (int64, error) {
	var count int64

	// 构建查询条件，优先使用 is_delete 以匹配索引
	err := r.db.Model(&model.ManifestPO{}).
		Where("is_delete = ?", 0).
		Where("status = ?", 0).
		Count(&count).Error

	return count, err
}

// GetManifestItemsByManifestID 根据运单ID获取物品列表
func (r *ManifestRepositoryImpl) GetManifestItemsByManifestID(ctx context.Context, manifestID int64) ([]*entity.ManifestItem, error) {
	var itemPOs []*model.ManifestItemPO
	if err := r.db.Where("manifest_id = ?", manifestID).Find(&itemPOs).Error; err != nil {
		return nil, err
	}

	var items []*entity.ManifestItem
	for _, po := range itemPOs {
		items = append(items, po.ToEntity())
	}
	return items, nil
}

// GetManifestItemsByManifestIDs 根据多个运单ID批量获取物品列表
func (r *ManifestRepositoryImpl) GetManifestItemsByManifestIDs(ctx context.Context, manifestIDs []int64) ([]*entity.ManifestItem, error) {
	if len(manifestIDs) == 0 {
		return []*entity.ManifestItem{}, nil
	}

	var itemPOs []*model.ManifestItemPO
	if err := r.db.Where("manifest_id IN ?", manifestIDs).Find(&itemPOs).Error; err != nil {
		return nil, err
	}

	items := make([]*entity.ManifestItem, 0, len(itemPOs))
	for _, po := range itemPOs {
		items = append(items, po.ToEntity())
	}
	return items, nil
}

// GetManifestByID 根据ID获取运单
func (r *ManifestRepositoryImpl) GetManifestByID(ctx context.Context, id int64) (*entity.Manifest, error) {
	var manifestPO model.ManifestPO
	if err := r.db.Where("id = ?", id).Where("is_delete = ?", 0).First(&manifestPO).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	manifest := manifestPO.ToEntity()

	// 查询运单物品列表
	items, err := r.GetManifestItemsByManifestID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 设置物品列表
	manifest.Items = make([]entity.ManifestItem, len(items))
	for i, item := range items {
		manifest.Items[i] = *item
	}

	return manifest, nil
}

// GetManifestByOrderNumber 根据商家订单号获取运单
func (r *ManifestRepositoryImpl) GetManifestByOrderNumber(ctx context.Context, orderNumber string) (*entity.Manifest, error) {
	var manifestPO model.ManifestPO
	if err := r.db.Where("order_number = ?", orderNumber).Where("is_delete = ?", 0).First(&manifestPO).Error; err != nil {
		return nil, err
	}

	// 转换为实体
	manifest := manifestPO.ToEntity()

	return manifest, nil
}

// GetManifestsByIDs 根据多个ID批量获取运单（不包含物品列表）
// 用于提高查询性能，避免N+1查询问题
func (r *ManifestRepositoryImpl) GetManifestsByIDs(ctx context.Context, ids []int64) ([]*entity.Manifest, error) {
	if len(ids) == 0 {
		return []*entity.Manifest{}, nil
	}

	var manifestPOs []*model.ManifestPO
	if err := r.db.Where("id IN ? AND is_delete = ?", ids, 0).Find(&manifestPOs).Error; err != nil {
		return nil, err
	}

	// 转换为领域实体（不包含物品列表）
	manifests := make([]*entity.Manifest, 0, len(manifestPOs))
	for _, po := range manifestPOs {
		manifest := po.ToEntity()
		// 不查询物品列表，保持Items为空数组
		manifest.Items = []entity.ManifestItem{}
		manifests = append(manifests, manifest)
	}

	return manifests, nil
}

// UpdateManifest 更新运单基本信息
func (r *ManifestRepositoryImpl) UpdateManifest(ctx context.Context, manifest *entity.Manifest) error {
	// 只更新允许的字段
	updateFields := map[string]interface{}{
		"receiver_name":     manifest.ReceiverName,
		"receiver_phone":    manifest.ReceiverPhone,
		"receiver_zip_code": manifest.ReceiverZipCode,
		"receiver_address":  manifest.ReceiverAddress,
		"update_time":       gorm.Expr("NOW()"),
	}

	return r.db.Model(&model.ManifestPO{}).Where("id = ?", manifest.ID).Updates(updateFields).Error
}

// UpdateManifestItems 更新运单物品信息（先删除，再插入）
func (r *ManifestRepositoryImpl) UpdateManifestItems(ctx context.Context, manifestID int64, items []*entity.ManifestItem) error {
	// 开启事务
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 删除原有物品
		if err := tx.Where("manifest_id = ?", manifestID).Delete(&model.ManifestItemPO{}).Error; err != nil {
			return err
		}

		// 如果没有新的物品需要添加，则直接返回
		if len(items) == 0 {
			return nil
		}

		// 2. 插入新物品
		itemPOs := make([]*model.ManifestItemPO, 0, len(items))
		for _, item := range items {
			itemPO := &model.ManifestItemPO{
				ManifestID: manifestID,
				Name:       item.Name,
				NameEn:     item.NameEn,
				Weight:     item.Weight,
				Quantity:   item.Quantity,
				Price:      item.Price,
				Value:      item.Value,
				// CreateTime和UpdateTime会由数据库自动设置
			}
			itemPOs = append(itemPOs, itemPO)
		}

		return tx.Create(itemPOs).Error
	})
}

// UpdateManifestWithItems 在事务中同时更新运单信息和物品信息
func (r *ManifestRepositoryImpl) UpdateManifestWithItems(ctx context.Context, manifest *entity.Manifest, items []*entity.ManifestItem) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// 1. 更新运单基本信息，包括校验状态和错误信息

		// 使用Select确保所有字段都被更新，即使是零值
		if err := tx.Model(&model.ManifestPO{}).
			Select("receiver_name", "receiver_phone", "receiver_zip_code", "receiver_address",
				"validation_status", "validation_error", "update_time").
			Where("id = ?", manifest.ID).
			Updates(map[string]interface{}{
				"receiver_name":     manifest.ReceiverName,
				"receiver_phone":    manifest.ReceiverPhone,
				"receiver_zip_code": manifest.ReceiverZipCode,
				"receiver_address":  manifest.ReceiverAddress,
				"validation_status": manifest.ValidationStatus,
				"validation_error":  manifest.ValidationError,
				"update_time":       gorm.Expr("NOW()"),
			}).Error; err != nil {
			return err
		}

		// 2. 删除原有物品
		if err := tx.Where("manifest_id = ?", manifest.ID).Delete(&model.ManifestItemPO{}).Error; err != nil {
			return err
		}

		// 如果没有新的物品需要添加，则直接返回
		if len(items) == 0 {
			return nil
		}

		// 3. 插入新物品
		itemPOs := make([]*model.ManifestItemPO, 0, len(items))
		for _, item := range items {
			itemPO := &model.ManifestItemPO{
				ManifestID: manifest.ID,
				Name:       item.Name,
				NameEn:     item.NameEn,
				Weight:     item.Weight,
				Quantity:   item.Quantity,
				Price:      item.Price,
				Value:      item.Value,
				// CreateTime和UpdateTime会由数据库自动设置
			}
			itemPOs = append(itemPOs, itemPO)
		}

		return tx.Create(itemPOs).Error
	})
}

// UpdateManifestStatus 更新运单状态
func (r *ManifestRepositoryImpl) UpdateManifestStatus(ctx context.Context, manifestID int64, status int) error {
	// 使用Select确保即使是零值也会更新
	return r.db.Model(&model.ManifestPO{}).
		Where("id = ?", manifestID).
		Select("status", "update_time").
		Updates(map[string]interface{}{
			"status":      status,
			"update_time": gorm.Expr("NOW()"),
		}).Error
}

// CountProblemManifests 统计问题运单数量
func (r *ManifestRepositoryImpl) CountProblemManifests(ctx context.Context) (int64, error) {
	var count int64
	// 问题运单定义为：validation_status = 0 (校验失败) 且 is_delete = 0 (未删除)
	if err := r.db.WithContext(ctx).Model(&model.ManifestPO{}).Where("validation_status = ? AND is_delete = ?", 0, 0).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CountPendingProblemTickets 统计待处理的问题工单数量
// 假设 ProblemTicketPO 的 Status 字段中 0 代表待处理
func (r *ManifestRepositoryImpl) CountPendingProblemTickets(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&model.ProblemTicketPO{}).Where("status = ?", 0).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// FindUsersWithProblemManifests 查询拥有问题运单的用户列表
func (r *ManifestRepositoryImpl) FindUsersWithProblemManifests(ctx context.Context, page, pageSize int) ([]*entity.UserWithProblemCount, int64, error) {
	offset := (page - 1) * pageSize

	// 使用原生SQL查询拥有待处理问题工单的用户及其问题工单数量
	// 先构建一个子查询，获取每个用户的待处理问题工单数量
	subQuery := r.db.Table("problem_manifest_tickets").
		Select("customer_account_id, COUNT(*) as pending_count").
		Where("status = ?", valueobject.ProblemTicketStatusPending).
		Group("customer_account_id").
		Having("COUNT(*) > 0")

	// 查询总数
	var total int64
	countQuery := r.db.Table("(?) as subq", subQuery).Count(&total)
	if countQuery.Error != nil {
		return nil, 0, countQuery.Error
	}

	// 分页查询用户详情
	rows, err := r.db.Table("(?) as ticket_counts", subQuery).
		Joins("JOIN tb_user ON tb_user.id = ticket_counts.customer_account_id").
		Select("tb_user.id, tb_user.username, tb_user.nickname, ticket_counts.pending_count").
		Order("ticket_counts.pending_count DESC").
		Offset(offset).
		Limit(pageSize).
		Rows()

	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	// 解析查询结果
	var result []*entity.UserWithProblemCount
	for rows.Next() {
		user := &entity.UserWithProblemCount{}
		if err := r.db.ScanRows(rows, user); err != nil {
			return nil, 0, err
		}
		result = append(result, user)
	}

	return result, total, nil
}

// GetUserManifestsByTimeRange 按发货时间获取用户运单
func (r *ManifestRepositoryImpl) GetUserManifestsByTimeRange(ctx context.Context, userID int64, startTime, endTime time.Time) ([]entity.Manifest, error) {
	var manifests []entity.Manifest

	query := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Where("shipment_time BETWEEN ? AND ?", startTime, endTime).
		Where("status > ?", 2).   // 只查询已发货的运单
		Where("is_delete = ?", 0) // 只查询未删除的运单

	err := query.Find(&manifests).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户时间范围内的运单失败: %w", err)
	}

	// 查询运单物品信息
	if len(manifests) > 0 {
		manifestIDs := make([]int64, 0, len(manifests))
		for _, manifest := range manifests {
			manifestIDs = append(manifestIDs, manifest.ID)
		}

		// 批量查询运单物品
		items, err := r.GetManifestItemsByManifestIDs(ctx, manifestIDs)
		if err != nil {
			return nil, fmt.Errorf("批量获取运单物品失败: %w", err)
		}

		// 将物品与运单关联
		itemMap := make(map[int64][]*entity.ManifestItem)
		for _, item := range items {
			itemMap[item.ManifestID] = append(itemMap[item.ManifestID], item)
		}

		// 为每个运单设置物品列表
		for i := range manifests {
			if itemsPtr, exists := itemMap[manifests[i].ID]; exists {
				// 将[]*entity.ManifestItem转换为[]entity.ManifestItem
				items := make([]entity.ManifestItem, len(itemsPtr))
				for j, item := range itemsPtr {
					items[j] = *item
				}
				manifests[i].Items = items
			}
		}
	}

	return manifests, nil
}

// GetUserManifestsByCreateTimeRange 获取用户在指定预报时间范围内的运单
func (r *ManifestRepositoryImpl) GetUserManifestsByCreateTimeRange(ctx context.Context, userID int64, startTime, endTime time.Time) ([]entity.Manifest, error) {
	// 实现与上面类似，只是使用create_time字段过滤
	var manifestPOs []*model.ManifestPO

	query := r.db.WithContext(ctx).
		Model(&model.ManifestPO{}).
		Where("user_id = ?", userID).
		Where("is_delete = ?", 0)

	// 添加时间范围条件
	if !startTime.IsZero() {
		query = query.Where("create_time >= ?", startTime)
	}
	if !endTime.IsZero() {
		query = query.Where("create_time <= ?", endTime)
	}

	// 执行查询
	if err := query.Find(&manifestPOs).Error; err != nil {
		return nil, err
	}

	// 转换为领域实体
	manifests := make([]entity.Manifest, 0, len(manifestPOs))
	for _, po := range manifestPOs {
		manifests = append(manifests, *po.ToEntity())
	}

	return manifests, nil
}

// SearchManifests 多条件查询运单
func (r *ManifestRepositoryImpl) SearchManifests(ctx context.Context, query, createTimeStart, createTimeEnd, pickUpTimeStart, pickUpTimeEnd, shipmentTimeStart, shipmentTimeEnd, deliveredTimeStart, deliveredTimeEnd string, status *int, userID *int64, masterBillID *int64, page, pageSize int) ([]*entity.Manifest, int64, error) {
	var manifestPOs []*model.ManifestPO
	var total int64

	offset := (page - 1) * pageSize

	// 构建基础查询
	dbQuery := r.db.WithContext(ctx).Model(&model.ManifestPO{}).Where("is_delete = ?", 0)

	// 添加查询条件

	// 单号查询条件（模糊匹配多个字段）
	if query != "" {
		dbQuery = dbQuery.Where(
			r.db.Where("express_number LIKE ?", "%"+query+"%").
				Or("transferred_tracking_number LIKE ?", "%"+query+"%").
				Or("order_no LIKE ?", "%"+query+"%").
				Or("order_number LIKE ?", "%"+query+"%"),
		)
	}

	// 创建时间范围
	if createTimeStart != "" {
		dbQuery = dbQuery.Where("create_time >= ?", createTimeStart)
	}
	if createTimeEnd != "" {
		dbQuery = dbQuery.Where("create_time <= ?", createTimeEnd+" 23:59:59")
	}

	// 揽件时间范围
	if pickUpTimeStart != "" {
		dbQuery = dbQuery.Where("pick_up_time >= ?", pickUpTimeStart)
	}
	if pickUpTimeEnd != "" {
		dbQuery = dbQuery.Where("pick_up_time <= ?", pickUpTimeEnd+" 23:59:59")
	}

	// 发货时间范围
	if shipmentTimeStart != "" {
		dbQuery = dbQuery.Where("shipment_time >= ?", shipmentTimeStart)
	}
	if shipmentTimeEnd != "" {
		dbQuery = dbQuery.Where("shipment_time <= ?", shipmentTimeEnd+" 23:59:59")
	}

	// 签收时间范围
	if deliveredTimeStart != "" {
		dbQuery = dbQuery.Where("delivered_time >= ?", deliveredTimeStart)
	}
	if deliveredTimeEnd != "" {
		dbQuery = dbQuery.Where("delivered_time <= ?", deliveredTimeEnd+" 23:59:59")
	}

	// 运单状态
	if status != nil {
		dbQuery = dbQuery.Where("status = ?", *status)
	}

	// 用户ID
	if userID != nil {
		dbQuery = dbQuery.Where("user_id = ?", *userID)
	}

	// 提单ID
	if masterBillID != nil {
		dbQuery = dbQuery.Where("master_bill_id = ?", *masterBillID)
	}

	// 查询总数
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询数据
	if err := dbQuery.Order("create_time DESC").Offset(offset).Limit(pageSize).Find(&manifestPOs).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	manifests := make([]*entity.Manifest, 0, len(manifestPOs))
	for _, po := range manifestPOs {
		manifest := po.ToEntity()
		manifests = append(manifests, manifest)
	}

	// 如果有查询到运单，批量获取物品信息
	if len(manifests) > 0 {
		// 收集所有运单ID
		manifestIDs := make([]int64, 0, len(manifests))
		for _, m := range manifests {
			manifestIDs = append(manifestIDs, m.ID)
		}

		// 批量查询运单物品
		items, err := r.GetManifestItemsByManifestIDs(ctx, manifestIDs)
		if err != nil {
			return nil, 0, fmt.Errorf("批量获取运单物品失败: %w", err)
		}

		// 将物品与运单关联
		itemMap := make(map[int64][]*entity.ManifestItem)
		for _, item := range items {
			itemMap[item.ManifestID] = append(itemMap[item.ManifestID], item)
		}

		// 为每个运单设置物品列表
		for i, manifest := range manifests {
			if itemsPtr, exists := itemMap[manifest.ID]; exists {
				manifests[i].Items = make([]entity.ManifestItem, len(itemsPtr))
				for j, item := range itemsPtr {
					manifests[i].Items[j] = *item
				}
			} else {
				// 如果没有物品，设置为空数组
				manifests[i].Items = []entity.ManifestItem{}
			}
		}
	}

	return manifests, total, nil
}

// FindManifestsByExpressNumber 根据快递单号模糊查询运单
func (r *ManifestRepositoryImpl) FindManifestsByExpressNumber(ctx context.Context, expressNumber string, page, pageSize int) ([]*entity.Manifest, int64, error) {
	var manifestPOs []*model.ManifestPO
	var total int64

	offset := (page - 1) * pageSize

	// 构建基础查询
	dbQuery := r.db.WithContext(ctx).Model(&model.ManifestPO{}).Where("is_delete = ?", 0)

	// 添加快递单号模糊查询条件
	if expressNumber != "" {
		dbQuery = dbQuery.Where("express_number LIKE ?", expressNumber+"%")
	}

	// 查询总数
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询数据
	if err := dbQuery.Order("create_time DESC").Offset(offset).Limit(pageSize).Find(&manifestPOs).Error; err != nil {
		return nil, 0, err
	}

	// 转换为领域实体
	manifests := make([]*entity.Manifest, 0, len(manifestPOs))
	for _, po := range manifestPOs {
		manifest := po.ToEntity()
		manifests = append(manifests, manifest)
	}

	// 如果有查询到运单，批量获取物品信息
	if len(manifests) > 0 {
		// 收集所有运单ID
		manifestIDs := make([]int64, 0, len(manifests))
		for _, m := range manifests {
			manifestIDs = append(manifestIDs, m.ID)
		}

		// 批量查询运单物品
		items, err := r.GetManifestItemsByManifestIDs(ctx, manifestIDs)
		if err != nil {
			return nil, 0, fmt.Errorf("批量获取运单物品失败: %w", err)
		}

		// 将物品与运单关联
		itemMap := make(map[int64][]*entity.ManifestItem)
		for _, item := range items {
			itemMap[item.ManifestID] = append(itemMap[item.ManifestID], item)
		}

		// 为每个运单设置物品列表
		for i, manifest := range manifests {
			if itemsPtr, exists := itemMap[manifest.ID]; exists {
				manifests[i].Items = make([]entity.ManifestItem, len(itemsPtr))
				for j, item := range itemsPtr {
					manifests[i].Items[j] = *item
				}
			} else {
				// 如果没有物品，设置为空数组
				manifests[i].Items = []entity.ManifestItem{}
			}
		}
	}

	return manifests, total, nil
}

// CreateManifest 创建运单
func (r *ManifestRepositoryImpl) CreateManifest(ctx context.Context, manifest *entity.Manifest) (*entity.Manifest, error) {
	manifestPO := model.ManifestPO{
		ExpressNumber:              manifest.ExpressNumber,
		TransferredTrackingNumber:  manifest.TransferredTrackingNumber,
		OrderNo:                    manifest.OrderNo,
		OrderNumber:                manifest.OrderNumber,
		ReceiverName:               manifest.ReceiverName,
		ReceiverPhone:              manifest.ReceiverPhone,
		ReceiverZipCode:            manifest.ReceiverZipCode,
		ReceiverAddress:            manifest.ReceiverAddress,
		Status:                     manifest.Status,
		RemoteAreaSurcharge:        manifest.RemoteAreaSurcharge,
		UserID:                     manifest.UserID,
		MasterBillID:               manifest.MasterBillID,
		CreateTime:                 &manifest.CreateTime,
		UpdateTime:                 &manifest.UpdateTime,
		PickUpTime:                 manifest.PickUpTime.Time,
		ShipmentTime:               manifest.ShipmentTime.Time,
		DeliveredTime:              manifest.DeliveredTime.Time,
		IsDelete:                   false, // 新创建的记录不删除
	}

	if err := r.db.WithContext(ctx).Create(&manifestPO).Error; err != nil {
		return nil, err
	}

	// 设置生成的ID
	manifest.ID = manifestPO.ID

	return manifest, nil
}

// CreateManifestItem 创建运单物品
func (r *ManifestRepositoryImpl) CreateManifestItem(ctx context.Context, item *entity.ManifestItem) error {
	itemPO := model.ManifestItemPO{
		ManifestID: item.ManifestID,
		Name:       item.Name,
		Quantity:   item.Quantity,
		Price:      item.Price,
		Value:      item.Value,
		CreateTime: item.CreateTime.Time,
		UpdateTime: item.UpdateTime.Time,
	}

	return r.db.WithContext(ctx).Create(&itemPO).Error
}

// GetManifestsByTrackingNumbers 根据一批运单号获取运单列表
func (r *ManifestRepositoryImpl) GetManifestsByTrackingNumbers(ctx context.Context, trackingNumbers []string) ([]*entity.Manifest, error) {
	var manifestPOs []*model.ManifestPO
	if err := r.db.WithContext(ctx).Preload("Items").Where("express_number IN ?", trackingNumbers).Find(&manifestPOs).Error; err != nil {
		return nil, err
	}

	manifests := make([]*entity.Manifest, 0, len(manifestPOs))
	for _, po := range manifestPOs {
		manifests = append(manifests, po.ToEntity())
	}

	return manifests, nil
}

// WithTransaction 在事务中执行操作
func (r *ManifestRepositoryImpl) WithTransaction(ctx context.Context, fn func(ctx context.Context) error) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		ctx = context.WithValue(ctx, "DB", tx)
		return fn(ctx)
	})
}
