package examples

import (
	"context"
	"fmt"
	"log"
	"time"

	"go.uber.org/zap"

	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/entity"
	"zebra-hub-system/internal/domain/repository"
)

// BillingProcessorUsageExample 多线程账单处理器使用示例
type BillingProcessorUsageExample struct {
	processor *service.BillingTaskProcessor
	logger    *zap.Logger
}

// NewBillingProcessorUsageExample 创建使用示例
func NewBillingProcessorUsageExample() *BillingProcessorUsageExample {
	// 初始化日志
	logger, _ := zap.NewProduction()
	
	// 这里应该注入实际的repository实现
	// 此处使用nil仅作为示例，实际使用时需要提供真实的实现
	var (
		billingRepo                     repository.BillingRepository                     = nil
		manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository = nil
		userRepo                        repository.UserRepository                        = nil
		shippingFeeTemplateRepo         repository.ShippingFeeTemplateRepository         = nil
		shipmentTypeRepo                repository.ShipmentTypeRepository                = nil
		billingGenerationTaskRepo       repository.BillingGenerationTaskRepository       = nil
		billingCycleRepo                repository.BillingCycleRepository                = nil
	)

	// 创建处理器（使用默认配置）
	processor := service.NewBillingTaskProcessor(
		billingRepo,
		manifestFinancialAdjustmentRepo,
		userRepo,
		shippingFeeTemplateRepo,
		shipmentTypeRepo,
		billingGenerationTaskRepo,
		billingCycleRepo,
		logger,
	)
	
	return &BillingProcessorUsageExample{
		processor: processor,
		logger:    logger,
	}
}

// Example1_DefaultConfiguration 示例1：使用默认配置
func (e *BillingProcessorUsageExample) Example1_DefaultConfiguration() {
	fmt.Println("=== 示例1：使用默认配置的多线程账单处理 ===")
	
	// 创建测试消息
	message := &entity.BillingGenerationMessage{
		TaskID:         "task-001",
		BillingCycleID: 123,
		CustomerIDs:    []int64{1001, 1002, 1003, 1004, 1005},
		StartTime:      time.Now().AddDate(0, 0, -30), // 30天前
		EndTime:        time.Now(),
		Currency:       "CNY",
	}
	
	ctx := context.Background()
	
	// 处理任务
	fmt.Printf("开始处理 %d 个客户的账单生成任务...\n", len(message.CustomerIDs))
	
	err := e.processor.ProcessBillingTask(ctx, message)
	if err != nil {
		log.Printf("账单处理失败: %v", err)
		return
	}
	
	fmt.Println("账单处理完成！")
	fmt.Println("默认配置：5个并发Worker，1秒进度更新间隔")
}

// Example2_CustomConfiguration 示例2：使用自定义配置
func (e *BillingProcessorUsageExample) Example2_CustomConfiguration() {
	fmt.Println("\n=== 示例2：使用自定义配置的多线程账单处理 ===")
	
	// 自定义配置
	config := &service.BillingTaskProcessorConfig{
		MaxConcurrency:         8,                         // 8个并发Worker
		ProgressUpdateInterval: 500 * time.Millisecond,   // 500ms更新间隔
	}
	
	// 这里应该注入实际的repository实现
	var (
		billingRepo                     repository.BillingRepository                     = nil
		manifestFinancialAdjustmentRepo repository.ManifestFinancialAdjustmentRepository = nil
		userRepo                        repository.UserRepository                        = nil
		shippingFeeTemplateRepo         repository.ShippingFeeTemplateRepository         = nil
		shipmentTypeRepo                repository.ShipmentTypeRepository                = nil
		billingGenerationTaskRepo       repository.BillingGenerationTaskRepository       = nil
		billingCycleRepo                repository.BillingCycleRepository                = nil
	)

	// 使用自定义配置创建处理器
	processor := service.NewBillingTaskProcessorWithConfig(
		billingRepo,
		manifestFinancialAdjustmentRepo,
		userRepo,
		shippingFeeTemplateRepo,
		shipmentTypeRepo,
		billingGenerationTaskRepo,
		billingCycleRepo,
		e.logger,
		config,
	)
	
	// 创建大批量测试消息
	customerIDs := make([]int64, 50) // 50个客户
	for i := 0; i < 50; i++ {
		customerIDs[i] = int64(2000 + i)
	}
	
	message := &entity.BillingGenerationMessage{
		TaskID:         "task-002-batch",
		BillingCycleID: 124,
		CustomerIDs:    customerIDs,
		StartTime:      time.Now().AddDate(0, 0, -30),
		EndTime:        time.Now(),
		Currency:       "CNY",
	}
	
	ctx := context.Background()
	
	fmt.Printf("开始处理 %d 个客户的大批量账单生成任务...\n", len(message.CustomerIDs))
	fmt.Printf("自定义配置：%d个并发Worker，%v进度更新间隔\n", 
		config.MaxConcurrency, 
		config.ProgressUpdateInterval)
	
	startTime := time.Now()
	err := processor.ProcessBillingTask(ctx, message)
	duration := time.Since(startTime)
	
	if err != nil {
		log.Printf("批量账单处理失败: %v", err)
		return
	}
	
	fmt.Printf("批量账单处理完成！耗时: %v\n", duration)
}

// Example3_EnvironmentBasedConfiguration 示例3：基于环境的配置
func (e *BillingProcessorUsageExample) Example3_EnvironmentBasedConfiguration() {
	fmt.Println("\n=== 示例3：基于环境的配置 ===")
	
	// 模拟不同环境的配置
	environments := map[string]*service.BillingTaskProcessorConfig{
		"development": {
			MaxConcurrency:         3,
			ProgressUpdateInterval: 2 * time.Second,
		},
		"testing": {
			MaxConcurrency:         5,
			ProgressUpdateInterval: 1 * time.Second,
		},
		"production": {
			MaxConcurrency:         10,
			ProgressUpdateInterval: 500 * time.Millisecond,
		},
		"high_load": {
			MaxConcurrency:         15,
			ProgressUpdateInterval: 500 * time.Millisecond,
		},
	}
	
	// 模拟当前环境（可以通过环境变量获取）
	currentEnv := "production" // 可以从 os.Getenv("APP_ENV") 获取
	
	config := environments[currentEnv]
	if config == nil {
		config = service.DefaultBillingTaskProcessorConfig()
	}
	
	fmt.Printf("当前环境: %s\n", currentEnv)
	fmt.Printf("配置: %d个并发Worker，%v进度更新间隔\n", 
		config.MaxConcurrency, 
		config.ProgressUpdateInterval)
}

// Example4_PerformanceComparison 示例4：性能对比
func (e *BillingProcessorUsageExample) Example4_PerformanceComparison() {
	fmt.Println("\n=== 示例4：性能对比演示 ===")
	
	// 模拟不同并发配置的性能
	configs := []struct {
		name           string
		maxConcurrency int
		expectedTime   time.Duration
	}{
		{"单线程模拟", 1, 100 * time.Second},
		{"低并发", 3, 35 * time.Second},
		{"中并发", 5, 20 * time.Second},
		{"高并发", 10, 10 * time.Second},
		{"超高并发", 15, 7 * time.Second},
	}
	
	customerCount := 100
	avgProcessingTime := 1 * time.Second // 假设每个客户平均处理时间
	
	fmt.Printf("假设处理 %d 个客户，每个客户平均处理时间 %v：\n\n", customerCount, avgProcessingTime)
	
	for _, cfg := range configs {
		// 理论计算时间
		theoreticalTime := time.Duration(float64(avgProcessingTime) * float64(customerCount) / float64(cfg.maxConcurrency))
		
		fmt.Printf("配置: %-12s | 并发数: %2d | 理论时间: %8s | 实际可能时间: %8s\n", 
			cfg.name, 
			cfg.maxConcurrency,
			theoreticalTime.Round(time.Second),
			cfg.expectedTime)
	}
	
	fmt.Println("\n注意：实际性能还受到数据库连接池、网络IO、系统资源等因素影响")
}

// Example5_ErrorHandlingAndMonitoring 示例5：错误处理和监控
func (e *BillingProcessorUsageExample) Example5_ErrorHandlingAndMonitoring() {
	fmt.Println("\n=== 示例5：错误处理和监控 ===")
	
	// 监控配置建议
	fmt.Println("监控配置建议:")
	fmt.Println("1. 关键指标监控:")
	fmt.Println("   - 任务处理总时间")
	fmt.Println("   - 并发Worker利用率")
	fmt.Println("   - 客户处理成功率")
	fmt.Println("   - 数据库连接池使用率")
	fmt.Println("   - 内存使用量")
	fmt.Println("   - CPU使用率")
	
	fmt.Println("\n2. 告警配置:")
	fmt.Println("   - 处理时间超过阈值（如30秒）")
	fmt.Println("   - 错误率超过阈值（如5%）")
	fmt.Println("   - 数据库连接池耗尽")
	fmt.Println("   - 内存使用率过高（如>80%）")
	
	fmt.Println("\n3. 日志级别:")
	fmt.Println("   - Info: 任务开始/完成、总体统计")
	fmt.Println("   - Debug: Worker级别详细信息（仅开发/测试环境）")
	fmt.Println("   - Error: 错误详情和失败客户信息")
	
	fmt.Println("\n4. 容错机制:")
	fmt.Println("   - 单个客户处理失败不影响其他客户")
	fmt.Println("   - 支持部分成功的处理结果")
	fmt.Println("   - 详细的错误信息记录")
	fmt.Println("   - 支持Context取消机制")
}

// RunAllExamples 运行所有示例
func (e *BillingProcessorUsageExample) RunAllExamples() {
	fmt.Println("多线程账单处理器使用示例")
	fmt.Println("=====================================")
	
	// 运行各个示例
	e.Example1_DefaultConfiguration()
	e.Example2_CustomConfiguration()
	e.Example3_EnvironmentBasedConfiguration()
	e.Example4_PerformanceComparison()
	e.Example5_ErrorHandlingAndMonitoring()
	
	fmt.Println("\n=====================================")
	fmt.Println("示例演示完成")
}

// 配置加载器示例
type ConfigLoader struct{}

// LoadConfigFromYAML 从YAML文件加载配置的示例
func (cl *ConfigLoader) LoadConfigFromYAML(env string) *service.BillingTaskProcessorConfig {
	// 这是一个示例实现，实际项目中应该使用viper等配置库
	switch env {
	case "development":
		return &service.BillingTaskProcessorConfig{
			MaxConcurrency:         3,
			ProgressUpdateInterval: 1 * time.Second,
		}
	case "testing":
		return &service.BillingTaskProcessorConfig{
			MaxConcurrency:         5,
			ProgressUpdateInterval: 1 * time.Second,
		}
	case "production":
		return &service.BillingTaskProcessorConfig{
			MaxConcurrency:         10,
			ProgressUpdateInterval: 500 * time.Millisecond,
		}
	default:
		return service.DefaultBillingTaskProcessorConfig()
	}
}

// LoadConfigFromEnv 从环境变量加载配置的示例
func (cl *ConfigLoader) LoadConfigFromEnv() *service.BillingTaskProcessorConfig {
	config := service.DefaultBillingTaskProcessorConfig()
	
	// 实际实现中应该使用 os.Getenv() 和类型转换
	// 这里只是示例逻辑
	
	// if maxConcStr := os.Getenv("BILLING_PROCESSOR_MAX_CONCURRENCY"); maxConcStr != "" {
	//     if maxConc, err := strconv.Atoi(maxConcStr); err == nil {
	//         config.MaxConcurrency = maxConc
	//     }
	// }
	
	// if intervalStr := os.Getenv("BILLING_PROCESSOR_PROGRESS_INTERVAL_MS"); intervalStr != "" {
	//     if intervalMs, err := strconv.Atoi(intervalStr); err == nil {
	//         config.ProgressUpdateInterval = time.Duration(intervalMs) * time.Millisecond
	//     }
	// }
	
	return config
}

// 使用示例的main函数
func ExampleMain() {
	example := NewBillingProcessorUsageExample()
	example.RunAllExamples()
} 