# 斑马物巢系统

## 项目简介

斑马物巢系统是一套高性能、高可用的后端服务，专注于处理公司跨境物流业务。目标是提供稳定、高效、易于扩展的 API 接口，支持前端应用和可能的第三方系统集成。

## 技术栈

- 后端语言：Go (Golang)
- Web 框架：Gin
- 数据库交互：GORM 配合 MySQL
- 配置管理：Viper
- 日志库：Zap
- 参数校验：go-playground/validator
- 认证：JWT

## 项目结构

```
[project-root]/
├── cmd/                     # 主要应用程序的入口
│   └── api-server/          # API服务入口
│       └── main.go
├── internal/                # 内部代码，不希望其他项目导入
│   ├── adapter/             # 适配器层
│   │   └── persistence/     # 数据库仓储实现
│   ├── app/                 # 应用层
│   │   └── service/         # 应用服务
│   ├── domain/              # 领域层
│   │   ├── entity/          # 实体
│   │   ├── valueobject/     # 值对象
│   │   └── repository/      # 仓储接口定义
│   ├── config/              # 配置加载与管理
│   ├── handler/             # API处理器
│   ├── middleware/          # HTTP中间件
│   ├── router/              # API路由定义
│   └── util/                # 通用工具函数
├── configs/                 # 配置文件
└── README.md
```

## 接口说明

### 用户登录

- 接口路径：`/api/v1/login`
- 请求方式：POST
- 请求参数：
  ```json
  {
    "username": "用户名或手机号",
    "password": "密码"
  }
  ```
- 响应结果：
  ```json
  {
    "success": true,
    "errorCode": 100000,
    "errorMessage": "操作成功",
    "requestId": "uuid",
    "timestamp": "2024-05-08T10:00:00Z",
    "data": {
      "token": "JWT令牌",
      "user_info": {
        "id": 1,
        "username": "用户名",
        "nickname": "昵称",
        ...
      }
    }
  }
  ```

## 启动方式

1. 确保安装了 Go 环境
2. 配置数据库连接信息：`configs/config.yaml`
3. 运行服务：
   ```bash
   go run cmd/api-server/main.go
   ```

## 依赖管理

项目使用 Go Module 进行依赖管理：

```bash
go mod tidy
```
