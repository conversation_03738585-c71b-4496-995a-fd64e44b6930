package config

import (
	"fmt"

	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// InitRabbitMQ 初始化RabbitMQ连接
func InitRabbitMQ(config *RabbitMQConfig, logger *zap.Logger) (*amqp.Connection, error) {
	// 构建连接URL
	url := fmt.Sprintf("amqp://%s:%s@%s:%d%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.VirtualHost,
	)

	// 建立连接
	conn, err := amqp.Dial(url)
	if err != nil {
		if logger != nil {
			logger.Error("Failed to connect to RabbitMQ",
				zap.String("host", config.Host),
				zap.Int("port", config.Port),
				zap.String("virtualHost", config.VirtualHost),
				zap.String("username", config.Username),
				zap.Error(err))
		}
		return nil, fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	if logger != nil {
		logger.Info("Successfully connected to RabbitMQ",
			zap.String("host", config.Host),
			zap.Int("port", config.Port),
			zap.String("virtualHost", config.VirtualHost),
			zap.String("username", config.Username))
	}

	return conn, nil
}

// BillingQueueConfig 账单队列配置
const (
	BillingGenerationQueue    = "billing.generation"      // 账单生成队列
	BillingGenerationExchange = "billing.generation.exchange" // 账单生成交换机
	BillingGenerationRoutingKey = "billing.generation.task"   // 路由键
) 