package handler

import (
	"net/http"
	"strconv"
	"strings"
	"zebra-hub-system/internal/app/service"
	"zebra-hub-system/internal/domain/valueobject"
	"zebra-hub-system/internal/util"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ShippingFeeTemplateHandler 运费模板处理器
type ShippingFeeTemplateHandler struct {
	shippingFeeTemplateService service.ShippingFeeTemplateService
	logger                     *zap.Logger
}

// NewShippingFeeTemplateHandler 创建运费模板处理器实例
func NewShippingFeeTemplateHandler(shippingFeeTemplateService service.ShippingFeeTemplateService, logger *zap.Logger) *ShippingFeeTemplateHandler {
	return &ShippingFeeTemplateHandler{
		shippingFeeTemplateService: shippingFeeTemplateService,
		logger:                     logger,
	}
}

// GetTemplatesWithSearch 查询运费模板
// @Summary 查询运费模板
// @Description 查询运费模板，支持根据模板名称进行模糊搜索
// @Tags 运费模板管理
// @Accept json
// @Produce json
// @Param name query string false "模板名称（模糊搜索）"
// @Success 200 {object} util.Response{data=service.GetTemplatesWithSearchResponse} "成功"
// @Failure 400 {object} util.Response "参数错误"
// @Failure 500 {object} util.Response "服务器内部错误"
// @Router /api/v1/shipping-fee-templates [get]
func (h *ShippingFeeTemplateHandler) GetTemplatesWithSearch(c *gin.Context) {
	var req service.GetTemplatesWithSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("查询运费模板参数绑定失败", zap.Error(err))
		util.ResponseError(c, valueobject.ERROR_INVALID_PARAMETER, "参数格式错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	response, err := h.shippingFeeTemplateService.GetTemplatesWithSearch(c, &req)
	if err != nil {
		h.logger.Error("查询运费模板失败", zap.Error(err))
		util.ResponseError(c, valueobject.ERROR_UNKNOWN, "查询运费模板失败", http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, response)
}

// CreateTemplate 创建运费模板
// @Summary 创建运费模板
// @Description 创建新的运费模板
// @Tags 运费模板管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Token"
// @Param body body service.CreateShippingFeeTemplateRequest true "创建运费模板请求"
// @Success 200 {object} object{success=bool,errorCode=int,errorMessage=string,data=service.ShippingFeeTemplateDTO} "创建成功"
// @Failure 400 {object} object{success=bool,errorCode=int,errorMessage=string} "参数错误"
// @Failure 409 {object} object{success=bool,errorCode=int,errorMessage=string} "模板名称已存在"
// @Failure 500 {object} object{success=bool,errorCode=int,errorMessage=string} "内部服务器错误"
// @Router /api/v1/shipping-fee-templates [post]
func (h *ShippingFeeTemplateHandler) CreateTemplate(c *gin.Context) {
	var req service.CreateShippingFeeTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("创建运费模板参数绑定失败", zap.Error(err))
		util.ResponseError(c, 100002, "参数格式错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	template, err := h.shippingFeeTemplateService.CreateTemplate(c, &req)
	if err != nil {
		h.logger.Error("创建运费模板失败", zap.Error(err))
		if strings.Contains(err.Error(), "模板名称已存在") {
			util.ResponseError(c, 100007, err.Error(), http.StatusConflict)
		} else {
			util.ResponseError(c, 100001, "创建运费模板失败: "+err.Error(), http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, template)
}

// UpdateTemplate 更新运费模板
// @Summary 更新运费模板
// @Description 更新现有的运费模板
// @Tags 运费模板管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Token"
// @Param body body service.UpdateShippingFeeTemplateRequest true "更新运费模板请求"
// @Success 200 {object} object{success=bool,errorCode=int,errorMessage=string,data=service.ShippingFeeTemplateDTO} "更新成功"
// @Failure 400 {object} object{success=bool,errorCode=int,errorMessage=string} "参数错误"
// @Failure 404 {object} object{success=bool,errorCode=int,errorMessage=string} "模板不存在"
// @Failure 409 {object} object{success=bool,errorCode=int,errorMessage=string} "模板名称已被其他模板使用"
// @Failure 500 {object} object{success=bool,errorCode=int,errorMessage=string} "内部服务器错误"
// @Router /api/v1/shipping-fee-templates [put]
func (h *ShippingFeeTemplateHandler) UpdateTemplate(c *gin.Context) {
	var req service.UpdateShippingFeeTemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("更新运费模板参数绑定失败", zap.Error(err))
		util.ResponseError(c, 100002, "参数格式错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	template, err := h.shippingFeeTemplateService.UpdateTemplate(c, &req)
	if err != nil {
		h.logger.Error("更新运费模板失败", zap.Error(err))
		if strings.Contains(err.Error(), "不存在") {
			util.ResponseError(c, 100006, err.Error(), http.StatusNotFound)
		} else if strings.Contains(err.Error(), "模板名称已被其他模板使用") {
			util.ResponseError(c, 100007, err.Error(), http.StatusConflict)
		} else {
			util.ResponseError(c, 100001, "更新运费模板失败: "+err.Error(), http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, template)
}

// DeleteTemplate 删除运费模板
// @Summary 删除运费模板
// @Description 删除指定的运费模板
// @Tags 运费模板管理
// @Produce json
// @Param Authorization header string true "Bearer Token"
// @Param id path int true "模板ID"
// @Success 200 {object} object{success=bool,errorCode=int,errorMessage=string} "删除成功"
// @Failure 400 {object} object{success=bool,errorCode=int,errorMessage=string} "参数错误"
// @Failure 404 {object} object{success=bool,errorCode=int,errorMessage=string} "模板不存在"
// @Failure 500 {object} object{success=bool,errorCode=int,errorMessage=string} "内部服务器错误"
// @Router /api/v1/shipping-fee-templates/{id} [delete]
func (h *ShippingFeeTemplateHandler) DeleteTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.logger.Error("模板ID格式错误", zap.String("id", idStr), zap.Error(err))
		util.ResponseError(c, 100002, "模板ID格式错误", http.StatusBadRequest)
		return
	}

	err = h.shippingFeeTemplateService.DeleteTemplate(c, id)
	if err != nil {
		h.logger.Error("删除运费模板失败", zap.Int64("id", id), zap.Error(err))
		if strings.Contains(err.Error(), "不存在") {
			util.ResponseError(c, 100006, err.Error(), http.StatusNotFound)
		} else if strings.Contains(err.Error(), "正在被用户使用") {
			util.ResponseError(c, 100002, err.Error(), http.StatusBadRequest)
		} else {
			util.ResponseError(c, 100001, "删除运费模板失败: "+err.Error(), http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, nil)
}

// GetTemplateByID 根据ID获取运费模板
// @Summary 根据ID获取运费模板
// @Description 根据ID获取运费模板详情
// @Tags 运费模板管理
// @Produce json
// @Param Authorization header string true "Bearer Token"
// @Param id path int true "模板ID"
// @Success 200 {object} object{success=bool,errorCode=int,errorMessage=string,data=service.ShippingFeeTemplateDTO} "获取成功"
// @Failure 400 {object} object{success=bool,errorCode=int,errorMessage=string} "参数错误"
// @Failure 404 {object} object{success=bool,errorCode=int,errorMessage=string} "模板不存在"
// @Failure 500 {object} object{success=bool,errorCode=int,errorMessage=string} "内部服务器错误"
// @Router /api/v1/shipping-fee-templates/{id} [get]
func (h *ShippingFeeTemplateHandler) GetTemplateByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		h.logger.Error("模板ID格式错误", zap.String("id", idStr), zap.Error(err))
		util.ResponseError(c, 100002, "模板ID格式错误", http.StatusBadRequest)
		return
	}

	template, err := h.shippingFeeTemplateService.GetTemplateByID(c, id)
	if err != nil {
		h.logger.Error("获取运费模板失败", zap.Int64("id", id), zap.Error(err))
		if strings.Contains(err.Error(), "不存在") {
			util.ResponseError(c, 100006, err.Error(), http.StatusNotFound)
		} else {
			util.ResponseError(c, 100001, "获取运费模板失败: "+err.Error(), http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, template)
}

// GetUserConfiguredTemplates 根据用户ID获取其已配置的运费模板
// @Summary 根据用户ID获取其已配置的运费模板
// @Description 根据用户ID获取该用户已配置的所有运费模板，按类型排序
// @Tags 运费模板管理
// @Produce json
// @Param Authorization header string true "Bearer Token"
// @Param userId path int true "用户ID"
// @Success 200 {object} object{success=bool,errorCode=int,errorMessage=string,data=service.GetUserConfiguredTemplatesResponse} "获取成功"
// @Failure 400 {object} object{success=bool,errorCode=int,errorMessage=string} "参数错误"
// @Failure 500 {object} object{success=bool,errorCode=int,errorMessage=string} "内部服务器错误"
// @Router /api/v1/shipping-fee-templates/user/{userId} [get]
func (h *ShippingFeeTemplateHandler) GetUserConfiguredTemplates(c *gin.Context) {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		h.logger.Error("用户ID格式错误", zap.String("userId", userIDStr), zap.Error(err))
		util.ResponseError(c, 100002, "用户ID格式错误", http.StatusBadRequest)
		return
	}

	response, err := h.shippingFeeTemplateService.GetUserConfiguredTemplates(c, userID)
	if err != nil {
		h.logger.Error("获取用户已配置的运费模板失败", zap.Int64("userId", userID), zap.Error(err))
		util.ResponseError(c, 100001, "获取用户已配置的运费模板失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	util.ResponseSuccess(c, response)
}

// UpdateUserTemplateConfigurations 更新用户的模板配置
// @Summary 更新用户的模板配置
// @Description 批量更新用户的运费模板配置，支持同时配置多个类型的模板
// @Tags 运费模板管理
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Token"
// @Param userId path int true "用户ID"
// @Param body body service.UpdateUserTemplateConfigurationsRequest true "更新模板配置请求"
// @Success 200 {object} object{success=bool,errorCode=int,errorMessage=string,data=service.GetUserConfiguredTemplatesResponse} "更新成功"
// @Failure 400 {object} object{success=bool,errorCode=int,errorMessage=string} "参数错误"
// @Failure 404 {object} object{success=bool,errorCode=int,errorMessage=string} "模板不存在"
// @Failure 500 {object} object{success=bool,errorCode=int,errorMessage=string} "内部服务器错误"
// @Router /api/v1/shipping-fee-templates/user/{userId}/configurations [put]
func (h *ShippingFeeTemplateHandler) UpdateUserTemplateConfigurations(c *gin.Context) {
	userIDStr := c.Param("userId")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		h.logger.Error("用户ID格式错误", zap.String("userId", userIDStr), zap.Error(err))
		util.ResponseError(c, 100002, "用户ID格式错误", http.StatusBadRequest)
		return
	}

	var req service.UpdateUserTemplateConfigurationsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("更新用户模板配置参数绑定失败", zap.Error(err))
		util.ResponseError(c, 100002, "参数格式错误: "+err.Error(), http.StatusBadRequest)
		return
	}

	response, err := h.shippingFeeTemplateService.UpdateUserTemplateConfigurations(c, userID, &req)
	if err != nil {
		h.logger.Error("更新用户模板配置失败", zap.Int64("userId", userID), zap.Error(err))
		if strings.Contains(err.Error(), "不存在") {
			util.ResponseError(c, 100006, err.Error(), http.StatusNotFound)
		} else if strings.Contains(err.Error(), "无效") || strings.Contains(err.Error(), "重复") {
			util.ResponseError(c, 100002, err.Error(), http.StatusBadRequest)
		} else {
			util.ResponseError(c, 100001, "更新用户模板配置失败: "+err.Error(), http.StatusInternalServerError)
		}
		return
	}

	util.ResponseSuccess(c, response)
} 